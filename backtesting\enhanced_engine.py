"""
Enhanced Backtesting Engine with Statistical Analysis
Comprehensive backtesting framework with performance metrics and validation
"""

import os
import json
import logging
import datetime
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

# Set up logging
logger = logging.getLogger("enhanced_backtesting")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)
os.makedirs("reports/backtesting", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/enhanced_backtesting.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addH<PERSON><PERSON>(handler)


@dataclass
class Trade:
    """Data class for individual trades"""
    entry_time: datetime.datetime
    exit_time: Optional[datetime.datetime]
    symbol: str
    side: str  # 'buy' or 'sell'
    entry_price: float
    exit_price: Optional[float]
    quantity: float
    pnl: Optional[float]
    pnl_percent: Optional[float]
    fees: float
    reason: str  # Entry/exit reason
    market_regime: Optional[str]
    confidence: float


@dataclass
class BacktestResults:
    """Data class for backtest results"""
    start_date: datetime.datetime
    end_date: datetime.datetime
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    total_pnl: float
    total_pnl_percent: float
    max_drawdown: float
    max_drawdown_percent: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    profit_factor: float
    average_win: float
    average_loss: float
    largest_win: float
    largest_loss: float
    consecutive_wins: int
    consecutive_losses: int
    trades: List[Trade]


class EnhancedBacktestEngine:
    """
    Enhanced backtesting engine with comprehensive analysis
    """
    
    def __init__(self, initial_balance=10000, commission_rate=0.001):
        """
        Initialize backtesting engine
        
        Args:
            initial_balance (float): Starting balance
            commission_rate (float): Commission rate per trade
        """
        self.initial_balance = initial_balance
        self.commission_rate = commission_rate
        
        # Trading state
        self.current_balance = initial_balance
        self.equity_curve = []
        self.trades = []
        self.open_positions = {}
        
        # Performance tracking
        self.daily_returns = []
        self.drawdown_periods = []
        
        # Market regime tracking
        self.regime_performance = {}
    
    def run_backtest(self, historical_data, strategy_func, start_date=None, end_date=None):
        """
        Run comprehensive backtest
        
        Args:
            historical_data (pd.DataFrame): Historical market data
            strategy_func (callable): Strategy function that returns signals
            start_date (datetime): Start date for backtest
            end_date (datetime): End date for backtest
        
        Returns:
            BacktestResults: Comprehensive backtest results
        """
        try:
            logger.info("Starting enhanced backtest")
            
            # Filter data by date range
            if start_date:
                historical_data = historical_data[historical_data.index >= start_date]
            if end_date:
                historical_data = historical_data[historical_data.index <= end_date]
            
            # Reset state
            self._reset_state()
            
            # Process each data point
            for i, (timestamp, row) in enumerate(historical_data.iterrows()):
                # Get market data up to current point
                current_data = historical_data.iloc[:i+1]
                
                # Generate strategy signals
                signals = strategy_func(current_data)
                
                # Process signals
                self._process_signals(signals, timestamp, row)
                
                # Update equity curve
                self._update_equity_curve(timestamp, row)
                
                # Log progress
                if i % 1000 == 0:
                    logger.info(f"Processed {i}/{len(historical_data)} data points")
            
            # Close any remaining open positions
            self._close_all_positions(historical_data.iloc[-1])
            
            # Calculate results
            results = self._calculate_results(
                historical_data.index[0] if len(historical_data) > 0 else datetime.datetime.now(),
                historical_data.index[-1] if len(historical_data) > 0 else datetime.datetime.now()
            )
            
            logger.info(f"Backtest completed. Total trades: {results.total_trades}, Win rate: {results.win_rate:.2%}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in backtest: {e}")
            raise
    
    def _reset_state(self):
        """Reset backtesting state"""
        self.current_balance = self.initial_balance
        self.equity_curve = []
        self.trades = []
        self.open_positions = {}
        self.daily_returns = []
        self.drawdown_periods = []
        self.regime_performance = {}
    
    def _process_signals(self, signals, timestamp, market_data):
        """Process strategy signals"""
        if not signals:
            return
        
        for signal in signals if isinstance(signals, list) else [signals]:
            if signal.get('action') == 'buy':
                self._open_position(signal, timestamp, market_data, 'buy')
            elif signal.get('action') == 'sell':
                self._open_position(signal, timestamp, market_data, 'sell')
            elif signal.get('action') == 'close':
                self._close_position(signal.get('symbol'), timestamp, market_data, signal.get('reason', 'signal'))
    
    def _open_position(self, signal, timestamp, market_data, side):
        """Open a new position"""
        try:
            symbol = signal.get('symbol', 'DEFAULT')
            price = market_data.get('close', market_data.get('price', 0))
            confidence = signal.get('confidence', 0.5)
            
            # Calculate position size based on risk management
            position_size = self._calculate_position_size(signal, price)
            
            if position_size <= 0:
                return
            
            # Calculate fees
            fees = position_size * price * self.commission_rate
            
            # Check if we have enough balance
            required_balance = position_size * price + fees
            if required_balance > self.current_balance:
                logger.warning(f"Insufficient balance for trade: required={required_balance}, available={self.current_balance}")
                return
            
            # Create trade
            trade = Trade(
                entry_time=timestamp,
                exit_time=None,
                symbol=symbol,
                side=side,
                entry_price=price,
                exit_price=None,
                quantity=position_size,
                pnl=None,
                pnl_percent=None,
                fees=fees,
                reason=signal.get('reason', 'strategy_signal'),
                market_regime=signal.get('market_regime'),
                confidence=confidence
            )
            
            # Update balance
            self.current_balance -= required_balance
            
            # Store open position
            self.open_positions[symbol] = trade
            
            logger.debug(f"Opened {side} position: {symbol} @ {price:.4f}, size={position_size:.4f}")
            
        except Exception as e:
            logger.error(f"Error opening position: {e}")
    
    def _close_position(self, symbol, timestamp, market_data, reason):
        """Close an open position"""
        try:
            if symbol not in self.open_positions:
                return
            
            trade = self.open_positions[symbol]
            exit_price = market_data.get('close', market_data.get('price', 0))
            
            # Calculate P&L
            if trade.side == 'buy':
                pnl = (exit_price - trade.entry_price) * trade.quantity
            else:  # sell
                pnl = (trade.entry_price - exit_price) * trade.quantity
            
            # Subtract exit fees
            exit_fees = trade.quantity * exit_price * self.commission_rate
            pnl -= exit_fees
            
            # Calculate percentage P&L
            pnl_percent = pnl / (trade.entry_price * trade.quantity) * 100
            
            # Update trade
            trade.exit_time = timestamp
            trade.exit_price = exit_price
            trade.pnl = pnl
            trade.pnl_percent = pnl_percent
            trade.fees += exit_fees
            trade.reason += f" -> {reason}"
            
            # Update balance
            self.current_balance += (trade.quantity * exit_price) + pnl
            
            # Add to completed trades
            self.trades.append(trade)
            
            # Remove from open positions
            del self.open_positions[symbol]
            
            # Track regime performance
            if trade.market_regime:
                if trade.market_regime not in self.regime_performance:
                    self.regime_performance[trade.market_regime] = {'trades': 0, 'pnl': 0, 'wins': 0}
                
                self.regime_performance[trade.market_regime]['trades'] += 1
                self.regime_performance[trade.market_regime]['pnl'] += pnl
                if pnl > 0:
                    self.regime_performance[trade.market_regime]['wins'] += 1
            
            logger.debug(f"Closed position: {symbol} @ {exit_price:.4f}, P&L={pnl:.2f} ({pnl_percent:.2f}%)")
            
        except Exception as e:
            logger.error(f"Error closing position: {e}")
    
    def _calculate_position_size(self, signal, price):
        """Calculate position size based on risk management"""
        # Simple position sizing - can be enhanced
        risk_percent = signal.get('risk_percent', 2.0) / 100  # Default 2% risk
        max_position_value = self.current_balance * risk_percent
        
        return max_position_value / price
    
    def _update_equity_curve(self, timestamp, market_data):
        """Update equity curve"""
        # Calculate current portfolio value
        portfolio_value = self.current_balance
        
        # Add value of open positions
        for trade in self.open_positions.values():
            current_price = market_data.get('close', market_data.get('price', 0))
            if trade.side == 'buy':
                position_value = trade.quantity * current_price
            else:  # sell
                position_value = trade.quantity * (2 * trade.entry_price - current_price)
            
            portfolio_value += position_value
        
        self.equity_curve.append({
            'timestamp': timestamp,
            'equity': portfolio_value,
            'balance': self.current_balance,
            'open_positions': len(self.open_positions)
        })
    
    def _close_all_positions(self, final_market_data):
        """Close all remaining open positions"""
        for symbol in list(self.open_positions.keys()):
            self._close_position(symbol, final_market_data.name, final_market_data, 'backtest_end')
    
    def _calculate_results(self, start_date, end_date):
        """Calculate comprehensive backtest results"""
        try:
            if not self.trades:
                return BacktestResults(
                    start_date=start_date,
                    end_date=end_date,
                    total_trades=0,
                    winning_trades=0,
                    losing_trades=0,
                    win_rate=0.0,
                    total_pnl=0.0,
                    total_pnl_percent=0.0,
                    max_drawdown=0.0,
                    max_drawdown_percent=0.0,
                    sharpe_ratio=0.0,
                    sortino_ratio=0.0,
                    calmar_ratio=0.0,
                    profit_factor=0.0,
                    average_win=0.0,
                    average_loss=0.0,
                    largest_win=0.0,
                    largest_loss=0.0,
                    consecutive_wins=0,
                    consecutive_losses=0,
                    trades=[]
                )
            
            # Basic statistics
            total_trades = len(self.trades)
            winning_trades = sum(1 for trade in self.trades if trade.pnl > 0)
            losing_trades = sum(1 for trade in self.trades if trade.pnl < 0)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # P&L calculations
            total_pnl = sum(trade.pnl for trade in self.trades)
            total_pnl_percent = (self.current_balance - self.initial_balance) / self.initial_balance * 100
            
            # Win/Loss statistics
            wins = [trade.pnl for trade in self.trades if trade.pnl > 0]
            losses = [trade.pnl for trade in self.trades if trade.pnl < 0]
            
            average_win = np.mean(wins) if wins else 0
            average_loss = np.mean(losses) if losses else 0
            largest_win = max(wins) if wins else 0
            largest_loss = min(losses) if losses else 0
            
            # Profit factor
            gross_profit = sum(wins) if wins else 0
            gross_loss = abs(sum(losses)) if losses else 0
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
            
            # Drawdown calculation
            equity_values = [point['equity'] for point in self.equity_curve]
            max_drawdown, max_drawdown_percent = self._calculate_drawdown(equity_values)
            
            # Risk-adjusted returns
            returns = self._calculate_returns()
            sharpe_ratio = self._calculate_sharpe_ratio(returns)
            sortino_ratio = self._calculate_sortino_ratio(returns)
            calmar_ratio = total_pnl_percent / max_drawdown_percent if max_drawdown_percent > 0 else 0
            
            # Consecutive wins/losses
            consecutive_wins, consecutive_losses = self._calculate_consecutive_stats()
            
            return BacktestResults(
                start_date=start_date,
                end_date=end_date,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                win_rate=win_rate,
                total_pnl=total_pnl,
                total_pnl_percent=total_pnl_percent,
                max_drawdown=max_drawdown,
                max_drawdown_percent=max_drawdown_percent,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                profit_factor=profit_factor,
                average_win=average_win,
                average_loss=average_loss,
                largest_win=largest_win,
                largest_loss=largest_loss,
                consecutive_wins=consecutive_wins,
                consecutive_losses=consecutive_losses,
                trades=self.trades
            )
            
        except Exception as e:
            logger.error(f"Error calculating results: {e}")
            raise
    
    def _calculate_drawdown(self, equity_values):
        """Calculate maximum drawdown"""
        if not equity_values:
            return 0, 0
        
        peak = equity_values[0]
        max_dd = 0
        max_dd_percent = 0
        
        for value in equity_values:
            if value > peak:
                peak = value
            
            drawdown = peak - value
            drawdown_percent = drawdown / peak * 100 if peak > 0 else 0
            
            if drawdown > max_dd:
                max_dd = drawdown
            if drawdown_percent > max_dd_percent:
                max_dd_percent = drawdown_percent
        
        return max_dd, max_dd_percent
    
    def _calculate_returns(self):
        """Calculate daily returns"""
        if len(self.equity_curve) < 2:
            return []
        
        returns = []
        for i in range(1, len(self.equity_curve)):
            prev_equity = self.equity_curve[i-1]['equity']
            curr_equity = self.equity_curve[i]['equity']
            
            if prev_equity > 0:
                daily_return = (curr_equity - prev_equity) / prev_equity
                returns.append(daily_return)
        
        return returns
    
    def _calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """Calculate Sharpe ratio"""
        if not returns:
            return 0
        
        excess_returns = np.array(returns) - risk_free_rate / 252  # Daily risk-free rate
        
        if np.std(excess_returns) == 0:
            return 0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
    
    def _calculate_sortino_ratio(self, returns, risk_free_rate=0.02):
        """Calculate Sortino ratio"""
        if not returns:
            return 0
        
        excess_returns = np.array(returns) - risk_free_rate / 252
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0 or np.std(downside_returns) == 0:
            return 0
        
        return np.mean(excess_returns) / np.std(downside_returns) * np.sqrt(252)
    
    def _calculate_consecutive_stats(self):
        """Calculate consecutive wins and losses"""
        if not self.trades:
            return 0, 0
        
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_wins = 0
        current_losses = 0
        
        for trade in self.trades:
            if trade.pnl > 0:
                current_wins += 1
                current_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, current_wins)
            elif trade.pnl < 0:
                current_losses += 1
                current_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, current_losses)
            else:
                current_wins = 0
                current_losses = 0
        
        return max_consecutive_wins, max_consecutive_losses
    
    def generate_report(self, results, output_path="reports/backtesting"):
        """Generate comprehensive backtest report"""
        try:
            # Create output directory
            os.makedirs(output_path, exist_ok=True)
            
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Generate text report
            report_file = os.path.join(output_path, f"backtest_report_{timestamp}.txt")
            self._generate_text_report(results, report_file)
            
            # Generate JSON report
            json_file = os.path.join(output_path, f"backtest_data_{timestamp}.json")
            self._generate_json_report(results, json_file)
            
            # Generate charts
            chart_file = os.path.join(output_path, f"backtest_charts_{timestamp}.png")
            self._generate_charts(results, chart_file)
            
            logger.info(f"Backtest report generated: {report_file}")
            
            return {
                'text_report': report_file,
                'json_report': json_file,
                'charts': chart_file
            }
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return None
    
    def _generate_text_report(self, results, file_path):
        """Generate text report"""
        with open(file_path, 'w') as f:
            f.write("=" * 60 + "\n")
            f.write("ENHANCED BACKTESTING REPORT\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"Period: {results.start_date} to {results.end_date}\n")
            f.write(f"Initial Balance: ${self.initial_balance:,.2f}\n")
            f.write(f"Final Balance: ${self.current_balance:,.2f}\n")
            f.write(f"Total Return: {results.total_pnl_percent:.2f}%\n\n")
            
            f.write("TRADE STATISTICS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total Trades: {results.total_trades}\n")
            f.write(f"Winning Trades: {results.winning_trades}\n")
            f.write(f"Losing Trades: {results.losing_trades}\n")
            f.write(f"Win Rate: {results.win_rate:.2%}\n")
            f.write(f"Profit Factor: {results.profit_factor:.2f}\n\n")
            
            f.write("RISK METRICS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Maximum Drawdown: {results.max_drawdown_percent:.2f}%\n")
            f.write(f"Sharpe Ratio: {results.sharpe_ratio:.2f}\n")
            f.write(f"Sortino Ratio: {results.sortino_ratio:.2f}\n")
            f.write(f"Calmar Ratio: {results.calmar_ratio:.2f}\n\n")
            
            f.write("PERFORMANCE DETAILS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Average Win: ${results.average_win:.2f}\n")
            f.write(f"Average Loss: ${results.average_loss:.2f}\n")
            f.write(f"Largest Win: ${results.largest_win:.2f}\n")
            f.write(f"Largest Loss: ${results.largest_loss:.2f}\n")
            f.write(f"Max Consecutive Wins: {results.consecutive_wins}\n")
            f.write(f"Max Consecutive Losses: {results.consecutive_losses}\n\n")
            
            # Market regime performance
            if self.regime_performance:
                f.write("MARKET REGIME PERFORMANCE:\n")
                f.write("-" * 30 + "\n")
                for regime, perf in self.regime_performance.items():
                    win_rate = perf['wins'] / perf['trades'] if perf['trades'] > 0 else 0
                    f.write(f"{regime.upper()}: {perf['trades']} trades, "
                           f"${perf['pnl']:.2f} P&L, {win_rate:.2%} win rate\n")
    
    def _generate_json_report(self, results, file_path):
        """Generate JSON report"""
        report_data = {
            'backtest_results': asdict(results),
            'equity_curve': self.equity_curve,
            'regime_performance': self.regime_performance,
            'settings': {
                'initial_balance': self.initial_balance,
                'commission_rate': self.commission_rate
            }
        }
        
        # Convert datetime objects to strings for JSON serialization
        def convert_datetime(obj):
            if isinstance(obj, datetime.datetime):
                return obj.isoformat()
            return obj
        
        with open(file_path, 'w') as f:
            json.dump(report_data, f, indent=2, default=convert_datetime)
    
    def _generate_charts(self, results, file_path):
        """Generate performance charts"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # Equity curve
            if self.equity_curve:
                timestamps = [point['timestamp'] for point in self.equity_curve]
                equity_values = [point['equity'] for point in self.equity_curve]
                
                axes[0, 0].plot(timestamps, equity_values)
                axes[0, 0].set_title('Equity Curve')
                axes[0, 0].set_ylabel('Portfolio Value ($)')
                axes[0, 0].grid(True)
            
            # P&L distribution
            if results.trades:
                pnl_values = [trade.pnl for trade in results.trades]
                axes[0, 1].hist(pnl_values, bins=20, alpha=0.7)
                axes[0, 1].set_title('P&L Distribution')
                axes[0, 1].set_xlabel('P&L ($)')
                axes[0, 1].set_ylabel('Frequency')
                axes[0, 1].grid(True)
            
            # Monthly returns
            if len(self.equity_curve) > 30:
                monthly_returns = self._calculate_monthly_returns()
                if monthly_returns:
                    months = list(monthly_returns.keys())
                    returns = list(monthly_returns.values())
                    
                    axes[1, 0].bar(range(len(months)), returns)
                    axes[1, 0].set_title('Monthly Returns')
                    axes[1, 0].set_ylabel('Return (%)')
                    axes[1, 0].set_xticks(range(len(months)))
                    axes[1, 0].set_xticklabels([m.strftime('%Y-%m') for m in months], rotation=45)
                    axes[1, 0].grid(True)
            
            # Drawdown
            if self.equity_curve:
                equity_values = [point['equity'] for point in self.equity_curve]
                timestamps = [point['timestamp'] for point in self.equity_curve]
                
                peak = equity_values[0]
                drawdowns = []
                
                for value in equity_values:
                    if value > peak:
                        peak = value
                    drawdown = (peak - value) / peak * 100
                    drawdowns.append(drawdown)
                
                axes[1, 1].fill_between(timestamps, drawdowns, alpha=0.3, color='red')
                axes[1, 1].set_title('Drawdown')
                axes[1, 1].set_ylabel('Drawdown (%)')
                axes[1, 1].grid(True)
            
            plt.tight_layout()
            plt.savefig(file_path, dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.error(f"Error generating charts: {e}")
    
    def _calculate_monthly_returns(self):
        """Calculate monthly returns"""
        if len(self.equity_curve) < 2:
            return {}
        
        monthly_data = {}
        
        for point in self.equity_curve:
            month_key = point['timestamp'].replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            if month_key not in monthly_data:
                monthly_data[month_key] = {'start': point['equity'], 'end': point['equity']}
            else:
                monthly_data[month_key]['end'] = point['equity']
        
        monthly_returns = {}
        for month, data in monthly_data.items():
            if data['start'] > 0:
                return_pct = (data['end'] - data['start']) / data['start'] * 100
                monthly_returns[month] = return_pct
        
        return monthly_returns
