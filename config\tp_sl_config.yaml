# Take Profit / Stop Loss Configuration

# Market condition-based TP/SL settings
tp_sl_settings:
  stable_market:
    take_profit: 0.7  # 0.7% take profit in stable market
    stop_loss: 0.8    # 0.8% stop loss in stable market
  volatile_market:
    take_profit: 1.0  # 1.0% take profit in volatile market
    stop_loss: 1.2    # 1.2% stop loss in volatile market

# Market condition thresholds
market_condition_thresholds:
  atr_threshold: 1.2  # ATR threshold for volatile market
  roc_threshold: 1.5  # Rate of Change threshold for volatile market

# Currency-specific multipliers
currency_multipliers:
  BTC: 1.0    # Base multiplier
  ETH: 1.5    # ETH is 1.5x more volatile than BTC
  SOL: 3.0    # SOL is 3x more volatile than BTC
  BNB: 1.8    # BNB is 1.8x more volatile than BTC
  DEFAULT: 2.0 # Default multiplier for other currencies
