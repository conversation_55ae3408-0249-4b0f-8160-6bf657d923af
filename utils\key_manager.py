"""
Key Manager Module

This module provides secure storage and management of API keys
using strong encryption and key rotation.
"""

import os
import time
import logging
import datetime
import json
import base64
import getpass
import secrets
import hashlib
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Set up logging
logger = logging.getLogger("key_manager")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/key_manager.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)


class KeyManager:
    """
    Manages secure storage and retrieval of API keys
    """
    
    def __init__(self, keys_file="keys.enc", salt_file="salt.bin"):
        """
        Initialize the key manager
        
        Parameters:
        keys_file (str): Encrypted keys file
        salt_file (str): Salt file for key derivation
        """
        self.keys_file = Path(keys_file)
        self.salt_file = Path(salt_file)
        self.keys = {}
        self.fernet = None
        self.password_hash = None
        self.last_rotation = None
        self.rotation_interval = 30  # days
        
        # Create salt if it doesn't exist
        if not self.salt_file.exists():
            self._create_salt()
        
        logger.info("KeyManager initialized")
    
    def _create_salt(self):
        """
        Create a new salt for key derivation
        
        Returns:
        bytes: Salt
        """
        salt = secrets.token_bytes(16)
        with open(self.salt_file, 'wb') as f:
            f.write(salt)
        
        logger.info("New salt created")
        return salt
    
    def _get_salt(self):
        """
        Get the salt for key derivation
        
        Returns:
        bytes: Salt
        """
        with open(self.salt_file, 'rb') as f:
            return f.read()
    
    def _derive_key(self, password):
        """
        Derive encryption key from password
        
        Parameters:
        password (str): Password for encryption
        
        Returns:
        bytes: Derived key
        """
        salt = self._get_salt()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        
        # Store password hash for verification
        self.password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        return key
    
    def initialize(self, password=None):
        """
        Initialize the key manager with a password
        
        Parameters:
        password (str, optional): Password for encryption
        
        Returns:
        bool: True if initialized successfully, False otherwise
        """
        try:
            # Get password if not provided
            if password is None:
                password = getpass.getpass("Enter password for API key encryption: ")
            
            # Derive key
            key = self._derive_key(password)
            
            # Create Fernet cipher
            self.fernet = Fernet(key)
            
            # Load keys if file exists
            if self.keys_file.exists():
                self._load_keys(password)
            else:
                self.keys = {
                    "metadata": {
                        "created": datetime.datetime.now().isoformat(),
                        "last_rotation": None,
                        "rotation_interval": self.rotation_interval
                    },
                    "keys": {}
                }
                self._save_keys()
            
            logger.info("KeyManager initialized successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error initializing KeyManager: {e}")
            return False
    
    def _load_keys(self, password):
        """
        Load encrypted keys from file
        
        Parameters:
        password (str): Password for decryption
        
        Returns:
        bool: True if keys were loaded successfully, False otherwise
        """
        try:
            with open(self.keys_file, 'rb') as f:
                encrypted_data = f.read()
            
            # Decrypt data
            decrypted_data = self.fernet.decrypt(encrypted_data)
            
            # Parse JSON
            self.keys = json.loads(decrypted_data.decode())
            
            # Get last rotation date
            if "metadata" in self.keys and "last_rotation" in self.keys["metadata"]:
                last_rotation_str = self.keys["metadata"]["last_rotation"]
                if last_rotation_str:
                    self.last_rotation = datetime.datetime.fromisoformat(last_rotation_str)
            
            # Get rotation interval
            if "metadata" in self.keys and "rotation_interval" in self.keys["metadata"]:
                self.rotation_interval = self.keys["metadata"]["rotation_interval"]
            
            logger.info("Keys loaded successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error loading keys: {e}")
            return False
    
    def _save_keys(self):
        """
        Save encrypted keys to file
        
        Returns:
        bool: True if keys were saved successfully, False otherwise
        """
        try:
            # Update metadata
            self.keys["metadata"]["updated"] = datetime.datetime.now().isoformat()
            
            # Convert to JSON
            data = json.dumps(self.keys).encode()
            
            # Encrypt data
            encrypted_data = self.fernet.encrypt(data)
            
            # Save to file
            with open(self.keys_file, 'wb') as f:
                f.write(encrypted_data)
            
            logger.info("Keys saved successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error saving keys: {e}")
            return False
    
    def add_key(self, service, key_id, key_value, is_primary=True):
        """
        Add a new API key
        
        Parameters:
        service (str): Service name (e.g., 'binance', 'openai')
        key_id (str): Key identifier (e.g., 'api_key', 'secret_key')
        key_value (str): Key value
        is_primary (bool): Whether this is the primary key for the service
        
        Returns:
        bool: True if key was added successfully, False otherwise
        """
        try:
            # Initialize keys dictionary if needed
            if "keys" not in self.keys:
                self.keys["keys"] = {}
            
            # Initialize service dictionary if needed
            if service not in self.keys["keys"]:
                self.keys["keys"][service] = {}
            
            # Add key
            self.keys["keys"][service][key_id] = {
                "value": key_value,
                "added": datetime.datetime.now().isoformat(),
                "is_primary": is_primary
            }
            
            # Save keys
            self._save_keys()
            
            logger.info(f"Key '{key_id}' added for service '{service}'")
            return True
        
        except Exception as e:
            logger.error(f"Error adding key: {e}")
            return False
    
    def get_key(self, service, key_id):
        """
        Get an API key
        
        Parameters:
        service (str): Service name (e.g., 'binance', 'openai')
        key_id (str): Key identifier (e.g., 'api_key', 'secret_key')
        
        Returns:
        str: Key value or None if not found
        """
        try:
            # Check if key exists
            if (
                "keys" in self.keys and
                service in self.keys["keys"] and
                key_id in self.keys["keys"][service]
            ):
                return self.keys["keys"][service][key_id]["value"]
            
            logger.warning(f"Key '{key_id}' for service '{service}' not found")
            return None
        
        except Exception as e:
            logger.error(f"Error getting key: {e}")
            return None
    
    def get_primary_key(self, service, key_id):
        """
        Get the primary API key for a service
        
        Parameters:
        service (str): Service name (e.g., 'binance', 'openai')
        key_id (str): Key identifier (e.g., 'api_key', 'secret_key')
        
        Returns:
        str: Primary key value or None if not found
        """
        try:
            # Check if service exists
            if "keys" in self.keys and service in self.keys["keys"]:
                # Find primary key
                for k_id, k_data in self.keys["keys"][service].items():
                    if k_id == key_id and k_data.get("is_primary", False):
                        return k_data["value"]
            
            logger.warning(f"Primary key '{key_id}' for service '{service}' not found")
            return None
        
        except Exception as e:
            logger.error(f"Error getting primary key: {e}")
            return None
    
    def rotate_keys(self, password=None):
        """
        Rotate encryption keys
        
        Parameters:
        password (str, optional): New password for encryption
        
        Returns:
        bool: True if keys were rotated successfully, False otherwise
        """
        try:
            # Get new password if not provided
            if password is None:
                password = getpass.getpass("Enter new password for API key encryption: ")
            
            # Create backup of current keys
            backup_file = self.keys_file.with_suffix(".bak")
            if self.keys_file.exists():
                with open(self.keys_file, 'rb') as src, open(backup_file, 'wb') as dst:
                    dst.write(src.read())
            
            # Create new salt
            self._create_salt()
            
            # Derive new key
            key = self._derive_key(password)
            
            # Create new Fernet cipher
            self.fernet = Fernet(key)
            
            # Update rotation metadata
            self.last_rotation = datetime.datetime.now()
            self.keys["metadata"]["last_rotation"] = self.last_rotation.isoformat()
            
            # Save keys with new encryption
            self._save_keys()
            
            logger.info("Keys rotated successfully")
            return True
        
        except Exception as e:
            logger.error(f"Error rotating keys: {e}")
            return False
    
    def check_rotation_needed(self):
        """
        Check if key rotation is needed
        
        Returns:
        bool: True if rotation is needed, False otherwise
        """
        if self.last_rotation is None:
            return True
        
        days_since_rotation = (datetime.datetime.now() - self.last_rotation).days
        return days_since_rotation >= self.rotation_interval
    
    def verify_password(self, password):
        """
        Verify if the provided password is correct
        
        Parameters:
        password (str): Password to verify
        
        Returns:
        bool: True if password is correct, False otherwise
        """
        if self.password_hash is None:
            return False
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        return password_hash == self.password_hash
