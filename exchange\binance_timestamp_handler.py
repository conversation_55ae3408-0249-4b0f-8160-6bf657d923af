"""
Binance Timestamp Error Handler

This module provides utilities for handling timestamp errors in Binance API requests.
"""

import time
import logging
import requests
import hmac
import hashlib
from typing import Dict, Any, Optional, Tuple, Callable

from utils.time_sync import time_sync, get_adjusted_timestamp

# Configure logging
logger = logging.getLogger(__name__)

# Binance error codes
TIMESTAMP_ERROR_CODES = [
    -1021,  # INVALID_TIMESTAMP
    -1022   # SIGNATURE_NOT_VALID
]

class BinanceTimestampHandler:
    """
    Handler for Binance timestamp errors
    """
    
    def __init__(self, max_retries: int = 3, retry_delay: int = 1):
        """
        Initialize the timestamp error handler
        
        Args:
            max_retries (int): Maximum number of retries
            retry_delay (int): Delay between retries in seconds
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.server_time_diff = 0
        self.last_server_time_check = 0
        
        # Initialize time synchronization
        self._check_server_time()
        
        logger.info("BinanceTimestampHandler initialized")
    
    def _check_server_time(self) -> bool:
        """
        Check Binance server time and calculate time difference
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get server time
            response = requests.get("https://api.binance.com/api/v3/time", timeout=5)
            
            if response.status_code == 200:
                server_time = response.json().get("serverTime", 0)
                local_time = int(time.time() * 1000)
                
                # Calculate time difference
                self.server_time_diff = server_time - local_time
                self.last_server_time_check = local_time
                
                logger.info(f"Binance server time difference: {self.server_time_diff}ms")
                return True
            else:
                logger.warning(f"Failed to get Binance server time: Status code {response.status_code}")
                return False
        
        except Exception as e:
            logger.error(f"Error checking Binance server time: {e}")
            return False
    
    def get_timestamp(self) -> int:
        """
        Get timestamp adjusted for Binance server time
        
        Returns:
            int: Adjusted timestamp in milliseconds
        """
        # Check if we need to refresh server time
        if time.time() * 1000 - self.last_server_time_check > 3600000:  # 1 hour
            self._check_server_time()
        
        # Get NTP-adjusted timestamp
        ntp_timestamp = get_adjusted_timestamp(ms=True)
        
        # Apply Binance-specific adjustment
        return ntp_timestamp + self.server_time_diff
    
    def prepare_request_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare request parameters with adjusted timestamp
        
        Args:
            params (Dict[str, Any]): Request parameters
            
        Returns:
            Dict[str, Any]: Adjusted parameters
        """
        # Create a copy of the parameters
        adjusted_params = params.copy()
        
        # Add or update timestamp
        adjusted_params['timestamp'] = self.get_timestamp()
        
        return adjusted_params
    
    def sign_request(self, params: Dict[str, Any], api_secret: str) -> str:
        """
        Sign request parameters with API secret
        
        Args:
            params (Dict[str, Any]): Request parameters
            api_secret (str): API secret key
            
        Returns:
            str: Request signature
        """
        # Convert parameters to query string
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        
        # Create signature
        signature = hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def handle_request_with_retry(self, request_func: Callable, params: Dict[str, Any], api_secret: str) -> Tuple[bool, Any]:
        """
        Handle request with retry for timestamp errors
        
        Args:
            request_func (Callable): Function to make the request
            params (Dict[str, Any]): Request parameters
            api_secret (str): API secret key
            
        Returns:
            Tuple[bool, Any]: (Success, Response)
        """
        for attempt in range(self.max_retries):
            try:
                # Prepare parameters with timestamp
                adjusted_params = self.prepare_request_params(params)
                
                # Add signature
                signature = self.sign_request(adjusted_params, api_secret)
                adjusted_params['signature'] = signature
                
                # Make request
                response = request_func(adjusted_params)
                
                # Check for timestamp error
                if isinstance(response, dict) and 'code' in response and response['code'] in TIMESTAMP_ERROR_CODES:
                    # Refresh server time
                    self._check_server_time()
                    
                    # Wait before retry
                    time.sleep(self.retry_delay * (2 ** attempt))
                    
                    logger.warning(f"Timestamp error (attempt {attempt+1}/{self.max_retries}): {response}")
                    continue
                
                # Success
                return True, response
            
            except Exception as e:
                logger.error(f"Error in request (attempt {attempt+1}/{self.max_retries}): {e}")
                
                # Wait before retry
                time.sleep(self.retry_delay * (2 ** attempt))
        
        # All attempts failed
        return False, None

# Global instance
timestamp_handler = BinanceTimestampHandler()

def get_binance_timestamp() -> int:
    """
    Get timestamp adjusted for Binance server time
    
    Returns:
        int: Adjusted timestamp in milliseconds
    """
    return timestamp_handler.get_timestamp()

def prepare_binance_request(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare request parameters with adjusted timestamp
    
    Args:
        params (Dict[str, Any]): Request parameters
        
    Returns:
        Dict[str, Any]: Adjusted parameters
    """
    return timestamp_handler.prepare_request_params(params)

def sign_binance_request(params: Dict[str, Any], api_secret: str) -> str:
    """
    Sign request parameters with API secret
    
    Args:
        params (Dict[str, Any]): Request parameters
        api_secret (str): API secret key
        
    Returns:
        str: Request signature
    """
    return timestamp_handler.sign_request(params, api_secret)

def handle_binance_request(request_func: Callable, params: Dict[str, Any], api_secret: str) -> Tuple[bool, Any]:
    """
    Handle Binance request with retry for timestamp errors
    
    Args:
        request_func (Callable): Function to make the request
        params (Dict[str, Any]): Request parameters
        api_secret (str): API secret key
        
    Returns:
        Tuple[bool, Any]: (Success, Response)
    """
    return timestamp_handler.handle_request_with_retry(request_func, params, api_secret)
