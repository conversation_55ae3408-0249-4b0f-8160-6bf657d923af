#!/usr/bin/env python3
"""
SP.Bot Enhanced v2.0.0 - Main Entry Point
Advanced AI-Powered Trading Bot with ML Market Detection and Enhanced Features
"""

import os
import sys
import argparse
import asyncio
import logging
import datetime
import signal
import yaml
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import enhanced components
from analysis.indicators import MLMarketClassifier
from ai_services.enhanced_debate_mode import EnhancedDebateMode
from utils.smart_reconnection import BinanceSmartReconnection
from strategies.enhanced_trading_hours import EnhancedTradingHours
from backtesting.enhanced_engine import EnhancedBacktestEngine
from security.key_manager import KeyManager
from utils.memory_guard import MemoryGuard

# Import existing components
from main import TradingBot
from exchange.binance_api import BinanceAPI
from ai_services.market_analyzer import MarketAnalyzer
from strategies.strategy import TradingStrategy
from risk_management.risk_controller import RiskController
from utils.logger import setup_logging

# Set up logging
logger = logging.getLogger("enhanced_main")


class EnhancedTradingBot:
    """
    Enhanced Trading Bot with advanced AI, ML market detection, and comprehensive features
    """

    def __init__(self, config_path="config/enhanced_config.yaml", mode="paper"):
        """
        Initialize Enhanced Trading Bot

        Args:
            config_path (str): Path to configuration file
            mode (str): Trading mode ('live', 'paper', 'backtest')
        """
        self.config_path = config_path
        self.mode = mode
        self.config = {}
        self.running = False

        # Enhanced components
        self.ml_classifier = None
        self.enhanced_debate_mode = None
        self.smart_reconnection = None
        self.enhanced_trading_hours = None
        self.memory_guard = None

        # Core components
        self.binance_api = None
        self.market_analyzer = None
        self.trading_strategy = None
        self.risk_controller = None
        self.key_manager = None

        # Performance tracking
        self.start_time = None
        self.trade_count = 0
        self.session_pnl = 0.0

        # Load configuration
        self.load_configuration()

        # Initialize components
        self.initialize_components()

    def load_configuration(self):
        """Load enhanced configuration"""
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)

            logger.info(f"Configuration loaded from {self.config_path}")

            # Validate required sections
            required_sections = [
                'trading_pairs', 'ai_models', 'risk_management',
                'indicators', 'ml_market_detection', 'enhanced_debate_mode'
            ]

            for section in required_sections:
                if section not in self.config:
                    logger.warning(f"Missing configuration section: {section}")
                    self.config[section] = {}

        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            # Load default configuration
            self.config = self.get_default_config()

    def get_default_config(self):
        """Get default configuration if file loading fails"""
        return {
            'trading_pairs': {'BTC/USDT': {'enabled': True}},
            'ai_models': {
                'openai': {'weight': 0.30},
                'deepseek': {'weight': 0.35},
                'qwen': {'weight': 0.35}
            },
            'risk_management': {
                'normal_account': {'risk_percent': 2.0},
                'small_account': {'risk_percent': 1.0}
            },
            'ml_market_detection': {'enabled': True},
            'enhanced_debate_mode': {'enabled': True},
            'smart_reconnection': {'enabled': True},
            'enhanced_trading_hours': {'enabled': True}
        }

    def initialize_components(self):
        """Initialize all enhanced components"""
        try:
            logger.info("Initializing enhanced components...")

            # Initialize memory guard first
            if self.config.get('resources', {}).get('memory_monitoring', True):
                self.memory_guard = MemoryGuard()
                self.memory_guard.start_monitoring()

            # Initialize key manager
            self.key_manager = KeyManager()

            # Initialize ML market classifier
            if self.config.get('ml_market_detection', {}).get('enabled', True):
                self.ml_classifier = MLMarketClassifier(
                    model_path=self.config.get('ml_market_detection', {}).get(
                        'model_path', 'data/ml_models/market_classifier.joblib'
                    )
                )
                logger.info("ML Market Classifier initialized")

            # Initialize enhanced debate mode
            if self.config.get('enhanced_debate_mode', {}).get('enabled', True):
                base_weights = {
                    model: config['weight']
                    for model, config in self.config.get('ai_models', {}).items()
                }
                self.enhanced_debate_mode = EnhancedDebateMode(base_weights=base_weights)
                logger.info("Enhanced Debate Mode initialized")

            # Initialize enhanced trading hours
            if self.config.get('enhanced_trading_hours', {}).get('enabled', True):
                self.enhanced_trading_hours = EnhancedTradingHours()
                logger.info("Enhanced Trading Hours initialized")

            # Initialize Binance API
            self.binance_api = BinanceAPI()

            # Initialize smart reconnection
            if self.config.get('smart_reconnection', {}).get('enabled', True):
                self.smart_reconnection = BinanceSmartReconnection(
                    api_client=self.binance_api,
                    max_retries=self.config.get('smart_reconnection', {}).get('max_retries', 10)
                )

                # Set up callbacks
                self.smart_reconnection.set_callbacks(
                    on_connect=self.on_api_connect,
                    on_disconnect=self.on_api_disconnect,
                    on_retry=self.on_api_retry
                )
                logger.info("Smart Reconnection System initialized")

            # Initialize other core components
            self.market_analyzer = MarketAnalyzer()
            self.trading_strategy = TradingStrategy()
            self.risk_controller = RiskController()

            logger.info("All components initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing components: {e}")
            raise

    async def start(self):
        """Start the enhanced trading bot"""
        try:
            logger.info(f"Starting SP.Bot Enhanced v2.0.0 in {self.mode} mode")
            self.start_time = datetime.datetime.now()
            self.running = True

            # Connect to Binance API
            if self.smart_reconnection:
                success = self.smart_reconnection.connect_with_retry(
                    self.binance_api.connect
                )
                if not success:
                    logger.error("Failed to connect to Binance API")
                    return False
            else:
                if not self.binance_api.connect():
                    logger.error("Failed to connect to Binance API")
                    return False

            # Validate account and permissions
            if not await self.validate_account():
                logger.error("Account validation failed")
                return False

            # Start main trading loop
            await self.main_trading_loop()

        except KeyboardInterrupt:
            logger.info("Received shutdown signal")
        except Exception as e:
            logger.error(f"Error in main execution: {e}")
        finally:
            await self.shutdown()

    async def validate_account(self):
        """Validate account balance and permissions"""
        try:
            # Check account balance
            balance = self.binance_api.get_account_balance()
            if not balance or balance.get('USDT', 0) < 7:
                logger.error(f"Insufficient USDT balance: {balance.get('USDT', 0)}")
                return False

            # Check API permissions
            account_info = self.binance_api.get_account_info()
            if not account_info:
                logger.error("Failed to get account information")
                return False

            permissions = account_info.get('permissions', [])
            required_permissions = ['SPOT', 'MARGIN']

            for perm in required_permissions:
                if perm not in permissions:
                    logger.warning(f"Missing permission: {perm}")

            logger.info(f"Account validated. USDT Balance: {balance.get('USDT', 0):.2f}")
            return True

        except Exception as e:
            logger.error(f"Error validating account: {e}")
            return False

    async def main_trading_loop(self):
        """Main enhanced trading loop"""
        logger.info("Starting main trading loop")

        while self.running:
            try:
                # Check if trading is allowed
                if self.enhanced_trading_hours:
                    trading_status = self.enhanced_trading_hours.is_trading_allowed()
                    if not trading_status['allowed']:
                        logger.info(f"Trading not allowed: {trading_status['reason']}")
                        await asyncio.sleep(60)  # Check again in 1 minute
                        continue

                # Process each enabled trading pair
                for symbol, pair_config in self.config.get('trading_pairs', {}).items():
                    if not pair_config.get('enabled', True):
                        continue

                    try:
                        await self.process_trading_pair(symbol)
                    except Exception as e:
                        logger.error(f"Error processing {symbol}: {e}")

                # Memory cleanup
                if self.memory_guard:
                    self.memory_guard.cleanup_if_needed()

                # Wait before next iteration
                await asyncio.sleep(60)  # 1-minute intervals

            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")
                await asyncio.sleep(30)  # Wait before retrying

    async def process_trading_pair(self, symbol):
        """Process a single trading pair"""
        try:
            # Get market data
            market_data = self.binance_api.get_market_data(symbol)
            if not market_data:
                logger.warning(f"No market data for {symbol}")
                return

            # Classify market regime using ML
            market_regime = None
            if self.ml_classifier:
                regime_prediction = self.ml_classifier.predict_market_regime(market_data)
                market_regime = regime_prediction.get('regime', 'unclear')
                logger.debug(f"{symbol} market regime: {market_regime} (confidence: {regime_prediction.get('confidence', 0):.2f})")

            # Get AI signals
            ai_signals = await self.market_analyzer.get_ai_signals(market_data, symbol)

            # Enhanced debate mode
            if self.enhanced_debate_mode and len(ai_signals) > 1:
                debate_result = self.enhanced_debate_mode.conduct_debate(ai_signals, market_data)
                final_decision = debate_result.get('decision', 'hold')
                confidence = debate_result.get('confidence', 0.5)
            else:
                # Fallback to simple majority
                final_decision = 'hold'
                confidence = 0.5

            # Generate trading signals
            trading_signals = self.trading_strategy.generate_signals(
                market_data, final_decision, confidence, market_regime
            )

            # Execute trades if signals are strong enough
            if trading_signals and confidence >= 0.55:
                await self.execute_trades(symbol, trading_signals, market_regime)

        except Exception as e:
            logger.error(f"Error processing trading pair {symbol}: {e}")

    def on_api_connect(self):
        """Callback for successful API connection"""
        logger.info("Successfully connected to Binance API")

    def on_api_disconnect(self, reason):
        """Callback for API disconnection"""
        logger.warning(f"Disconnected from Binance API: {reason}")

    def on_api_retry(self, attempt, error):
        """Callback for API retry attempts"""
        logger.info(f"API connection retry {attempt}: {error}")

    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("Shutting down SP.Bot Enhanced...")
        self.running = False

        try:
            # Stop memory monitoring
            if self.memory_guard:
                self.memory_guard.stop_monitoring()

            # Stop smart reconnection health monitoring
            if self.smart_reconnection:
                self.smart_reconnection.stop_health_monitoring()

            # Save performance data
            if self.enhanced_debate_mode:
                self.enhanced_debate_mode.save_performance_data()

            logger.info("Shutdown completed successfully")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}")
    # The main loop will handle the actual shutdown


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='SP.Bot Enhanced v2.0.0')
    parser.add_argument('--mode', choices=['live', 'paper', 'backtest'],
                       default='paper', help='Trading mode')
    parser.add_argument('--config', default='config/enhanced_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='Logging level')

    args = parser.parse_args()

    # Set up logging
    setup_logging(level=args.log_level)

    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Create and start the enhanced trading bot
    bot = EnhancedTradingBot(config_path=args.config, mode=args.mode)
    await bot.start()


if __name__ == "__main__":
    asyncio.run(main())
