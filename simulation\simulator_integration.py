"""
Simulator Integration Module

This module provides utilities to integrate the paper trading simulator
with the main trading bot.
"""

import logging
import os
from pathlib import Path
import yaml
import json

logger = logging.getLogger(__name__)

def is_simulation_mode(config_path=None):
    """
    Check if simulation mode is enabled
    
    Args:
        config_path (str, optional): Path to configuration file
        
    Returns:
        bool: True if simulation mode is enabled, False otherwise
    """
    # Check environment variable first
    if os.environ.get("SIMULATION_MODE", "").lower() == "true":
        return True
    
    # Check command line arguments
    import sys
    if "--simulation" in sys.argv or "--paper-trading" in sys.argv:
        return True
    
    # Check configuration file
    if config_path is None:
        config_path = "config/simulation_config.yaml"
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        return config.get("simulation_mode", False)
    except Exception as e:
        logger.error(f"Error checking simulation mode: {e}")
        return False

def initialize_simulator(config_path=None):
    """
    Initialize the paper trading simulator
    
    Args:
        config_path (str, optional): Path to configuration file
        
    Returns:
        PaperTradingSimulator: Simulator instance
    """
    from simulation.paper_trading import PaperTradingSimulator
    
    # Initialize simulator
    simulator = PaperTradingSimulator(config_path)
    
    logger.info("Paper trading simulator initialized")
    
    return simulator

def adapt_bot_for_simulation(bot, simulator):
    """
    Adapt the trading bot for simulation mode
    
    Args:
        bot: Trading bot instance
        simulator: Paper trading simulator instance
        
    Returns:
        bot: Adapted trading bot instance
    """
    # Store original methods
    original_execute_trade = bot.execute_trade if hasattr(bot, 'execute_trade') else None
    original_get_balance = bot.get_balance if hasattr(bot, 'get_balance') else None
    
    # Replace execute_trade method
    def simulated_execute_trade(symbol, side, quantity, price, stop_loss=None, take_profit=None, ai_signals=None):
        logger.info(f"Executing simulated trade: {side} {quantity} {symbol} at {price}")
        return simulator.execute_trade(symbol, side, quantity, price, stop_loss, take_profit, ai_signals)
    
    # Replace get_balance method
    def simulated_get_balance():
        return simulator.balance
    
    # Attach simulator to bot
    bot.simulator = simulator
    
    # Replace methods
    if original_execute_trade:
        bot.original_execute_trade = original_execute_trade
        bot.execute_trade = simulated_execute_trade
    
    if original_get_balance:
        bot.original_get_balance = original_get_balance
        bot.get_balance = simulated_get_balance
    
    # Add method to switch back to live mode
    def switch_to_live_mode():
        logger.info("Switching to LIVE mode")
        if hasattr(bot, 'original_execute_trade'):
            bot.execute_trade = bot.original_execute_trade
        if hasattr(bot, 'original_get_balance'):
            bot.get_balance = bot.original_get_balance
        bot.is_simulation_mode = False
    
    # Add method to switch to simulation mode
    def switch_to_simulation_mode():
        logger.info("Switching to SIMULATION mode")
        bot.execute_trade = simulated_execute_trade
        bot.get_balance = simulated_get_balance
        bot.is_simulation_mode = True
    
    # Attach methods to bot
    bot.switch_to_live_mode = switch_to_live_mode
    bot.switch_to_simulation_mode = switch_to_simulation_mode
    bot.is_simulation_mode = True
    
    logger.info("Bot adapted for simulation mode")
    
    return bot
