"""
Dynamic Take Profit / Stop Loss Module

This module implements a dynamic TP/SL system that adjusts based on market conditions.
"""

import os
import yaml
import logging
import numpy as np
from pathlib import Path

logger = logging.getLogger(__name__)

class DynamicTPSL:
    """
    Dynamic Take Profit / Stop Loss Manager
    
    Adjusts TP/SL levels based on market conditions.
    """
    
    def __init__(self, config_path="config/tp_sl_config.yaml"):
        """
        Initialize the dynamic TP/SL manager
        
        Args:
            config_path (str): Path to TP/SL configuration file
        """
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Extract settings
        self.tp_sl_settings = self.config.get("tp_sl_settings", {
            "stable_market": {"take_profit": 0.7, "stop_loss": 0.8},
            "volatile_market": {"take_profit": 1.0, "stop_loss": 1.2}
        })
        
        self.market_condition_thresholds = self.config.get("market_condition_thresholds", {
            "atr_threshold": 1.2,
            "roc_threshold": 1.5
        })
        
        self.currency_multipliers = self.config.get("currency_multipliers", {
            "BTC": 1.0,
            "ETH": 1.5,
            "SOL": 3.0,
            "BNB": 1.8,
            "DEFAULT": 2.0
        })
        
        logger.info("DynamicTPSL initialized")
    
    def _load_config(self, config_path):
        """
        Load configuration from YAML file
        
        Args:
            config_path (str): Path to configuration file
            
        Returns:
            dict: Configuration parameters
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Error loading TP/SL config: {e}")
            return {
                "tp_sl_settings": {
                    "stable_market": {"take_profit": 0.7, "stop_loss": 0.8},
                    "volatile_market": {"take_profit": 1.0, "stop_loss": 1.2}
                },
                "market_condition_thresholds": {
                    "atr_threshold": 1.2,
                    "roc_threshold": 1.5
                },
                "currency_multipliers": {
                    "BTC": 1.0,
                    "ETH": 1.5,
                    "SOL": 3.0,
                    "BNB": 1.8,
                    "DEFAULT": 2.0
                }
            }
    
    def determine_market_condition(self, atr, roc, currency="BTC"):
        """
        Determine market condition based on ATR and ROC
        
        Args:
            atr (float): Average True Range value
            roc (float): Rate of Change value
            currency (str): Currency symbol
            
        Returns:
            str: Market condition ('stable' or 'volatile')
        """
        # Get currency multiplier
        multiplier = self.currency_multipliers.get(currency, self.currency_multipliers.get("DEFAULT", 2.0))
        
        # Apply currency multiplier to thresholds
        atr_threshold = self.market_condition_thresholds.get("atr_threshold", 1.2) * multiplier
        roc_threshold = self.market_condition_thresholds.get("roc_threshold", 1.5) * multiplier
        
        # Determine market condition
        if atr < atr_threshold and abs(roc) < roc_threshold:
            logger.info(f"Market condition for {currency}: STABLE (ATR: {atr:.2f}, ROC: {roc:.2f})")
            return "stable"
        else:
            logger.info(f"Market condition for {currency}: VOLATILE (ATR: {atr:.2f}, ROC: {roc:.2f})")
            return "volatile"
    
    def get_tp_sl_levels(self, market_condition, entry_price, side="buy"):
        """
        Get take profit and stop loss levels based on market condition
        
        Args:
            market_condition (str): Market condition ('stable' or 'volatile')
            entry_price (float): Entry price
            side (str): Trade side ('buy' or 'sell')
            
        Returns:
            tuple: (take_profit_price, stop_loss_price)
        """
        # Get TP/SL percentages based on market condition
        settings = self.tp_sl_settings.get(market_condition, self.tp_sl_settings.get("stable_market"))
        tp_percent = settings.get("take_profit", 0.7) / 100
        sl_percent = settings.get("stop_loss", 0.8) / 100
        
        # Calculate TP/SL prices based on side
        if side.lower() == "buy":
            take_profit_price = entry_price * (1 + tp_percent)
            stop_loss_price = entry_price * (1 - sl_percent)
        else:  # sell
            take_profit_price = entry_price * (1 - tp_percent)
            stop_loss_price = entry_price * (1 + sl_percent)
        
        logger.info(f"TP/SL levels for {side.upper()} in {market_condition.upper()} market:")
        logger.info(f"  Entry: {entry_price:.2f}")
        logger.info(f"  Take Profit: {take_profit_price:.2f} ({tp_percent * 100:.1f}%)")
        logger.info(f"  Stop Loss: {stop_loss_price:.2f} ({sl_percent * 100:.1f}%)")
        
        return take_profit_price, stop_loss_price
    
    def calculate_atr(self, high_prices, low_prices, close_prices, period=14):
        """
        Calculate Average True Range (ATR)
        
        Args:
            high_prices (list): List of high prices
            low_prices (list): List of low prices
            close_prices (list): List of close prices
            period (int): ATR period
            
        Returns:
            float: ATR value
        """
        if len(high_prices) < period + 1 or len(low_prices) < period + 1 or len(close_prices) < period + 1:
            return 0
        
        # Calculate true ranges
        tr_values = []
        
        for i in range(1, len(close_prices)):
            high = high_prices[i]
            low = low_prices[i]
            prev_close = close_prices[i-1]
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            
            true_range = max(tr1, tr2, tr3)
            tr_values.append(true_range)
        
        # Calculate ATR
        if len(tr_values) < period:
            return sum(tr_values) / len(tr_values)
        
        atr = sum(tr_values[-period:]) / period
        return atr
    
    def calculate_roc(self, prices, period=14):
        """
        Calculate Rate of Change (ROC)
        
        Args:
            prices (list): List of prices
            period (int): ROC period
            
        Returns:
            float: ROC value
        """
        if len(prices) < period + 1:
            return 0
        
        current_price = prices[-1]
        previous_price = prices[-period-1]
        
        roc = ((current_price - previous_price) / previous_price) * 100
        return roc
