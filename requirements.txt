aiodns==3.2.0
aiohappyeyeballs==2.6.1
aiohttp==3.10.11
aiosignal==1.3.2
astroid==3.3.9
attrs==25.3.0
bandit==1.8.3
ccxt==4.4.72
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
colorama==0.4.6
cryptography==44.0.2
dateparser==1.2.1
debugpy==1.8.14
decorator==5.2.1
dill==0.4.0
frozenlist==1.5.0
graphviz==0.20.3
idna==3.10
iniconfig==2.1.0
isort==6.0.1
loguru==0.7.0
markdown-it-py==3.0.0
mccabe==0.7.0
mdurl==0.1.2
multidict==6.3.2
numpy==1.26.4
objgraph==3.5.0
openai==1.3.0
packaging==24.2
pandas==2.2.3
pandas_ta==0.3.14b0
pbr==6.1.1
platformdirs==4.3.7
pluggy==1.5.0
propcache==0.3.1
psutil==7.0.0
pycares==4.6.0
pycparser==2.22
pycryptodome==3.22.0
Pygments==2.19.1
pylint==3.3.6
pytrends==4.9.2
pytest==8.3.5
pytest-asyncio==0.26.0
python-binance==1.0.19
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2025.2
pywin32==306
PyYAML==6.0.2
ratelimit==2.2.1
regex==2024.11.6
requests==2.31.0
rich==14.0.0
schedule==1.2.1
setuptools==78.1.0
six==1.17.0
slack-sdk==3.21.3
stevedore==5.4.1
tomlkit==0.13.2
typing_extensions==4.13.1
tzdata==2025.2
tzlocal==5.3.1
ujson==5.10.0
urllib3==2.3.0
websockets==12.0
wheel==0.45.1
win32_setctime==1.2.0
yarl==1.19.0

# Optional dependencies for news analysis
newsapi-python==0.2.7
matplotlib==3.7.2
seaborn==0.12.2

# Enhanced bot dependencies
deepseek-ai==0.1.0
qwen-client==0.1.0
ta-lib==0.4.26
scipy==1.11.2
pytest-cov==4.1.0
tqdm==4.65.0

# Enhanced v2.0.0 dependencies
scikit-learn>=1.3.0
joblib>=1.3.0
xgboost>=1.7.0
lightgbm>=4.0.0
plotly>=5.15.0
ntplib>=0.4.0
memory-profiler>=0.61.0
configparser>=5.3.0
jsonschema>=4.19.0
bcrypt>=4.0.0
keyring>=24.2.0
numba>=0.57.0
cython>=3.0.0
anthropic>=0.3.0
tabulate>=0.9.0
