"""
Trading Hours Module

This module implements customized trading hours by symbol.
"""

import os
import yaml
import logging
import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

class TradingHoursManager:
    """
    Trading Hours Manager
    
    Manages trading hours for different symbols and avoids trading during major market open/close windows.
    """
    
    def __init__(self, config_path="config/trading_hours_config.yaml"):
        """
        Initialize the trading hours manager
        
        Args:
            config_path (str): Path to trading hours configuration file
        """
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Extract settings
        self.trading_hours = self.config.get("trading_hours", {
            "DEFAULT": {"start": 0, "end": 24}
        })
        
        self.market_windows = self.config.get("market_windows", [])
        
        logger.info("TradingHoursManager initialized")
    
    def _load_config(self, config_path):
        """
        Load configuration from YAML file
        
        Args:
            config_path (str): Path to configuration file
            
        Returns:
            dict: Configuration parameters
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Error loading trading hours config: {e}")
            return {
                "trading_hours": {
                    "DEFAULT": {"start": 0, "end": 24}
                },
                "market_windows": []
            }
    
    def is_trading_time(self, symbol):
        """
        Check if current time is within trading hours for the symbol
        
        Args:
            symbol (str): Trading pair symbol
            
        Returns:
            bool: True if current time is within trading hours, False otherwise
        """
        # Get current UTC hour
        current_hour = datetime.datetime.utcnow().hour
        
        # Get trading hours for the symbol
        symbol_hours = self.trading_hours.get(symbol, self.trading_hours.get("DEFAULT", {"start": 0, "end": 24}))
        start_hour = symbol_hours.get("start", 0)
        end_hour = symbol_hours.get("end", 24)
        
        # Check if current hour is within trading hours
        is_trading_hour = (current_hour >= start_hour and current_hour < end_hour)
        
        if is_trading_hour:
            logger.info(f"Current hour ({current_hour} UTC) is within trading hours for {symbol} ({start_hour}-{end_hour} UTC)")
        else:
            logger.info(f"Current hour ({current_hour} UTC) is outside trading hours for {symbol} ({start_hour}-{end_hour} UTC)")
        
        return is_trading_hour
    
    def is_market_open_close_window(self):
        """
        Check if current time is within a major market open/close window
        
        Returns:
            tuple: (bool, str) - (True if within a market window, name of the market window)
        """
        # Get current UTC time
        now = datetime.datetime.utcnow()
        current_hour = now.hour
        current_minute = now.minute
        
        # Check each market window
        for window in self.market_windows:
            start_hour = window.get("start_hour", 0)
            start_minute = window.get("start_minute", 0)
            end_hour = window.get("end_hour", 0)
            end_minute = window.get("end_minute", 0)
            
            # Convert to minutes for easier comparison
            current_time_minutes = current_hour * 60 + current_minute
            start_time_minutes = start_hour * 60 + start_minute
            end_time_minutes = end_hour * 60 + end_minute
            
            # Check if current time is within the window
            if current_time_minutes >= start_time_minutes and current_time_minutes <= end_time_minutes:
                logger.info(f"Current time ({current_hour:02d}:{current_minute:02d} UTC) is within {window.get('name', 'market window')}")
                return True, window.get("name", "market window")
        
        return False, None
    
    def avoid_market_open_close(self):
        """
        Check if trading should be avoided due to major market open/close windows
        
        Returns:
            bool: True if trading should be avoided, False otherwise
        """
        is_window, window_name = self.is_market_open_close_window()
        
        if is_window:
            logger.info(f"Avoiding trading during {window_name}")
            return True
        
        return False
    
    def get_optimal_trading_hours(self, symbol):
        """
        Get optimal trading hours for a symbol
        
        Args:
            symbol (str): Trading pair symbol
            
        Returns:
            list: List of optimal trading hours
        """
        # Get trading hours for the symbol
        symbol_hours = self.trading_hours.get(symbol, self.trading_hours.get("DEFAULT", {"start": 0, "end": 24}))
        start_hour = symbol_hours.get("start", 0)
        end_hour = symbol_hours.get("end", 24)
        
        # Generate list of hours
        hours = list(range(start_hour, end_hour))
        
        # Filter out market open/close windows
        optimal_hours = []
        for hour in hours:
            # Check if hour is within any market window
            is_in_window = False
            for window in self.market_windows:
                window_start_hour = window.get("start_hour", 0)
                window_end_hour = window.get("end_hour", 0)
                
                if hour >= window_start_hour and hour <= window_end_hour:
                    is_in_window = True
                    break
            
            if not is_in_window:
                optimal_hours.append(hour)
        
        logger.info(f"Optimal trading hours for {symbol}: {optimal_hours}")
        
        return optimal_hours
