"""
Unit tests for the paper trading simulation module.
"""

import unittest
import os
import sys
import json
import datetime
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simulation.paper_trading import PaperTradingSimulator


class TestPaperTrading(unittest.TestCase):
    """Test cases for the paper trading simulation module."""

    def setUp(self):
        """Set up test environment before each test."""
        # Create a mock config for testing
        self.mock_config = {
            "simulation_mode": True,
            "max_simulation_trades": 10,
            "initial_balance": 100.0,
            "report_frequency": "daily"
        }
        
        # Patch the _load_config method to return our mock config
        with patch('simulation.paper_trading.PaperTradingSimulator._load_config', return_value=self.mock_config):
            self.simulator = PaperTradingSimulator()
    
    def test_initialization(self):
        """Test that the simulator initializes correctly."""
        self.assertEqual(self.simulator.balance, 100.0)
        self.assertEqual(self.simulator.starting_balance, 100.0)
        self.assertEqual(self.simulator.max_trades, 10)
        self.assertEqual(len(self.simulator.trades), 0)
        self.assertEqual(len(self.simulator.open_positions), 0)
        self.assertEqual(self.simulator.trade_count, 0)
    
    def test_execute_trade_buy(self):
        """Test executing a buy trade."""
        trade = self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.01,
            price=30000.0,
            stop_loss=29000.0,
            take_profit=31000.0
        )
        
        # Check that the trade was executed correctly
        self.assertIsNotNone(trade)
        self.assertEqual(trade["symbol"], "BTC/USDT")
        self.assertEqual(trade["side"], "buy")
        self.assertEqual(trade["quantity"], 0.01)
        self.assertEqual(trade["price"], 30000.0)
        self.assertEqual(trade["value"], 300.0)
        self.assertEqual(trade["stop_loss"], 29000.0)
        self.assertEqual(trade["take_profit"], 31000.0)
        self.assertEqual(trade["status"], "open")
        
        # Check that the balance was updated
        self.assertEqual(self.simulator.balance, 100.0 - 300.0)
        
        # Check that the trade was added to open positions
        self.assertEqual(len(self.simulator.open_positions), 1)
        self.assertEqual(len(self.simulator.trades), 1)
        self.assertEqual(self.simulator.trade_count, 1)
    
    def test_execute_trade_insufficient_balance(self):
        """Test executing a buy trade with insufficient balance."""
        trade = self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=1.0,
            price=30000.0
        )
        
        # Check that the trade was not executed
        self.assertIsNone(trade)
        
        # Check that the balance was not updated
        self.assertEqual(self.simulator.balance, 100.0)
        
        # Check that no trade was added
        self.assertEqual(len(self.simulator.open_positions), 0)
        self.assertEqual(len(self.simulator.trades), 0)
        self.assertEqual(self.simulator.trade_count, 0)
    
    def test_execute_trade_max_trades_reached(self):
        """Test executing a trade when max trades is reached."""
        # Set trade count to max
        self.simulator.trade_count = 10
        
        trade = self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.001,
            price=30000.0
        )
        
        # Check that the trade was not executed
        self.assertIsNone(trade)
        
        # Check that the balance was not updated
        self.assertEqual(self.simulator.balance, 100.0)
        
        # Check that no trade was added
        self.assertEqual(len(self.simulator.open_positions), 0)
        self.assertEqual(len(self.simulator.trades), 0)
    
    def test_update_market_price_stop_loss(self):
        """Test updating market price triggering stop loss."""
        # Execute a buy trade
        self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.01,
            price=30000.0,
            stop_loss=29000.0,
            take_profit=31000.0
        )
        
        # Update market price to trigger stop loss
        triggered_trades = self.simulator.update_market_price("BTC/USDT", 28000.0)
        
        # Check that a sell trade was triggered
        self.assertEqual(len(triggered_trades), 1)
        self.assertEqual(triggered_trades[0]["reason"], "stop_loss")
        
        # Check that the position was closed
        self.assertEqual(len(self.simulator.open_positions), 0)
        
        # Check that the balance was updated (100 - 300 + 290 = 90)
        self.assertEqual(self.simulator.balance, 90.0)
    
    def test_update_market_price_take_profit(self):
        """Test updating market price triggering take profit."""
        # Execute a buy trade
        self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.01,
            price=30000.0,
            stop_loss=29000.0,
            take_profit=31000.0
        )
        
        # Update market price to trigger take profit
        triggered_trades = self.simulator.update_market_price("BTC/USDT", 32000.0)
        
        # Check that a sell trade was triggered
        self.assertEqual(len(triggered_trades), 1)
        self.assertEqual(triggered_trades[0]["reason"], "take_profit")
        
        # Check that the position was closed
        self.assertEqual(len(self.simulator.open_positions), 0)
        
        # Check that the balance was updated (100 - 300 + 310 = 110)
        self.assertEqual(self.simulator.balance, 110.0)
    
    def test_record_ai_prediction(self):
        """Test recording AI model predictions."""
        self.simulator.record_ai_prediction(
            model="openai",
            prediction="buy",
            confidence=0.85
        )
        
        self.simulator.record_ai_prediction(
            model="deepseek",
            prediction="sell",
            confidence=0.75
        )
        
        # Check that predictions were recorded
        self.assertEqual(len(self.simulator.ai_predictions["openai"]), 1)
        self.assertEqual(len(self.simulator.ai_predictions["deepseek"]), 1)
        self.assertEqual(self.simulator.ai_predictions["openai"][0]["prediction"], "buy")
        self.assertEqual(self.simulator.ai_predictions["deepseek"][0]["prediction"], "sell")
    
    def test_record_debate_session(self):
        """Test recording debate sessions."""
        self.simulator.record_debate_session(
            models_involved=["openai", "deepseek", "qwen"],
            recommendations={"openai": "buy", "deepseek": "sell", "qwen": "hold"},
            weights={"openai": 0.3, "deepseek": 0.35, "qwen": 0.35},
            final_decision="hold"
        )
        
        # Check that the debate session was recorded
        self.assertEqual(len(self.simulator.debate_sessions), 1)
        self.assertEqual(self.simulator.debate_sessions[0]["models_involved"], ["openai", "deepseek", "qwen"])
        self.assertEqual(self.simulator.debate_sessions[0]["final_decision"], "hold")
    
    def test_generate_report(self):
        """Test generating a simulation report."""
        # Add some trades and predictions
        self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.01,
            price=30000.0
        )
        
        self.simulator.record_ai_prediction(
            model="openai",
            prediction="buy",
            confidence=0.85,
            actual_outcome="buy"
        )
        
        self.simulator.verify_prediction("openai", 0, True)
        
        # Generate report
        report = self.simulator.generate_report()
        
        # Check report contents
        self.assertIn("timestamp", report)
        self.assertIn("balance", report)
        self.assertIn("trade_statistics", report)
        self.assertIn("ai_model_accuracy", report)
        
        # Check AI accuracy
        self.assertEqual(report["ai_model_accuracy"]["openai"], 1.0)


if __name__ == "__main__":
    unittest.main()
