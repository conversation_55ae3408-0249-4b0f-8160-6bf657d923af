"""
Unit tests for the AI debate mode functionality.
"""

import unittest
import os
import sys
import json
import datetime
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_services.debate_enhancer import DebateEnhancer


class TestDebateMode(unittest.TestCase):
    """Test cases for the AI debate mode functionality."""

    def setUp(self):
        """Set up test environment before each test."""
        # Create a temporary directory for test data
        self.test_data_dir = "test_data"
        os.makedirs(self.test_data_dir, exist_ok=True)
        
        # Initialize the debate enhancer with the test directory
        self.debate_enhancer = DebateEnhancer(base_path=self.test_data_dir)
        
        # Set up test model weights
        self.debate_enhancer.current_weights = {
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        }
        
        # Set up test model accuracies
        self.test_accuracies = {
            "openai": 0.75,
            "deepseek": 0.80,
            "qwen": 0.85
        }
    
    def tearDown(self):
        """Clean up after each test."""
        # Remove test data directory
        import shutil
        if os.path.exists(self.test_data_dir):
            shutil.rmtree(self.test_data_dir)
    
    def test_initialization(self):
        """Test that the debate enhancer initializes correctly."""
        self.assertEqual(self.debate_enhancer.current_weights["openai"], 0.30)
        self.assertEqual(self.debate_enhancer.current_weights["deepseek"], 0.35)
        self.assertEqual(self.debate_enhancer.current_weights["qwen"], 0.35)
        self.assertEqual(self.debate_enhancer.debate_sessions, 0)
    
    def test_record_model_accuracy(self):
        """Test recording model accuracy."""
        file_path = self.debate_enhancer.record_model_accuracy(self.test_accuracies)
        
        # Check that the file was created
        self.assertTrue(os.path.exists(file_path))
        
        # Check that the data was saved correctly
        with open(file_path, 'r') as f:
            data = json.load(f)
            self.assertEqual(data["openai_accuracy"], 0.75)
            self.assertEqual(data["deepseek_accuracy"], 0.80)
            self.assertEqual(data["qwen_accuracy"], 0.85)
        
        # Check that the performance history was updated
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        self.assertIn(today, self.debate_enhancer.performance_history)
        self.assertEqual(self.debate_enhancer.performance_history[today]["openai_accuracy"], 0.75)
    
    def test_update_ai_weights(self):
        """Test updating AI weights based on performance."""
        # Add some performance history
        today = datetime.datetime.now().date()
        
        for i in range(7):
            date_str = (today - datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            self.debate_enhancer.performance_history[date_str] = {
                "date": date_str,
                "openai_accuracy": 0.70,
                "deepseek_accuracy": 0.80,
                "qwen_accuracy": 0.90
            }
        
        # Update weights
        updated_weights = self.debate_enhancer.update_ai_weights()
        
        # Check that weights were updated correctly
        # Qwen should have the highest weight due to highest accuracy
        self.assertGreater(updated_weights["qwen"], updated_weights["deepseek"])
        self.assertGreater(updated_weights["deepseek"], updated_weights["openai"])
        
        # Check that weights sum to 1
        self.assertAlmostEqual(sum(updated_weights.values()), 1.0, places=5)
        
        # Check that weights are within bounds
        for model, weight in updated_weights.items():
            min_weight, max_weight = self.debate_enhancer.weight_bounds.get(model, (0, 1))
            self.assertGreaterEqual(weight, min_weight)
            self.assertLessEqual(weight, max_weight)
    
    def test_get_top_performing_model(self):
        """Test getting the top performing model."""
        # Add some performance history
        today = datetime.datetime.now().date()
        
        for i in range(7):
            date_str = (today - datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            self.debate_enhancer.performance_history[date_str] = {
                "date": date_str,
                "openai_accuracy": 0.70,
                "deepseek_accuracy": 0.80,
                "qwen_accuracy": 0.90
            }
        
        # Get top performing model
        top_model, top_accuracy = self.debate_enhancer.get_top_performing_model()
        
        # Check that the top model is correct
        self.assertEqual(top_model, "qwen")
        self.assertAlmostEqual(top_accuracy, 0.90, places=5)
    
    def test_get_debate_weights(self):
        """Test getting weights for debate mode."""
        # Add some performance history to determine top model
        today = datetime.datetime.now().date()
        
        for i in range(7):
            date_str = (today - datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            self.debate_enhancer.performance_history[date_str] = {
                "date": date_str,
                "openai_accuracy": 0.70,
                "deepseek_accuracy": 0.80,
                "qwen_accuracy": 0.90
            }
        
        # Get debate weights
        debate_weights = self.debate_enhancer.get_debate_weights()
        
        # Check that the top model (qwen) got extra weight
        self.assertGreater(debate_weights["qwen"], self.debate_enhancer.current_weights["qwen"])
        
        # Check that weights sum to 1
        self.assertAlmostEqual(sum(debate_weights.values()), 1.0, places=5)
    
    def test_log_debate_session(self):
        """Test logging a debate session."""
        # Log a debate session
        file_path = self.debate_enhancer.log_debate_session(
            models_involved=["openai", "deepseek", "qwen"],
            recommendations={
                "openai": "buy",
                "deepseek": "sell",
                "qwen": "hold"
            },
            confidences={
                "openai": 0.75,
                "deepseek": 0.80,
                "qwen": 0.85
            },
            weights={
                "openai": 0.30,
                "deepseek": 0.35,
                "qwen": 0.35
            },
            final_decision="hold",
            detailed_analyses={
                "openai": "Market shows bullish signals with strong volume.",
                "deepseek": "Technical indicators suggest a bearish reversal.",
                "qwen": "Mixed signals, recommend holding current position."
            }
        )
        
        # Check that the file was created
        self.assertTrue(os.path.exists(file_path))
        
        # Check that the data was saved correctly
        with open(file_path, 'r') as f:
            data = json.load(f)
            self.assertEqual(data["models_involved"], ["openai", "deepseek", "qwen"])
            self.assertEqual(data["recommendations"]["openai"], "buy")
            self.assertEqual(data["final_decision"], "hold")
        
        # Check that the debate session counter was incremented
        self.assertEqual(self.debate_enhancer.debate_sessions, 1)
    
    def test_format_debate_summary(self):
        """Test formatting a debate session summary."""
        # Create debate data
        debate_data = {
            "session_id": 1,
            "timestamp": datetime.datetime.now().isoformat(),
            "models_involved": ["openai", "deepseek", "qwen"],
            "recommendations": {
                "openai": "buy",
                "deepseek": "sell",
                "qwen": "hold"
            },
            "confidences": {
                "openai": 0.75,
                "deepseek": 0.80,
                "qwen": 0.85
            },
            "weights": {
                "openai": 0.30,
                "deepseek": 0.35,
                "qwen": 0.35
            },
            "final_decision": "hold",
            "detailed_analyses": {
                "openai": "Market shows bullish signals with strong volume.",
                "deepseek": "Technical indicators suggest a bearish reversal.",
                "qwen": "Mixed signals, recommend holding current position."
            }
        }
        
        # Format debate summary
        summary = self.debate_enhancer.format_debate_summary(debate_data)
        
        # Check that the summary contains key information
        self.assertIn("Debate Session #1", summary)
        self.assertIn("OPENAI (weight: 0.30): BUY", summary)
        self.assertIn("DEEPSEEK (weight: 0.35): SELL", summary)
        self.assertIn("QWEN (weight: 0.35): HOLD", summary)
        self.assertIn("Final Decision: HOLD", summary)
        self.assertIn("Market shows bullish signals", summary)


if __name__ == "__main__":
    unittest.main()
