#!/usr/bin/env python
"""
Binance API Connection Test Tool

This script tests the connection to the Binance API and verifies that
all endpoints are working correctly.
"""

import os
import sys
import time
import logging
import argparse
import requests
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).resolve().parent.parent))

from exchange.binance_api import BinanceAPI
from security.key_manager import KeyManager
from core.config import BotConfig

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("binance_api_test")

def test_direct_ping():
    """Test direct ping to Binance API"""
    try:
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
        if response.status_code == 200:
            logger.info("✅ Direct ping to Binance API successful")
            return True
        else:
            logger.error(f"❌ Direct ping to Binance API failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Direct ping to Binance API failed: {e}")
        return False

def test_direct_time():
    """Test direct time endpoint of Binance API"""
    try:
        response = requests.get("https://api.binance.com/api/v3/time", timeout=10)
        if response.status_code == 200:
            data = response.json()
            server_time = data.get("serverTime", 0)
            local_time = int(time.time() * 1000)
            time_diff = abs(server_time - local_time)
            
            logger.info(f"✅ Direct time check successful: Server time: {server_time}, Local time: {local_time}")
            logger.info(f"Time difference: {time_diff}ms")
            
            if time_diff > 10000:  # 10 seconds
                logger.warning("⚠️ Time difference is high (>10s)")
            
            return True
        else:
            logger.error(f"❌ Direct time check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Direct time check failed: {e}")
        return False

def test_ccxt_api():
    """Test CCXT API connection to Binance"""
    try:
        # Get API credentials
        api_key, api_secret = BotConfig.get_api_credentials()
        
        if not api_key or not api_secret:
            logger.warning("⚠️ No API credentials found. Testing public endpoints only.")
        
        # Initialize Binance API
        binance_api = BinanceAPI(api_key, api_secret)
        
        # Test API endpoints
        results = binance_api.test_binance_api_endpoints()
        
        # Print results
        for endpoint, success in results.items():
            if success:
                logger.info(f"✅ CCXT {endpoint} endpoint test: Successful")
            else:
                logger.error(f"❌ CCXT {endpoint} endpoint test: Failed")
        
        # Check API latency
        latency = binance_api.check_api_latency()
        logger.info(f"API latency: {latency:.2f}ms")
        
        if latency > 1000:
            logger.warning("⚠️ API latency is high (>1000ms)")
        
        return all(results.values())
    except Exception as e:
        logger.error(f"❌ CCXT API test failed: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test Binance API connection")
    parser.add_argument("--direct", action="store_true", help="Test direct API connection only")
    parser.add_argument("--ccxt", action="store_true", help="Test CCXT API connection only")
    args = parser.parse_args()
    
    logger.info("Starting Binance API connection test...")
    
    if args.direct or not (args.direct or args.ccxt):
        logger.info("Testing direct API connection...")
        ping_success = test_direct_ping()
        time_success = test_direct_time()
        
        if ping_success and time_success:
            logger.info("✅ Direct API connection tests passed")
        else:
            logger.error("❌ Direct API connection tests failed")
    
    if args.ccxt or not (args.direct or args.ccxt):
        logger.info("Testing CCXT API connection...")
        ccxt_success = test_ccxt_api()
        
        if ccxt_success:
            logger.info("✅ CCXT API connection tests passed")
        else:
            logger.error("❌ CCXT API connection tests failed")
    
    logger.info("Binance API connection test completed")

if __name__ == "__main__":
    main()
