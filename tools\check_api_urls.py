#!/usr/bin/env python
"""
API URL Checker

This script checks the API URLs being used by the trading bot to ensure
they are pointing to Binance and not any other exchange.
"""

import os
import sys
import logging
import argparse
import requests
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).resolve().parent.parent))

from exchange.binance_api import BinanceAPI
from security.key_manager import KeyManager
from core.config import BotConfig

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("api_url_checker")

def check_ccxt_urls():
    """Check CCXT URLs for Binance"""
    try:
        # Get API credentials
        api_key, api_secret = BotConfig.get_api_credentials()
        
        # Initialize Binance API
        binance_api = BinanceAPI(api_key, api_secret)
        
        # Get exchange info
        exchange = binance_api.exchange
        
        # Print exchange info
        logger.info(f"Exchange ID: {exchange.id}")
        logger.info(f"Exchange name: {exchange.name}")
        
        # Print API URLs
        if hasattr(exchange, 'urls') and 'api' in exchange.urls:
            logger.info("API URLs:")
            for key, value in exchange.urls['api'].items():
                if isinstance(value, dict):
                    for subkey, subvalue in value.items():
                        logger.info(f"  - {key}.{subkey}: {subvalue}")
                else:
                    logger.info(f"  - {key}: {value}")
            
            # Check if any URL contains 'coinbase'
            coinbase_urls = []
            for key, value in exchange.urls['api'].items():
                if isinstance(value, dict):
                    for subkey, subvalue in value.items():
                        if isinstance(subvalue, str) and 'coinbase' in subvalue.lower():
                            coinbase_urls.append(f"{key}.{subkey}: {subvalue}")
                elif isinstance(value, str) and 'coinbase' in value.lower():
                    coinbase_urls.append(f"{key}: {value}")
            
            if coinbase_urls:
                logger.error("❌ Found Coinbase URLs:")
                for url in coinbase_urls:
                    logger.error(f"  - {url}")
                return False
            else:
                logger.info("✅ No Coinbase URLs found")
                return True
        else:
            logger.warning("⚠️ No API URLs found")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking CCXT URLs: {e}")
        return False

def check_api_requests():
    """Check actual API requests to ensure they go to Binance"""
    try:
        # Get API credentials
        api_key, api_secret = BotConfig.get_api_credentials()
        
        # Initialize Binance API
        binance_api = BinanceAPI(api_key, api_secret)
        
        # Monkey patch the fetch method to log URLs
        original_fetch = binance_api.exchange.fetch
        
        urls = []
        
        def fetch_wrapper(url, *args, **kwargs):
            urls.append(url)
            logger.info(f"API request to: {url}")
            return original_fetch(url, *args, **kwargs)
        
        binance_api.exchange.fetch = fetch_wrapper
        
        # Make some API calls
        logger.info("Making test API calls...")
        binance_api.check_api_latency()
        binance_api.get_ticker("BTC/USDT")
        
        # Restore original fetch method
        binance_api.exchange.fetch = original_fetch
        
        # Check if any URL contains 'coinbase'
        coinbase_urls = [url for url in urls if 'coinbase' in url.lower()]
        
        if coinbase_urls:
            logger.error("❌ Found requests to Coinbase URLs:")
            for url in coinbase_urls:
                logger.error(f"  - {url}")
            return False
        else:
            logger.info(f"✅ All {len(urls)} API requests went to Binance")
            return True
    except Exception as e:
        logger.error(f"❌ Error checking API requests: {e}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Check API URLs")
    parser.add_argument("--urls", action="store_true", help="Check CCXT URLs only")
    parser.add_argument("--requests", action="store_true", help="Check API requests only")
    args = parser.parse_args()
    
    logger.info("Starting API URL check...")
    
    if args.urls or not (args.urls or args.requests):
        logger.info("Checking CCXT URLs...")
        urls_ok = check_ccxt_urls()
        
        if urls_ok:
            logger.info("✅ CCXT URLs check passed")
        else:
            logger.error("❌ CCXT URLs check failed")
    
    if args.requests or not (args.urls or args.requests):
        logger.info("Checking API requests...")
        requests_ok = check_api_requests()
        
        if requests_ok:
            logger.info("✅ API requests check passed")
        else:
            logger.error("❌ API requests check failed")
    
    logger.info("API URL check completed")

if __name__ == "__main__":
    main()
