# Trading Bot Enhancements

This repository contains enhancements to the trading bot to improve performance with small accounts (< $50).

## Overview

The enhancements focus on:

1. **Paper-Trading Mode**: Simulate trades without risking real money
2. **Enhanced Debate Mode**: Improve AI model decision-making
3. **Dynamic TP/SL System**: Adjust take profit and stop loss levels based on market conditions
4. **Micro-Trade Mode**: Optimize position sizing for small accounts
5. **Customized Trading Hours**: Define specific trading hours for each symbol
6. **Technical Improvements**: Optimize RAM and CPU usage, enhance error handling

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/trading-bot.git
   cd trading-bot
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure the bot:
   - Edit `config/enhanced_config.yaml` to set your preferences
   - Set your API keys in `.env` file

## Usage

### Running the Bot

```bash
# Run in live mode
python enhanced_bot.py

# Run in simulation mode
python enhanced_bot.py --simulation

# Run in low-power mode
python enhanced_bot.py --low-power

# Specify a custom configuration file
python enhanced_bot.py --config path/to/config.yaml
```

### Testing the Enhancements

```bash
# Run all tests
python tests/test_enhancements.py

# Run specific test
python tests/test_enhancements.py TestPaperTradingMode
```

## Configuration

The bot uses several configuration files:

- `config/enhanced_config.yaml`: Main configuration file
- `config/simulation_config.yaml`: Paper-trading mode configuration
- `config/ai_config.yaml`: AI models and debate mode configuration
- `config/tp_sl_config.yaml`: Dynamic TP/SL system configuration
- `config/micro_trade_config.yaml`: Micro-trade mode configuration
- `config/trading_hours_config.yaml`: Trading hours configuration

## Features

### Paper-Trading Mode

Simulate trades without executing real orders:

```yaml
# config/simulation_config.yaml
simulation_mode: true
max_simulation_trades: 50
```

### Enhanced Debate Mode

Improve AI model decision-making:

```yaml
# config/ai_config.yaml
debate_mode:
  enabled: true
  log_detailed_analyses: true
  extra_weight_for_top_performer: 0.10
  performance_evaluation_days: 7
```

### Dynamic TP/SL System

Adjust take profit and stop loss levels based on market conditions:

```yaml
# config/tp_sl_config.yaml
tp_sl_settings:
  stable_market:
    take_profit: 0.7
    stop_loss: 0.8
  volatile_market:
    take_profit: 1.0
    stop_loss: 1.2
```

### Micro-Trade Mode

Optimize position sizing for small accounts:

```yaml
# config/micro_trade_config.yaml
micro_trade_mode:
  enabled: true
  small_account_threshold: 25.0
  max_position_size_percent: 0.5
```

### Customized Trading Hours

Define specific trading hours for each symbol:

```yaml
# config/trading_hours_config.yaml
trading_hours:
  BTC/USDT: 
    start: 9
    end: 17
```

## Documentation

For detailed documentation, see:

- [Enhancements Documentation](docs/enhancements.md)
- [API Reference](docs/api_reference.md)
- [Configuration Guide](docs/configuration_guide.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
