#!/usr/bin/env python
"""
Test script for the AI Model Evaluator
"""

import os
import sys
import logging
import datetime
import random
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model evaluator
from ai_services.model_evaluator import AIModelEvaluator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Also print to stdout
def log_info(msg):
    logger.info(msg)
    print(msg)

def test_model_evaluator():
    """
    Test the AI Model Evaluator
    """
    try:
        # Initialize model evaluator
        log_info("Initializing model evaluator...")
        evaluator = AIModelEvaluator(base_weights={
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        })

        log_info("Model evaluator initialized")
        log_info(f"Base weights: {evaluator.base_weights}")
        log_info(f"Current weights: {evaluator.current_weights}")
    except Exception as e:
        logger.error(f"Error initializing model evaluator: {e}")
        import traceback
        traceback.print_exc()

    try:
        # Simulate predictions
        simulate_predictions(evaluator)

        # Evaluate performance
        log_info("Evaluating performance...")
        updated_weights = evaluator.evaluate_performance()
        log_info(f"Updated weights: {updated_weights}")

        # Generate daily report
        log_info("Generating daily report...")
        report = evaluator.generate_daily_report()
        log_info(f"Daily report: {report}")

        # Test debate mode
        test_debate_mode(evaluator)

        log_info("Model evaluator test completed")
    except Exception as e:
        logger.error(f"Error in test_model_evaluator: {e}")
        import traceback
        traceback.print_exc()

def simulate_predictions(evaluator):
    """
    Simulate predictions for testing
    """
    try:
        log_info("Simulating predictions...")

        # Simulate different accuracy levels for each model
        accuracy = {
            "openai": 0.70,
            "deepseek": 0.80,
            "qwen": 0.90
        }

        # Generate 20 predictions for each model
        for i in range(20):
            logger.debug(f"Generating prediction {i+1}/20...")
            for model in ["openai", "deepseek", "qwen"]:
                # Generate random prediction
                prediction = random.choice(["buy", "sell", "hold"])
                confidence = random.uniform(0.5, 0.95)

                # Record prediction
                evaluator.record_prediction(model, prediction, confidence)

                # Verify prediction with probability based on model accuracy
                correct = random.random() < accuracy[model]

                # Get the latest prediction index
                idx = len(evaluator.predictions.get(model, [])) - 1
                if idx < 0:
                    logger.warning(f"No predictions found for {model}")
                    continue

                # Verify prediction
                evaluator.verify_prediction(model, idx, correct)

        log_info("Predictions simulated")
    except Exception as e:
        logger.error(f"Error simulating predictions: {e}")
        import traceback
        traceback.print_exc()

def test_debate_mode(evaluator):
    """
    Test debate mode
    """
    try:
        log_info("Testing debate mode...")

        # Create mock signals with disagreement
        signals = {
            "openai": {
                "analysis": {
                    "recommendation": "buy",
                    "confidence": 80
                }
            },
            "deepseek": {
                "analysis": {
                    "recommendation": "sell",
                    "confidence": 75
                }
            },
            "qwen": {
                "analysis": {
                    "recommendation": "hold",
                    "confidence": 85
                }
            }
        }

        # Get debate weights
        log_info("Getting debate weights...")
        debate_weights = evaluator.get_debate_weights(signals)
        log_info(f"Debate weights: {debate_weights}")

        # Check if weights are adjusted based on performance
        log_info("Checking if weights are adjusted based on performance...")

        # Qwen should have higher weight due to better performance
        if "qwen" in debate_weights and "qwen" in evaluator.base_weights:
            if debate_weights["qwen"] > evaluator.base_weights["qwen"]:
                log_info("PASS: Qwen's weight increased due to better performance")
            else:
                log_info("FAIL: Qwen's weight not increased despite better performance")
        else:
            log_info("FAIL: Qwen not found in weights")

        log_info("Debate mode test completed")
    except Exception as e:
        logger.error(f"Error testing debate mode: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_model_evaluator()
