    def _call_deepseek_api(self, account, formatted_data, system_prompt=None, user_prompt=None):
        """
        Call DeepSeek API for market analysis

        Args:
            account (AIServiceAccount): DeepSeek account to use
            formatted_data (str): Formatted market data
            system_prompt (str, optional): Custom system prompt to use
            user_prompt (str, optional): Additional user prompt to append

        Returns:
            dict: Analysis result
        """
        try:
            # In a real implementation, this would make an actual API call
            # For simulation, we'll generate a random response
            import random

            # Use provided system prompt or default
            actual_system_prompt = system_prompt or self.system_prompts["deepseek"]
            
            # Combine formatted data with user prompt if provided
            actual_user_prompt = formatted_data
            if user_prompt:
                actual_user_prompt = f"{formatted_data}\n\n{user_prompt}"

            # Simulate API call
            time.sleep(0.5)  # Simulate API latency

            # Mark account as used
            account.mark_used(tokens_used=random.randint(100, 500))

            # Generate random sentiment
            sentiment = random.choice(["bullish", "bearish", "neutral"])

            # Generate random recommendation
            if sentiment == "bullish":
                recommendation = random.choices(["buy", "hold"], weights=[0.7, 0.3])[0]
            elif sentiment == "bearish":
                recommendation = random.choices(["sell", "hold"], weights=[0.7, 0.3])[0]
            else:
                recommendation = "hold"

            # Generate random confidence
            confidence = random.randint(50, 95)

            # Generate explanation
            explanations = {
                "bullish": [
                    "Price is showing strong upward momentum with increasing volume.",
                    "Recent price action has broken through key resistance levels.",
                    "Market sentiment is positive with strong buying pressure."
                ],
                "bearish": [
                    "Price is showing downward momentum with increasing selling volume.",
                    "Recent price action has broken below key support levels.",
                    "Market sentiment is negative with strong selling pressure."
                ],
                "neutral": [
                    "Price is consolidating within a range with no clear direction.",
                    "Volume is decreasing, indicating lack of conviction from buyers or sellers.",
                    "Market is waiting for a catalyst to determine the next move."
                ]
            }

            explanation = random.choice(explanations[sentiment])

            # Create analysis result
            analysis = {
                "sentiment": sentiment,
                "recommendation": recommendation,
                "confidence": confidence,
                "explanation": explanation
            }

            return {
                "service": "deepseek",
                "account_id": account.account_id,
                "analysis": analysis,
                "timestamp": datetime.datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calling DeepSeek API: {e}")
            account.mark_error()

            # If too many errors, disable the account
            if account.error_count >= 3:
                account.disable()

            return None

    def _call_qwen_api(self, account, formatted_data, system_prompt=None, user_prompt=None):
        """
        Call Qwen API for market analysis

        Args:
            account (AIServiceAccount): Qwen account to use
            formatted_data (str): Formatted market data
            system_prompt (str, optional): Custom system prompt to use
            user_prompt (str, optional): Additional user prompt to append

        Returns:
            dict: Analysis result
        """
        try:
            # In a real implementation, this would make an actual API call
            # For simulation, we'll generate a random response
            import random

            # Use provided system prompt or default
            actual_system_prompt = system_prompt or self.system_prompts["qwen"]
            
            # Combine formatted data with user prompt if provided
            actual_user_prompt = formatted_data
            if user_prompt:
                actual_user_prompt = f"{formatted_data}\n\n{user_prompt}"

            # Simulate API call
            time.sleep(0.5)  # Simulate API latency

            # Mark account as used
            account.mark_used(tokens_used=random.randint(100, 500))

            # Generate random sentiment
            sentiment = random.choice(["bullish", "bearish", "neutral"])

            # Generate random recommendation
            if sentiment == "bullish":
                recommendation = random.choices(["buy", "hold"], weights=[0.7, 0.3])[0]
            elif sentiment == "bearish":
                recommendation = random.choices(["sell", "hold"], weights=[0.7, 0.3])[0]
            else:
                recommendation = "hold"

            # Generate random confidence
            confidence = random.randint(50, 95)

            # Generate explanation
            explanations = {
                "bullish": [
                    "Price is showing strong upward momentum with increasing volume.",
                    "Recent price action has broken through key resistance levels.",
                    "Market sentiment is positive with strong buying pressure."
                ],
                "bearish": [
                    "Price is showing downward momentum with increasing selling volume.",
                    "Recent price action has broken below key support levels.",
                    "Market sentiment is negative with strong selling pressure."
                ],
                "neutral": [
                    "Price is consolidating within a range with no clear direction.",
                    "Volume is decreasing, indicating lack of conviction from buyers or sellers.",
                    "Market is waiting for a catalyst to determine the next move."
                ]
            }

            explanation = random.choice(explanations[sentiment])

            # Create analysis result
            analysis = {
                "sentiment": sentiment,
                "recommendation": recommendation,
                "confidence": confidence,
                "explanation": explanation
            }

            return {
                "service": "qwen",
                "account_id": account.account_id,
                "analysis": analysis,
                "timestamp": datetime.datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calling Qwen API: {e}")
            account.mark_error()

            # If too many errors, disable the account
            if account.error_count >= 3:
                account.disable()

            return None
