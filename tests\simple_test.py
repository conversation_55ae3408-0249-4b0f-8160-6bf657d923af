#!/usr/bin/env python

import os
import sys

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("Starting simple test...")

try:
    # Import the model evaluator
    from ai_services.model_evaluator import AIModelEvaluator
    print("Successfully imported AIModelEvaluator")
    
    # Initialize model evaluator
    evaluator = AIModelEvaluator()
    print(f"Successfully initialized AIModelEvaluator with weights: {evaluator.base_weights}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("Simple test completed")
