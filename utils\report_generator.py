"""
Report Generator Module

This module provides functionality for generating comprehensive
reports on trading bot performance, AI model accuracy, and system health.
"""

import os
import time
import logging
import datetime
import json
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

# Set up logging
logger = logging.getLogger("report_generator")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/report_generator.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)


class ReportGenerator:
    """
    Generates comprehensive reports on trading bot performance
    """
    
    def __init__(self, report_dir="reports"):
        """
        Initialize the report generator
        
        Parameters:
        report_dir (str): Directory to store reports
        """
        self.report_dir = Path(report_dir)
        self.report_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        self.daily_dir = self.report_dir / "daily"
        self.daily_dir.mkdir(exist_ok=True)
        
        self.weekly_dir = self.report_dir / "weekly"
        self.weekly_dir.mkdir(exist_ok=True)
        
        self.monthly_dir = self.report_dir / "monthly"
        self.monthly_dir.mkdir(exist_ok=True)
        
        self.ai_dir = self.report_dir / "ai_performance"
        self.ai_dir.mkdir(exist_ok=True)
        
        self.system_dir = self.report_dir / "system"
        self.system_dir.mkdir(exist_ok=True)
        
        logger.info(f"ReportGenerator initialized with report directory: {report_dir}")
    
    def generate_daily_report(self, trading_data, ai_data, system_data):
        """
        Generate daily performance report
        
        Parameters:
        trading_data (dict): Trading performance data
        ai_data (dict): AI model performance data
        system_data (dict): System health data
        
        Returns:
        str: Path to the generated report
        """
        try:
            # Get current date
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            
            # Create report data
            report_data = {
                "date": today,
                "timestamp": datetime.datetime.now().isoformat(),
                "trading_performance": trading_data,
                "ai_performance": ai_data,
                "system_health": system_data
            }
            
            # Save report to file
            report_file = self.daily_dir / f"daily_report_{today}.json"
            with open(report_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            # Generate HTML report
            html_report = self._generate_html_report(report_data, "Daily Performance Report")
            html_file = self.daily_dir / f"daily_report_{today}.html"
            with open(html_file, 'w') as f:
                f.write(html_report)
            
            # Generate charts
            self._generate_performance_charts(report_data, self.daily_dir / f"daily_charts_{today}")
            
            logger.info(f"Daily report generated: {report_file}")
            
            return str(report_file)
        
        except Exception as e:
            logger.error(f"Error generating daily report: {e}")
            return None
    
    def generate_weekly_report(self):
        """
        Generate weekly performance report by aggregating daily reports
        
        Returns:
        str: Path to the generated report
        """
        try:
            # Get current date and start of week
            today = datetime.datetime.now()
            start_of_week = today - datetime.timedelta(days=today.weekday())
            end_of_week = start_of_week + datetime.timedelta(days=6)
            
            # Format dates
            start_date = start_of_week.strftime("%Y-%m-%d")
            end_date = end_of_week.strftime("%Y-%m-%d")
            week_str = f"{start_date}_to_{end_date}"
            
            # Collect daily reports for the week
            daily_reports = []
            for i in range(7):
                date = (start_of_week + datetime.timedelta(days=i)).strftime("%Y-%m-%d")
                report_file = self.daily_dir / f"daily_report_{date}.json"
                
                if report_file.exists():
                    with open(report_file, 'r') as f:
                        daily_reports.append(json.load(f))
            
            if not daily_reports:
                logger.warning("No daily reports found for the week")
                return None
            
            # Aggregate data
            weekly_data = self._aggregate_reports(daily_reports)
            
            # Add week information
            weekly_data["start_date"] = start_date
            weekly_data["end_date"] = end_date
            weekly_data["timestamp"] = datetime.datetime.now().isoformat()
            
            # Save report to file
            report_file = self.weekly_dir / f"weekly_report_{week_str}.json"
            with open(report_file, 'w') as f:
                json.dump(weekly_data, f, indent=2)
            
            # Generate HTML report
            html_report = self._generate_html_report(weekly_data, "Weekly Performance Report")
            html_file = self.weekly_dir / f"weekly_report_{week_str}.html"
            with open(html_file, 'w') as f:
                f.write(html_report)
            
            # Generate charts
            self._generate_performance_charts(weekly_data, self.weekly_dir / f"weekly_charts_{week_str}")
            
            logger.info(f"Weekly report generated: {report_file}")
            
            return str(report_file)
        
        except Exception as e:
            logger.error(f"Error generating weekly report: {e}")
            return None
    
    def generate_monthly_report(self):
        """
        Generate monthly performance report by aggregating daily reports
        
        Returns:
        str: Path to the generated report
        """
        try:
            # Get current date and start of month
            today = datetime.datetime.now()
            start_of_month = today.replace(day=1)
            
            # Get next month
            if today.month == 12:
                next_month = today.replace(year=today.year + 1, month=1, day=1)
            else:
                next_month = today.replace(month=today.month + 1, day=1)
            
            # Get end of month
            end_of_month = next_month - datetime.timedelta(days=1)
            
            # Format dates
            month_str = today.strftime("%Y-%m")
            
            # Collect daily reports for the month
            daily_reports = []
            current_day = start_of_month
            
            while current_day <= end_of_month:
                date = current_day.strftime("%Y-%m-%d")
                report_file = self.daily_dir / f"daily_report_{date}.json"
                
                if report_file.exists():
                    with open(report_file, 'r') as f:
                        daily_reports.append(json.load(f))
                
                current_day += datetime.timedelta(days=1)
            
            if not daily_reports:
                logger.warning("No daily reports found for the month")
                return None
            
            # Aggregate data
            monthly_data = self._aggregate_reports(daily_reports)
            
            # Add month information
            monthly_data["month"] = month_str
            monthly_data["timestamp"] = datetime.datetime.now().isoformat()
            
            # Save report to file
            report_file = self.monthly_dir / f"monthly_report_{month_str}.json"
            with open(report_file, 'w') as f:
                json.dump(monthly_data, f, indent=2)
            
            # Generate HTML report
            html_report = self._generate_html_report(monthly_data, "Monthly Performance Report")
            html_file = self.monthly_dir / f"monthly_report_{month_str}.html"
            with open(html_file, 'w') as f:
                f.write(html_report)
            
            # Generate charts
            self._generate_performance_charts(monthly_data, self.monthly_dir / f"monthly_charts_{month_str}")
            
            logger.info(f"Monthly report generated: {report_file}")
            
            return str(report_file)
        
        except Exception as e:
            logger.error(f"Error generating monthly report: {e}")
            return None
    
    def generate_ai_performance_report(self, ai_data):
        """
        Generate AI model performance report
        
        Parameters:
        ai_data (dict): AI model performance data
        
        Returns:
        str: Path to the generated report
        """
        try:
            # Get current date
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            
            # Create report data
            report_data = {
                "date": today,
                "timestamp": datetime.datetime.now().isoformat(),
                "ai_performance": ai_data
            }
            
            # Save report to file
            report_file = self.ai_dir / f"ai_performance_{today}.json"
            with open(report_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            # Generate HTML report
            html_report = self._generate_html_report(report_data, "AI Model Performance Report")
            html_file = self.ai_dir / f"ai_performance_{today}.html"
            with open(html_file, 'w') as f:
                f.write(html_report)
            
            # Generate charts
            self._generate_ai_charts(report_data, self.ai_dir / f"ai_charts_{today}")
            
            logger.info(f"AI performance report generated: {report_file}")
            
            return str(report_file)
        
        except Exception as e:
            logger.error(f"Error generating AI performance report: {e}")
            return None
    
    def generate_system_health_report(self, system_data):
        """
        Generate system health report
        
        Parameters:
        system_data (dict): System health data
        
        Returns:
        str: Path to the generated report
        """
        try:
            # Get current date
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            
            # Create report data
            report_data = {
                "date": today,
                "timestamp": datetime.datetime.now().isoformat(),
                "system_health": system_data
            }
            
            # Save report to file
            report_file = self.system_dir / f"system_health_{today}.json"
            with open(report_file, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            # Generate HTML report
            html_report = self._generate_html_report(report_data, "System Health Report")
            html_file = self.system_dir / f"system_health_{today}.html"
            with open(html_file, 'w') as f:
                f.write(html_report)
            
            # Generate charts
            self._generate_system_charts(report_data, self.system_dir / f"system_charts_{today}")
            
            logger.info(f"System health report generated: {report_file}")
            
            return str(report_file)
        
        except Exception as e:
            logger.error(f"Error generating system health report: {e}")
            return None
    
    def _aggregate_reports(self, reports):
        """
        Aggregate multiple reports into a single report
        
        Parameters:
        reports (list): List of report data dictionaries
        
        Returns:
        dict: Aggregated report data
        """
        # Initialize aggregated data
        aggregated = {
            "trading_performance": {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "profit_loss": 0,
                "win_rate": 0,
                "average_profit": 0,
                "average_loss": 0,
                "largest_profit": 0,
                "largest_loss": 0,
                "trades_by_symbol": {}
            },
            "ai_performance": {
                "openai": {
                    "accuracy": 0,
                    "predictions": 0,
                    "correct_predictions": 0
                },
                "deepseek": {
                    "accuracy": 0,
                    "predictions": 0,
                    "correct_predictions": 0
                },
                "qwen": {
                    "accuracy": 0,
                    "predictions": 0,
                    "correct_predictions": 0
                },
                "debate_mode": {
                    "activations": 0,
                    "correct_decisions": 0,
                    "accuracy": 0
                }
            },
            "system_health": {
                "average_cpu_usage": 0,
                "average_memory_usage": 0,
                "api_errors": 0,
                "network_errors": 0,
                "other_errors": 0
            }
        }
        
        # Aggregate data from each report
        for report in reports:
            trading = report.get("trading_performance", {})
            ai = report.get("ai_performance", {})
            system = report.get("system_health", {})
            
            # Aggregate trading performance
            aggregated["trading_performance"]["total_trades"] += trading.get("total_trades", 0)
            aggregated["trading_performance"]["winning_trades"] += trading.get("winning_trades", 0)
            aggregated["trading_performance"]["losing_trades"] += trading.get("losing_trades", 0)
            aggregated["trading_performance"]["profit_loss"] += trading.get("profit_loss", 0)
            
            # Update largest profit/loss
            if trading.get("largest_profit", 0) > aggregated["trading_performance"]["largest_profit"]:
                aggregated["trading_performance"]["largest_profit"] = trading.get("largest_profit", 0)
            
            if trading.get("largest_loss", 0) < aggregated["trading_performance"]["largest_loss"]:
                aggregated["trading_performance"]["largest_loss"] = trading.get("largest_loss", 0)
            
            # Aggregate trades by symbol
            for symbol, count in trading.get("trades_by_symbol", {}).items():
                if symbol in aggregated["trading_performance"]["trades_by_symbol"]:
                    aggregated["trading_performance"]["trades_by_symbol"][symbol] += count
                else:
                    aggregated["trading_performance"]["trades_by_symbol"][symbol] = count
            
            # Aggregate AI performance
            for model in ["openai", "deepseek", "qwen"]:
                model_data = ai.get(model, {})
                aggregated["ai_performance"][model]["predictions"] += model_data.get("predictions", 0)
                aggregated["ai_performance"][model]["correct_predictions"] += model_data.get("correct_predictions", 0)
            
            # Aggregate debate mode
            debate_data = ai.get("debate_mode", {})
            aggregated["ai_performance"]["debate_mode"]["activations"] += debate_data.get("activations", 0)
            aggregated["ai_performance"]["debate_mode"]["correct_decisions"] += debate_data.get("correct_decisions", 0)
            
            # Aggregate system health
            aggregated["system_health"]["api_errors"] += system.get("api_errors", 0)
            aggregated["system_health"]["network_errors"] += system.get("network_errors", 0)
            aggregated["system_health"]["other_errors"] += system.get("other_errors", 0)
        
        # Calculate averages and rates
        if aggregated["trading_performance"]["total_trades"] > 0:
            aggregated["trading_performance"]["win_rate"] = (
                aggregated["trading_performance"]["winning_trades"] / 
                aggregated["trading_performance"]["total_trades"] * 100
            )
        
        for model in ["openai", "deepseek", "qwen"]:
            if aggregated["ai_performance"][model]["predictions"] > 0:
                aggregated["ai_performance"][model]["accuracy"] = (
                    aggregated["ai_performance"][model]["correct_predictions"] / 
                    aggregated["ai_performance"][model]["predictions"] * 100
                )
        
        if aggregated["ai_performance"]["debate_mode"]["activations"] > 0:
            aggregated["ai_performance"]["debate_mode"]["accuracy"] = (
                aggregated["ai_performance"]["debate_mode"]["correct_decisions"] / 
                aggregated["ai_performance"]["debate_mode"]["activations"] * 100
            )
        
        # Calculate system health averages
        cpu_sum = 0
        memory_sum = 0
        count = 0
        
        for report in reports:
            system = report.get("system_health", {})
            if "cpu_usage" in system:
                cpu_sum += system["cpu_usage"]
                count += 1
            
            if "memory_usage" in system:
                memory_sum += system["memory_usage"]
        
        if count > 0:
            aggregated["system_health"]["average_cpu_usage"] = cpu_sum / count
            aggregated["system_health"]["average_memory_usage"] = memory_sum / count
        
        return aggregated
    
    def _generate_html_report(self, report_data, title):
        """
        Generate HTML report from report data
        
        Parameters:
        report_data (dict): Report data
        title (str): Report title
        
        Returns:
        str: HTML report
        """
        # TODO: Implement HTML report generation
        return "<html><body><h1>Report</h1><pre>" + json.dumps(report_data, indent=2) + "</pre></body></html>"
    
    def _generate_performance_charts(self, report_data, output_prefix):
        """
        Generate performance charts from report data
        
        Parameters:
        report_data (dict): Report data
        output_prefix (str): Prefix for output files
        
        Returns:
        bool: True if charts were generated, False otherwise
        """
        # TODO: Implement performance chart generation
        return True
    
    def _generate_ai_charts(self, report_data, output_prefix):
        """
        Generate AI performance charts from report data
        
        Parameters:
        report_data (dict): Report data
        output_prefix (str): Prefix for output files
        
        Returns:
        bool: True if charts were generated, False otherwise
        """
        # TODO: Implement AI chart generation
        return True
    
    def _generate_system_charts(self, report_data, output_prefix):
        """
        Generate system health charts from report data
        
        Parameters:
        report_data (dict): Report data
        output_prefix (str): Prefix for output files
        
        Returns:
        bool: True if charts were generated, False otherwise
        """
        # TODO: Implement system chart generation
        return True
