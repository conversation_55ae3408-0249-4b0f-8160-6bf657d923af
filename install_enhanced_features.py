#!/usr/bin/env python3
"""
SP.Bot Enhanced v2.0.0 Installation Script
Installs all required dependencies and sets up the enhanced trading bot
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Run a command and handle errors"""
    try:
        logger.info(f"Running: {description or command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running command: {command}")
        logger.error(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def install_core_dependencies():
    """Install core dependencies"""
    logger.info("Installing core dependencies...")
    
    core_packages = [
        "scikit-learn>=1.3.0",
        "joblib>=1.3.0",
        "plotly>=5.15.0",
        "memory-profiler>=0.61.0",
        "jsonschema>=4.19.0",
        "bcrypt>=4.0.0",
        "keyring>=24.2.0",
        "anthropic>=0.3.0",
        "tabulate>=0.9.0"
    ]
    
    for package in core_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            logger.warning(f"Failed to install {package}, continuing...")
    
    return True

def install_ml_dependencies():
    """Install machine learning dependencies"""
    logger.info("Installing ML dependencies...")
    
    ml_packages = [
        "xgboost>=1.7.0",
        "lightgbm>=4.0.0"
    ]
    
    for package in ml_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            logger.warning(f"Failed to install {package}, ML features may be limited")
    
    return True

def install_optional_dependencies():
    """Install optional performance dependencies"""
    logger.info("Installing optional performance dependencies...")
    
    optional_packages = [
        "numba>=0.57.0",
        "cython>=3.0.0"
    ]
    
    for package in optional_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            logger.warning(f"Failed to install {package}, performance optimizations may be limited")
    
    return True

def install_network_dependencies():
    """Install network and time synchronization dependencies"""
    logger.info("Installing network dependencies...")
    
    try:
        # Try to install ntplib
        if not run_command("pip install ntplib>=0.4.0", "Installing ntplib"):
            logger.warning("Failed to install ntplib, time synchronization may be limited")
    except Exception as e:
        logger.warning(f"Error installing network dependencies: {e}")
    
    return True

def create_directories():
    """Create necessary directories"""
    logger.info("Creating necessary directories...")
    
    directories = [
        "data/ml_models",
        "data/ai_performance",
        "reports/backtesting",
        "reports/market_regime",
        "reports/ai_performance",
        "reports/session_reports",
        "logs",
        "config"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")
        except Exception as e:
            logger.error(f"Failed to create directory {directory}: {e}")
    
    return True

def create_default_configs():
    """Create default configuration files if they don't exist"""
    logger.info("Creating default configuration files...")
    
    # Enhanced trading hours config
    trading_hours_config = """
# Enhanced Trading Hours Configuration
trading_sessions:
  asian_session:
    start_hour: 0
    start_minute: 0
    end_hour: 8
    end_minute: 0
    timezone: "UTC"
    active: true
    max_trades: 5
    rest_after_session: 120

  european_session:
    start_hour: 8
    start_minute: 0
    end_hour: 16
    end_minute: 0
    timezone: "UTC"
    active: true
    max_trades: 10
    rest_after_session: 120

  american_session:
    start_hour: 16
    start_minute: 0
    end_hour: 24
    end_minute: 0
    timezone: "UTC"
    active: true
    max_trades: 8
    rest_after_session: 120

rest_periods:
  low_liquidity_1:
    start_hour: 2
    start_minute: 0
    end_hour: 5
    end_minute: 0
    timezone: "UTC"
    reason: "Low liquidity period"
    active: true

symbol_specific_hours:
  BTC/USDT:
    preferred_hours: [9, 10, 11, 13, 14, 15, 16, 17]
    avoid_hours: [2, 3, 4, 5]
    max_trades_per_hour: 2
  ETH/USDT:
    preferred_hours: [8, 9, 10, 14, 15, 16]
    avoid_hours: [2, 3, 4, 5, 22, 23]
    max_trades_per_hour: 3
"""
    
    config_file = "config/enhanced_trading_hours.yaml"
    if not os.path.exists(config_file):
        try:
            with open(config_file, 'w') as f:
                f.write(trading_hours_config)
            logger.info(f"Created {config_file}")
        except Exception as e:
            logger.error(f"Failed to create {config_file}: {e}")
    
    return True

def verify_installation():
    """Verify that key components can be imported"""
    logger.info("Verifying installation...")
    
    test_imports = [
        ("sklearn", "scikit-learn"),
        ("joblib", "joblib"),
        ("plotly", "plotly"),
        ("bcrypt", "bcrypt"),
        ("anthropic", "anthropic")
    ]
    
    failed_imports = []
    
    for module, package in test_imports:
        try:
            __import__(module)
            logger.info(f"✓ {package} imported successfully")
        except ImportError:
            logger.warning(f"✗ Failed to import {package}")
            failed_imports.append(package)
    
    if failed_imports:
        logger.warning(f"Some packages failed to import: {failed_imports}")
        logger.warning("Enhanced features may be limited")
    else:
        logger.info("All core packages imported successfully!")
    
    return len(failed_imports) == 0

def main():
    """Main installation function"""
    logger.info("Starting SP.Bot Enhanced v2.0.0 installation...")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install dependencies
    logger.info("Installing dependencies...")
    install_core_dependencies()
    install_ml_dependencies()
    install_optional_dependencies()
    install_network_dependencies()
    
    # Create default configs
    create_default_configs()
    
    # Verify installation
    success = verify_installation()
    
    if success:
        logger.info("✓ SP.Bot Enhanced v2.0.0 installation completed successfully!")
        logger.info("You can now run the bot with: python enhanced_main.py --mode paper")
    else:
        logger.warning("⚠ Installation completed with some warnings")
        logger.warning("Some enhanced features may not be available")
        logger.info("You can still run the bot with basic features: python enhanced_main.py --mode paper")
    
    # Display next steps
    logger.info("\nNext steps:")
    logger.info("1. Configure your API keys in the .env file")
    logger.info("2. Review and customize config/enhanced_config.yaml")
    logger.info("3. Run in paper trading mode first: python enhanced_main.py --mode paper")
    logger.info("4. Check logs in the logs/ directory for any issues")

if __name__ == "__main__":
    main()
