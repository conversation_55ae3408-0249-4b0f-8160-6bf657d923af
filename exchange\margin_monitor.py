"""
Margin Consumption Monitor
Monitors margin usage and prevents liquidation
"""

import os
import time
import logging
import datetime
from threading import Thread, Event

# Set up logging
logger = logging.getLogger("margin_monitor")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/margin_monitor.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class MarginMonitor:
    """
    Monitors margin usage and prevents liquidation
    """

    def __init__(self, exchange_api, margin_manager, warning_threshold=75, critical_threshold=90, check_interval=30):
        """
        Initialize the margin monitor

        Args:
            exchange_api: Exchange API instance
            margin_manager: Isolated Margin Manager instance
            warning_threshold (float): Warning threshold percentage
            critical_threshold (float): Critical threshold percentage
            check_interval (int): Check interval in seconds
        """
        self.exchange_api = exchange_api
        self.margin_manager = margin_manager
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.check_interval = check_interval
        self.stop_event = Event()
        self.monitor_thread = None
        self.active_symbols = set()
        self.margin_usage = {}
        self.last_check_time = None
        self.callbacks = {
            'warning': [],
            'critical': [],
            'normal': []
        }
        logger.info(f"MarginMonitor initialized with warning threshold: {warning_threshold}%, critical threshold: {critical_threshold}%")

    def start_monitoring(self):
        """
        Start monitoring margin usage
        """
        if self.monitor_thread is None or not self.monitor_thread.is_alive():
            self.stop_event.clear()
            self.monitor_thread = Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("Margin monitoring started")

    def stop_monitoring(self):
        """
        Stop monitoring margin usage
        """
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.stop_event.set()
            self.monitor_thread.join(timeout=5)
            logger.info("Margin monitoring stopped")

    def add_symbol(self, symbol):
        """
        Add a symbol to monitor

        Args:
            symbol (str): Trading pair symbol
        """
        self.active_symbols.add(symbol)
        logger.info(f"Added {symbol} to margin monitoring")

    def remove_symbol(self, symbol):
        """
        Remove a symbol from monitoring

        Args:
            symbol (str): Trading pair symbol
        """
        if symbol in self.active_symbols:
            self.active_symbols.remove(symbol)
            logger.info(f"Removed {symbol} from margin monitoring")

    def register_callback(self, event_type, callback):
        """
        Register a callback for margin events

        Args:
            event_type (str): Event type ('warning', 'critical', 'normal')
            callback (callable): Callback function
        """
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            logger.info(f"Registered callback for {event_type} events")

    def _monitor_loop(self):
        """
        Main monitoring loop
        """
        while not self.stop_event.is_set():
            try:
                self._check_margin_usage()
                self.last_check_time = datetime.datetime.now()
            except Exception as e:
                logger.error(f"Error in margin monitoring: {e}")

            # Sleep until next check
            self.stop_event.wait(self.check_interval)

    def _check_margin_usage(self):
        """
        Check margin usage for all active symbols
        """
        if not self.active_symbols:
            return

        for symbol in self.active_symbols:
            try:
                # Get margin account info
                margin_account = self.margin_manager.get_margin_account(symbol)

                if not margin_account:
                    logger.warning(f"Could not get margin account for {symbol}")
                    # Use last known margin ratio if available, otherwise use a safe default
                    if symbol in self.margin_usage:
                        margin_ratio = self.margin_usage[symbol]
                        logger.info(f"Using last known margin ratio for {symbol}: {margin_ratio:.2f}%")
                    else:
                        margin_ratio = 50.0  # Safe default
                        logger.info(f"Using safe default margin ratio for {symbol}: {margin_ratio:.2f}%")

                    # Store and check the fallback ratio
                    self.margin_usage[symbol] = margin_ratio
                    self._check_thresholds(symbol, margin_ratio)
                    continue

                # Calculate margin ratio
                margin_ratio = self._calculate_margin_ratio(margin_account)

                # Store current usage
                self.margin_usage[symbol] = margin_ratio

                # Check thresholds and trigger callbacks
                self._check_thresholds(symbol, margin_ratio)

                logger.info(f"Margin usage for {symbol}: {margin_ratio:.2f}%")
            except Exception as e:
                logger.error(f"Error checking margin usage for {symbol}: {e}")

                # Implement fallback mechanism for error cases
                try:
                    # Use last known margin ratio if available, otherwise use a safe default
                    if symbol in self.margin_usage:
                        margin_ratio = self.margin_usage[symbol]
                        logger.info(f"Using last known margin ratio for {symbol} after error: {margin_ratio:.2f}%")
                    else:
                        margin_ratio = 50.0  # Safe default
                        logger.info(f"Using safe default margin ratio for {symbol} after error: {margin_ratio:.2f}%")
                        self.margin_usage[symbol] = margin_ratio
                except Exception as fallback_error:
                    logger.error(f"Error in margin monitoring fallback: {fallback_error}")
                    # Last resort fallback - don't update anything

    def _calculate_margin_ratio(self, margin_account):
        """
        Calculate margin ratio from account info

        Args:
            margin_account (dict): Margin account information

        Returns:
            float: Margin ratio percentage
        """
        try:
            # Check if this is a simulated account (our fallback mechanism)
            if margin_account.get('simulated', False):
                # For simulated accounts, use a safe default margin ratio
                # This allows the bot to continue functioning without real margin data
                logger.info("Using simulated margin ratio for safety")
                return 50.0  # 50% is a moderate value that won't trigger critical alerts

            # Extract relevant values
            total_asset_of_btc = float(margin_account.get('totalAssetOfBtc', 0))
            total_liability_of_btc = float(margin_account.get('totalLiabilityOfBtc', 0))

            # Check if we have margin level directly available
            if 'marginLevel' in margin_account:
                try:
                    margin_level = float(margin_account.get('marginLevel', '0'))
                    # Convert margin level to margin ratio (they are inversely related)
                    # Margin level of 200% means 50% of funds are borrowed (ratio of 50%)
                    if margin_level > 0:
                        margin_ratio = (100 / margin_level) * 100
                    else:
                        margin_ratio = 0
                    logger.debug(f"Calculated margin ratio from margin level: {margin_ratio:.2f}%")
                    return margin_ratio
                except (ValueError, TypeError) as e:
                    logger.warning(f"Error converting margin level: {e}")

            # Calculate margin ratio from assets and liabilities
            if total_asset_of_btc > 0:
                margin_ratio = (total_liability_of_btc / total_asset_of_btc) * 100
            else:
                margin_ratio = 0

            # Validate the calculated ratio
            if margin_ratio > 100:
                logger.warning(f"Calculated margin ratio exceeds 100%: {margin_ratio:.2f}%. Capping at 95%.")
                margin_ratio = 95.0  # Cap at 95% to avoid false alarms

            return margin_ratio
        except Exception as e:
            logger.error(f"Error calculating margin ratio: {e}")
            # Return a moderate value as fallback
            return 50.0

    def _check_thresholds(self, symbol, margin_ratio):
        """
        Check margin ratio against thresholds and trigger callbacks

        Args:
            symbol (str): Trading pair symbol
            margin_ratio (float): Margin ratio percentage
        """
        # Critical threshold
        if margin_ratio >= self.critical_threshold:
            logger.critical(f"CRITICAL: Margin usage for {symbol} is {margin_ratio:.2f}% (threshold: {self.critical_threshold}%)")
            for callback in self.callbacks['critical']:
                try:
                    callback(symbol, margin_ratio)
                except Exception as e:
                    logger.error(f"Error in critical callback: {e}")

        # Warning threshold
        elif margin_ratio >= self.warning_threshold:
            logger.warning(f"WARNING: Margin usage for {symbol} is {margin_ratio:.2f}% (threshold: {self.warning_threshold}%)")
            for callback in self.callbacks['warning']:
                try:
                    callback(symbol, margin_ratio)
                except Exception as e:
                    logger.error(f"Error in warning callback: {e}")

        # Normal
        else:
            logger.info(f"Normal margin usage for {symbol}: {margin_ratio:.2f}%")
            for callback in self.callbacks['normal']:
                try:
                    callback(symbol, margin_ratio)
                except Exception as e:
                    logger.error(f"Error in normal callback: {e}")

    def get_current_usage(self, symbol=None):
        """
        Get current margin usage

        Args:
            symbol (str, optional): Trading pair symbol

        Returns:
            dict or float: Margin usage for all symbols or specific symbol
        """
        if symbol:
            return self.margin_usage.get(symbol, 0)
        return self.margin_usage

    def is_safe_to_trade(self, symbol):
        """
        Check if it's safe to trade a symbol

        Args:
            symbol (str): Trading pair symbol

        Returns:
            bool: True if safe to trade, False otherwise
        """
        try:
            # Get current margin ratio
            margin_ratio = self.margin_usage.get(symbol, None)

            # If we don't have margin data for this symbol yet
            if margin_ratio is None:
                # Try to get fresh data
                try:
                    margin_account = self.margin_manager.get_margin_account(symbol)
                    if margin_account:
                        margin_ratio = self._calculate_margin_ratio(margin_account)
                        self.margin_usage[symbol] = margin_ratio
                    else:
                        # No margin data available, use a conservative approach
                        logger.warning(f"No margin data available for {symbol}. Using conservative approach.")
                        return True  # Allow trading but with reduced size (handled by get_safe_position_size)
                except Exception as e:
                    logger.error(f"Error getting fresh margin data for {symbol}: {e}")
                    return True  # Allow trading but with reduced size

            # Check if margin ratio is below warning threshold
            is_safe = margin_ratio < self.warning_threshold

            if not is_safe:
                logger.warning(f"Trading {symbol} is not safe due to high margin usage: {margin_ratio:.2f}%")

            return is_safe
        except Exception as e:
            logger.error(f"Error checking if safe to trade {symbol}: {e}")
            # In case of error, be conservative but allow trading with reduced size
            return True

    def get_safe_position_size(self, symbol, desired_size, buffer=10):
        """
        Get a safe position size that won't exceed margin thresholds

        Args:
            symbol (str): Trading pair symbol
            desired_size (float): Desired position size
            buffer (float): Safety buffer percentage

        Returns:
            float: Safe position size
        """
        try:
            # Get current margin ratio with fallback
            current_ratio = self.margin_usage.get(symbol, None)

            # If we don't have margin data for this symbol yet
            if current_ratio is None:
                try:
                    # Try to get fresh data
                    margin_account = self.margin_manager.get_margin_account(symbol)
                    if margin_account:
                        current_ratio = self._calculate_margin_ratio(margin_account)
                        self.margin_usage[symbol] = current_ratio
                    else:
                        # No margin data available, use a conservative approach
                        logger.warning(f"No margin data available for {symbol}. Using conservative position size.")
                        return desired_size * 0.5  # Use 50% of desired size as a safe default
                except Exception as e:
                    logger.error(f"Error getting fresh margin data for position sizing: {e}")
                    return desired_size * 0.5  # Use 50% of desired size as a safe default

            # Calculate safe threshold
            safe_threshold = self.warning_threshold - buffer

            # Apply dynamic position sizing based on margin usage
            if current_ratio >= self.critical_threshold:
                # Critical level - use minimal position size or no trade
                safe_size = desired_size * 0.1  # 10% of desired size
                logger.critical(f"CRITICAL margin usage ({current_ratio:.2f}%). Reducing position size for {symbol} to 10% of desired size.")
                return max(safe_size, 0.001)  # Ensure minimum viable size

            elif current_ratio >= safe_threshold:
                # Close to warning threshold, reduce position size proportionally
                # The closer to the threshold, the smaller the position
                available_margin = (self.warning_threshold - current_ratio) / buffer if buffer > 0 else 0
                reduction_factor = max(0.2, min(0.8, available_margin))  # Between 20% and 80%

                safe_size = desired_size * reduction_factor
                logger.warning(f"Reducing position size for {symbol} from {desired_size:.8f} to {safe_size:.8f} due to high margin usage ({current_ratio:.2f}%)")
                return safe_size

            # Normal margin levels, use full desired size
            return desired_size

        except Exception as e:
            logger.error(f"Error calculating safe position size: {e}")
            # In case of error, be conservative
            return desired_size * 0.5  # Use 50% of desired size as a safe default
