"""
API Security Module

This module provides utilities for secure API key management and permission verification.
"""

import os
import json
import base64
import logging
import hashlib
import requests
from typing import Dict, Any, Optional, List, Set, Tuple
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

# Configure logging
logger = logging.getLogger(__name__)

class APIKeyManager:
    """
    Secure API key manager with encryption
    """
    
    def __init__(self, key_file: str = "config/api_keys.enc", password_env: str = "API_KEY_PASSWORD"):
        """
        Initialize the API key manager
        
        Args:
            key_file (str): Path to encrypted key file
            password_env (str): Environment variable for encryption password
        """
        self.key_file = key_file
        self.password_env = password_env
        self.keys = {}
        self.fernet = None
        
        # Initialize encryption
        self._init_encryption()
        
        # Load keys
        self._load_keys()
        
        logger.info("APIKeyManager initialized")
    
    def _init_encryption(self) -> None:
        """
        Initialize encryption
        """
        try:
            # Get password from environment variable
            password = os.environ.get(self.password_env)
            
            if not password:
                logger.warning(f"No password found in environment variable {self.password_env}")
                password = "default_password"  # Fallback password
            
            # Convert password to bytes
            password_bytes = password.encode()
            
            # Generate salt
            salt = b"sp_bot_salt"  # Fixed salt for simplicity
            
            # Generate key
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000
            )
            
            key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
            
            # Create Fernet cipher
            self.fernet = Fernet(key)
            
            logger.info("Encryption initialized")
        
        except Exception as e:
            logger.error(f"Error initializing encryption: {e}")
            self.fernet = None
    
    def _load_keys(self) -> None:
        """
        Load encrypted API keys from file
        """
        try:
            # Check if key file exists
            if not os.path.exists(self.key_file):
                logger.warning(f"Key file {self.key_file} not found")
                return
            
            # Check if encryption is initialized
            if not self.fernet:
                logger.error("Encryption not initialized")
                return
            
            # Read encrypted data
            with open(self.key_file, "rb") as f:
                encrypted_data = f.read()
            
            # Decrypt data
            decrypted_data = self.fernet.decrypt(encrypted_data)
            
            # Parse JSON
            self.keys = json.loads(decrypted_data.decode())
            
            logger.info(f"Loaded {len(self.keys)} API keys")
        
        except Exception as e:
            logger.error(f"Error loading API keys: {e}")
            self.keys = {}
    
    def _save_keys(self) -> bool:
        """
        Save API keys to encrypted file
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if encryption is initialized
            if not self.fernet:
                logger.error("Encryption not initialized")
                return False
            
            # Convert keys to JSON
            json_data = json.dumps(self.keys).encode()
            
            # Encrypt data
            encrypted_data = self.fernet.encrypt(json_data)
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.key_file), exist_ok=True)
            
            # Write encrypted data
            with open(self.key_file, "wb") as f:
                f.write(encrypted_data)
            
            logger.info(f"Saved {len(self.keys)} API keys")
            return True
        
        except Exception as e:
            logger.error(f"Error saving API keys: {e}")
            return False
    
    def get_key(self, service: str, key_id: str = "default") -> Optional[str]:
        """
        Get API key for a service
        
        Args:
            service (str): Service name
            key_id (str): Key identifier
            
        Returns:
            str: API key
        """
        try:
            # Check if service exists
            if service not in self.keys:
                logger.warning(f"No keys found for service {service}")
                return None
            
            # Check if key ID exists
            if key_id not in self.keys[service]:
                logger.warning(f"No key found for service {service} with ID {key_id}")
                return None
            
            # Return key
            return self.keys[service][key_id]["api_key"]
        
        except Exception as e:
            logger.error(f"Error getting API key: {e}")
            return None
    
    def get_secret(self, service: str, key_id: str = "default") -> Optional[str]:
        """
        Get API secret for a service
        
        Args:
            service (str): Service name
            key_id (str): Key identifier
            
        Returns:
            str: API secret
        """
        try:
            # Check if service exists
            if service not in self.keys:
                logger.warning(f"No keys found for service {service}")
                return None
            
            # Check if key ID exists
            if key_id not in self.keys[service]:
                logger.warning(f"No key found for service {service} with ID {key_id}")
                return None
            
            # Return secret
            return self.keys[service][key_id].get("api_secret")
        
        except Exception as e:
            logger.error(f"Error getting API secret: {e}")
            return None
    
    def set_key(self, service: str, api_key: str, api_secret: Optional[str] = None, 
                key_id: str = "default", metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Set API key for a service
        
        Args:
            service (str): Service name
            api_key (str): API key
            api_secret (str, optional): API secret
            key_id (str): Key identifier
            metadata (Dict[str, Any], optional): Additional metadata
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Initialize service if it doesn't exist
            if service not in self.keys:
                self.keys[service] = {}
            
            # Set key
            self.keys[service][key_id] = {
                "api_key": api_key,
                "timestamp": int(time.time())
            }
            
            # Set secret if provided
            if api_secret:
                self.keys[service][key_id]["api_secret"] = api_secret
            
            # Set metadata if provided
            if metadata:
                self.keys[service][key_id]["metadata"] = metadata
            
            # Save keys
            return self._save_keys()
        
        except Exception as e:
            logger.error(f"Error setting API key: {e}")
            return False
    
    def delete_key(self, service: str, key_id: str = "default") -> bool:
        """
        Delete API key for a service
        
        Args:
            service (str): Service name
            key_id (str): Key identifier
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if service exists
            if service not in self.keys:
                logger.warning(f"No keys found for service {service}")
                return False
            
            # Check if key ID exists
            if key_id not in self.keys[service]:
                logger.warning(f"No key found for service {service} with ID {key_id}")
                return False
            
            # Delete key
            del self.keys[service][key_id]
            
            # Delete service if no keys left
            if not self.keys[service]:
                del self.keys[service]
            
            # Save keys
            return self._save_keys()
        
        except Exception as e:
            logger.error(f"Error deleting API key: {e}")
            return False
    
    def rotate_key(self, service: str, new_api_key: str, new_api_secret: Optional[str] = None,
                  key_id: str = "default", metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Rotate API key for a service
        
        Args:
            service (str): Service name
            new_api_key (str): New API key
            new_api_secret (str, optional): New API secret
            key_id (str): Key identifier
            metadata (Dict[str, Any], optional): Additional metadata
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get old key for backup
            old_key = None
            if service in self.keys and key_id in self.keys[service]:
                old_key = self.keys[service][key_id].copy()
            
            # Set new key
            success = self.set_key(service, new_api_key, new_api_secret, key_id, metadata)
            
            if success:
                logger.info(f"Rotated API key for service {service} with ID {key_id}")
                
                # Store old key for reference
                if old_key:
                    # Initialize old keys if they don't exist
                    if "old_keys" not in self.keys:
                        self.keys["old_keys"] = {}
                    
                    if service not in self.keys["old_keys"]:
                        self.keys["old_keys"][service] = {}
                    
                    # Store old key with timestamp
                    old_key["rotated_at"] = int(time.time())
                    self.keys["old_keys"][service][f"{key_id}_{old_key['timestamp']}"] = old_key
                    
                    # Save keys
                    self._save_keys()
            
            return success
        
        except Exception as e:
            logger.error(f"Error rotating API key: {e}")
            return False

class BinancePermissionVerifier:
    """
    Verifies Binance API key permissions
    """
    
    def __init__(self, api_key: str, api_secret: str):
        """
        Initialize the permission verifier
        
        Args:
            api_key (str): Binance API key
            api_secret (str): Binance API secret
        """
        self.api_key = api_key
        self.api_secret = api_secret
        
        logger.info("BinancePermissionVerifier initialized")
    
    def verify_permissions(self) -> Tuple[bool, Set[str]]:
        """
        Verify API key permissions
        
        Returns:
            Tuple[bool, Set[str]]: (Success, Permissions)
        """
        try:
            # Call Binance API to check permissions
            headers = {
                "X-MBX-APIKEY": self.api_key
            }
            
            response = requests.get("https://api.binance.com/api/v3/account", headers=headers)
            
            if response.status_code != 200:
                logger.error(f"Error checking API key permissions: {response.text}")
                return False, set()
            
            # Parse response
            data = response.json()
            
            # Extract permissions
            permissions = set(data.get("permissions", []))
            
            # Check for required permissions
            required_permissions = {"SPOT", "MARGIN"}
            has_required = required_permissions.issubset(permissions)
            
            # Check for withdrawal permission (should not have)
            has_withdrawal = "WITHDRAW" in permissions
            
            if has_withdrawal:
                logger.warning("API key has withdrawal permission - this is a security risk")
            
            if not has_required:
                logger.warning(f"API key missing required permissions: {required_permissions - permissions}")
            
            return True, permissions
        
        except Exception as e:
            logger.error(f"Error verifying API key permissions: {e}")
            return False, set()
    
    def verify_trading_permission(self) -> bool:
        """
        Verify API key has trading permission
        
        Returns:
            bool: True if API key has trading permission
        """
        success, permissions = self.verify_permissions()
        
        if not success:
            return False
        
        return "SPOT" in permissions and "MARGIN" in permissions
    
    def verify_no_withdrawal_permission(self) -> bool:
        """
        Verify API key does not have withdrawal permission
        
        Returns:
            bool: True if API key does not have withdrawal permission
        """
        success, permissions = self.verify_permissions()
        
        if not success:
            return False
        
        return "WITHDRAW" not in permissions

# Import time module
import time
