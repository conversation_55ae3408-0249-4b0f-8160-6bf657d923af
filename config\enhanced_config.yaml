# Enhanced Trading Bot Configuration

# General settings
bot_name: "SP.Bot Enhanced"
version: "2.0.0"
description: "Enhanced trading bot with small account optimizations"

# Trading mode
trading_mode: "live"  # Options: live, simulation
simulation_mode: false  # Set to true for paper trading

# API settings
api:
  exchange: "binance"
  use_testnet: false
  key_encryption: true

# Trading pairs
trading_pairs:
  - "BTC/USDT"
  - "ETH/USDT"
  - "BNB/USDT"
  - "SOL/USDT"

default_pair: "BTC/USDT"

# Time intervals
intervals:
  trade_interval: 60  # seconds
  market_update_interval: 5  # seconds
  health_check_interval: 300  # seconds

# AI models
ai_models:
  openai:
    weight: 0.30
    weight_range: [0.25, 0.35]
  deepseek:
    weight: 0.35
    weight_range: [0.30, 0.40]
  qwen:
    weight: 0.35
    weight_range: [0.30, 0.40]

# Debate mode
debate_mode:
  enabled: true
  log_detailed_analyses: true
  extra_weight_for_top_performer: 0.10
  performance_evaluation_days: 7

# Risk management
risk_management:
  normal_account:  # > $50
    risk_percent: 2.0
    max_leverage: 3.0
  small_account:  # $25-$50
    risk_percent: 1.0
    max_leverage: 2.0
  very_small_account:  # < $25
    risk_percent: 0.5
    max_leverage: 1.5
  max_combined_risk_percent: 5.0
  min_balance_usdt: 7.0

# Take profit / stop loss
tp_sl:
  stable_market:
    take_profit: 0.7  # 0.7% take profit in stable market
    stop_loss: 0.8    # 0.8% stop loss in stable market
  volatile_market:
    take_profit: 1.0  # 1.0% take profit in volatile market
    stop_loss: 1.2    # 1.2% stop loss in volatile market

# Stacking entries
stacking_entries:
  normal_account:
    first_entry_percent: 50
    second_entry_percent: 50
  small_account:
    first_entry_percent: 40
    second_entry_percent: 60
  very_small_account:
    first_entry_percent: 30
    second_entry_percent: 70

# Micro-trade mode
micro_trade_mode:
  enabled: true
  small_account_threshold: 25.0
  max_position_size_percent: 0.5

# Trading hours
trading_hours:
  BTC/USDT: 
    start: 9
    end: 17
  ETH/USDT: 
    start: 8
    end: 16
  SOL/USDT: 
    start: 13
    end: 20
  BNB/USDT: 
    start: 10
    end: 18
  DEFAULT:
    start: 0
    end: 24

# Market windows to avoid
market_windows:
  - name: "US Stock Market Open"
    start_hour: 13
    start_minute: 15
    end_hour: 13
    end_minute: 45
  - name: "US Stock Market Close"
    start_hour: 20
    start_minute: 45
    end_hour: 21
    end_minute: 15

# Enhanced technical indicators with new advanced indicators
indicators:
  ema:
    short_period: 50
    long_period: 200
    slope_lookback: 5
    flat_threshold: 0.05
  rsi:
    period: 14
    oversold: 30
    overbought: 70
    breakout_low: 40
    breakout_high: 60
  macd:
    fast_period: 12
    slow_period: 26
    signal_period: 9
  atr:
    period: 14
    currency_multipliers:
      BTC: 1.0
      ETH: 1.5
      SOL: 3.0
      BNB: 1.8
      DEFAULT: 2.0
  bollinger_bands:
    period: 20
    std_dev: 2.0
    use_ema: false
    squeeze_threshold: 0.3
  volume:
    period: 20
    threshold_multiplier: 1.1
  fibonacci:
    levels: [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.618, 2.618]
    lookback_period: 50
  # NEW ADVANCED INDICATORS
  ichimoku:
    enabled: true
    tenkan_period: 9
    kijun_period: 26
    senkou_b_period: 52
  adx:
    enabled: true
    period: 14
    trend_threshold: 20
    strong_trend_threshold: 30
  obv:
    enabled: true
    ma_short_period: 10
    ma_long_period: 20

# System resources
resources:
  ram_threshold: 75
  ram_critical_threshold: 90
  cpu_threshold: 85
  cpu_critical_threshold: 95
  low_power_mode: false  # Set to true for low-power hardware

# Error handling
error_handling:
  max_retries: 5
  retry_delay: 5
  log_errors: true

# ML-Based Market Detection
ml_market_detection:
  enabled: true
  model_path: "data/ml_models/market_classifier.joblib"
  retrain_interval_days: 7
  min_training_samples: 50
  feature_extraction:
    volatility_periods: [5, 10, 20]
    volume_periods: [5, 20]
    indicator_features: ["atr", "rsi", "bollinger_bands"]

# Enhanced Debate Mode
enhanced_debate_mode:
  enabled: true
  dynamic_weight_adjustment: true
  performance_tracking_days: 7
  confidence_threshold: 0.55
  min_models_for_debate: 2
  conflict_resolution: "weighted_confidence"
  debate_logging:
    enabled: true
    detailed_analysis: true
    save_sessions: true
    max_sessions_stored: 100

# Smart Reconnection System
smart_reconnection:
  enabled: true
  max_retries: 10
  initial_delay: 1.0
  max_delay: 300.0
  multiplier: 2.0
  jitter: true
  health_check_interval: 60
  connection_timeout: 5.0

# Enhanced Trading Hours with Rest Periods
enhanced_trading_hours:
  enabled: true
  rest_periods:
    low_liquidity:
      start_hour: 2
      end_hour: 5
      reason: "Low liquidity period"
    forced_rest_after_losses:
      consecutive_losses_trigger: 2
      rest_duration_hours: 1
    session_rest:
      enabled: true
      duration_minutes: 120  # 2 hours between sessions
  timezone_support: true
  default_timezone: "UTC"

# Enhanced Backtesting
backtesting:
  enabled: true
  initial_balance: 10000
  commission_rate: 0.001
  statistical_analysis:
    enabled: true
    calculate_sharpe_ratio: true
    calculate_sortino_ratio: true
    calculate_calmar_ratio: true
    risk_free_rate: 0.02
  reporting:
    generate_charts: true
    save_trade_details: true
    monthly_analysis: true
    regime_performance: true

# Portfolio Risk Management
portfolio_risk_management:
  enabled: true
  max_portfolio_risk_percent: 2.0
  margin_usage_limit_percent: 80.0
  liquidation_buffer_percent: 20.0
  position_correlation_limit: 0.7
  sector_concentration_limit: 50.0

# API Key Security
api_security:
  encryption_enabled: true
  encryption_method: "AES-256"
  key_rotation:
    enabled: true
    rotation_interval_hours: 24
  ram_scrubbing:
    enabled: true
    scrub_on_exit: true
    scrub_interval_minutes: 30

# Enhanced Reporting
reporting:
  save_daily_reports: true
  report_directory: "reports"
  log_level: "INFO"
  enhanced_reports:
    enabled: true
    market_regime_analysis: true
    model_performance_tracking: true
    risk_metrics: true
    statistical_significance: true
    visual_charts: true
  daily_market_regime_report:
    enabled: true
    save_path: "reports/market_regime"
  ai_performance_report:
    enabled: true
    save_path: "reports/ai_performance"
    update_frequency: "daily"
