"""
Unit tests for position sizing functionality.
"""

import unittest
import os
import sys
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from exchange.position_manager import PositionManager


class TestPositionSizing(unittest.TestCase):
    """Test cases for position sizing functionality."""

    def setUp(self):
        """Set up test environment before each test."""
        # Create mock exchange API
        self.mock_exchange = MagicMock()
        
        # Set up mock balance
        self.mock_exchange.get_balance.return_value = {
            'USDT': {'free': 100.0, 'used': 0.0, 'total': 100.0}
        }
        
        # Initialize position manager with mock exchange
        self.position_manager = PositionManager(self.mock_exchange)
    
    def test_calculate_position_size_normal_account(self):
        """Test position size calculation for normal account (>$50)."""
        # Set up mock balance for normal account
        self.mock_exchange.get_balance.return_value = {
            'USDT': {'free': 100.0, 'used': 0.0, 'total': 100.0}
        }
        
        # Calculate position size with 2% risk and 1% stop loss
        position_size = self.position_manager.calculate_position_size("BTC/USDT", 2.0, 1.0)
        
        # Expected position size: $100 * 2% / 1% = $200
        self.assertEqual(position_size, 200.0)
    
    def test_calculate_position_size_small_account(self):
        """Test position size calculation for small account ($25-$50)."""
        # Set up mock balance for small account
        self.mock_exchange.get_balance.return_value = {
            'USDT': {'free': 40.0, 'used': 0.0, 'total': 40.0}
        }
        
        # Calculate position size with 1% risk and 1% stop loss
        position_size = self.position_manager.calculate_position_size("BTC/USDT", 1.0, 1.0)
        
        # Expected position size: $40 * 1% / 1% = $40
        self.assertEqual(position_size, 40.0)
    
    def test_calculate_position_size_very_small_account(self):
        """Test position size calculation for very small account (<$25)."""
        # Set up mock balance for very small account
        self.mock_exchange.get_balance.return_value = {
            'USDT': {'free': 20.0, 'used': 0.0, 'total': 20.0}
        }
        
        # Calculate position size with 0.5% risk and 1% stop loss
        position_size = self.position_manager.calculate_position_size("BTC/USDT", 0.5, 1.0)
        
        # Expected position size: $20 * 0.5% / 1% = $10
        self.assertEqual(position_size, 10.0)
    
    def test_calculate_position_size_below_minimum(self):
        """Test position size calculation below minimum threshold."""
        # Set up mock balance for very small account
        self.mock_exchange.get_balance.return_value = {
            'USDT': {'free': 5.0, 'used': 0.0, 'total': 5.0}
        }
        
        # Calculate position size with 0.5% risk and 1% stop loss
        position_size = self.position_manager.calculate_position_size("BTC/USDT", 0.5, 1.0)
        
        # Expected position size: $0 (below minimum)
        self.assertEqual(position_size, 0)
    
    def test_calculate_risk_percent_normal_market(self):
        """Test risk percentage calculation in normal market conditions."""
        # Calculate risk percentage in normal market
        risk_percent = self.position_manager.calculate_risk_percent(is_volatile=False)
        
        # Expected risk percentage: 2% (normal conditions)
        self.assertEqual(risk_percent, 2.0)
    
    def test_calculate_risk_percent_volatile_market(self):
        """Test risk percentage calculation in volatile market conditions."""
        # Calculate risk percentage in volatile market
        risk_percent = self.position_manager.calculate_risk_percent(is_volatile=True)
        
        # Expected risk percentage: 1% (volatile conditions)
        self.assertEqual(risk_percent, 1.0)
    
    def test_calculate_risk_percent_with_existing_positions(self):
        """Test risk percentage calculation with existing positions."""
        # Add some positions
        self.position_manager.positions = {
            'BTC/USDT': {'risk_percent': 2.0},
            'ETH/USDT': {'risk_percent': 1.0}
        }
        
        # Calculate risk percentage in normal market
        risk_percent = self.position_manager.calculate_risk_percent(is_volatile=False)
        
        # Expected risk percentage: 2% (normal conditions, but limited by remaining risk)
        self.assertEqual(risk_percent, 2.0)
        
        # Add more positions to reach the limit
        self.position_manager.positions = {
            'BTC/USDT': {'risk_percent': 2.0},
            'ETH/USDT': {'risk_percent': 2.0},
            'SOL/USDT': {'risk_percent': 1.0}
        }
        
        # Calculate risk percentage in normal market
        risk_percent = self.position_manager.calculate_risk_percent(is_volatile=False)
        
        # Expected risk percentage: 0% (no remaining risk)
        self.assertEqual(risk_percent, 0)
    
    def test_can_open_position_normal_conditions(self):
        """Test if position can be opened under normal conditions."""
        # Check if position can be opened
        can_open = self.position_manager.can_open_position("BTC/USDT", is_volatile=False)
        
        # Should be able to open position
        self.assertTrue(can_open)
    
    def test_can_open_position_max_positions_reached(self):
        """Test if position can be opened when max positions is reached."""
        # Add max number of positions
        self.position_manager.positions = {
            'BTC/USDT': {'risk_percent': 1.0},
            'ETH/USDT': {'risk_percent': 1.0},
            'SOL/USDT': {'risk_percent': 1.0}
        }
        
        # Check if position can be opened
        can_open = self.position_manager.can_open_position("BNB/USDT", is_volatile=False)
        
        # Should not be able to open position
        self.assertFalse(can_open)
    
    def test_can_open_position_symbol_exists(self):
        """Test if position can be opened when symbol already exists."""
        # Add position for BTC/USDT
        self.position_manager.positions = {
            'BTC/USDT': {'risk_percent': 1.0}
        }
        
        # Check if position can be opened for BTC/USDT
        can_open = self.position_manager.can_open_position("BTC/USDT", is_volatile=False)
        
        # Should not be able to open position
        self.assertFalse(can_open)
    
    def test_can_open_position_insufficient_balance(self):
        """Test if position can be opened with insufficient balance."""
        # Set up mock balance for very small account
        self.mock_exchange.get_balance.return_value = {
            'USDT': {'free': 5.0, 'used': 0.0, 'total': 5.0}
        }
        
        # Check if position can be opened
        can_open = self.position_manager.can_open_position("BTC/USDT", is_volatile=False)
        
        # Should not be able to open position
        self.assertFalse(can_open)
    
    def test_can_open_position_trading_frozen(self):
        """Test if position can be opened when trading is frozen."""
        # Freeze trading
        import datetime
        self.position_manager.trading_freeze_until = datetime.datetime.now() + datetime.timedelta(hours=1)
        
        # Check if position can be opened
        can_open = self.position_manager.can_open_position("BTC/USDT", is_volatile=False)
        
        # Should not be able to open position
        self.assertFalse(can_open)
    
    def test_can_open_position_volatility_suspended(self):
        """Test if position can be opened when trading is suspended due to volatility."""
        # Suspend trading due to volatility
        import datetime
        self.position_manager.volatility_suspension_until = datetime.datetime.now() + datetime.timedelta(minutes=5)
        
        # Check if position can be opened
        can_open = self.position_manager.can_open_position("BTC/USDT", is_volatile=False)
        
        # Should not be able to open position
        self.assertFalse(can_open)
    
    def test_open_position_stacked_entry(self):
        """Test opening a position with stacked entry."""
        # Set up mock market data
        market_data = {
            'high_prices': [30100, 30200, 30300],
            'low_prices': [29900, 29800, 29700]
        }
        
        # Open initial position
        position = self.position_manager.open_position(
            symbol="BTC/USDT",
            side="buy",
            entry_price=30000,
            amount=50,
            market_data=market_data,
            risk_percent=2.0,
            stop_loss_percent=1.0,
            is_stacked_entry=False
        )
        
        # Check that position was opened correctly
        self.assertIsNotNone(position)
        self.assertEqual(position['symbol'], "BTC/USDT")
        self.assertEqual(position['side'], "buy")
        self.assertEqual(position['amount'], 50)
        self.assertEqual(position['is_initial_entry'], True)
        self.assertEqual(position['entry_confirmed'], False)
        
        # Open stacked entry
        stacked_position = self.position_manager.open_position(
            symbol="BTC/USDT",
            side="buy",
            entry_price=30100,
            amount=50,
            market_data=market_data,
            risk_percent=2.0,
            stop_loss_percent=1.0,
            is_stacked_entry=True
        )
        
        # Check that stacked entry was added correctly
        self.assertIsNotNone(stacked_position)
        self.assertEqual(stacked_position['entry_confirmed'], True)
        self.assertEqual(len(stacked_position['stacked_entries']), 1)
        
        # Check that average entry price was calculated correctly
        self.assertAlmostEqual(stacked_position['avg_entry_price'], 30050, places=2)


if __name__ == "__main__":
    unittest.main()
