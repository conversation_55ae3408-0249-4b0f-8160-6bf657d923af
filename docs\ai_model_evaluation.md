# AI Model Evaluation System

## Overview

The AI Model Evaluation system tracks and evaluates the performance of different AI models used in the trading bot. It automatically adjusts the weights of each model based on their historical accuracy, giving more weight to models that have performed better in the past.

## Features

- **Performance Tracking**: Records predictions from each AI model and verifies them against actual outcomes
- **Automatic Weight Adjustment**: Adjusts model weights based on historical accuracy
- **Debate Mode**: Special mode activated when models disagree, giving more weight to historically accurate models
- **Daily Performance Reports**: Generates daily reports on model performance
- **Cross-Source Signal Verification**: Verifies signals across multiple AI sources before making trading decisions

## Components

### AIModelEvaluator

The core component of the evaluation system is the `AIModelEvaluator` class, which:

- Tracks predictions from each AI model
- Verifies predictions against actual outcomes
- Calculates accuracy for each model
- Adjusts weights based on performance
- Provides debate mode weights when models disagree
- Generates daily performance reports

### Integration with AI Connectors

The evaluation system is integrated with the AI connectors:

- `MultiAIConnector`: Uses the model evaluator to record predictions and adjust weights
- `SimpleSignalAggregator`: Uses the model evaluator for signal aggregation
- `SignalAggregator`: Uses the model evaluator for signal aggregation

## How It Works

1. **Recording Predictions**:
   - When an AI model makes a prediction, it's recorded with the model name, prediction, and confidence level
   - The prediction is stored in the model evaluator's history

2. **Verifying Predictions**:
   - When the actual outcome is known, the prediction is verified as correct or incorrect
   - The verification is stored in the model evaluator's history

3. **Evaluating Performance**:
   - Once per day, the model evaluator calculates the accuracy of each model
   - Accuracy is calculated as the percentage of correct predictions
   - Weights are adjusted based on relative performance

4. **Adjusting Weights**:
   - Models with above-average accuracy get increased weights
   - Models with below-average accuracy get decreased weights
   - Weights are normalized to sum to 1

5. **Debate Mode**:
   - When models disagree on a recommendation, debate mode is activated
   - Historical accuracy is used to give more weight to more accurate models
   - A weighted decision is made based on the adjusted weights

## Configuration

The model evaluator can be configured with:

- **Base Weights**: Initial weights for each model
- **Evaluation Window**: How far back to look for performance evaluation
- **Minimum Predictions**: Minimum number of predictions needed for evaluation
- **Weight Adjustment Factor**: Maximum weight adjustment per evaluation

## Usage

```python
# Initialize model evaluator
from ai_services.model_evaluator import AIModelEvaluator

evaluator = AIModelEvaluator(base_weights={
    "openai": 0.30,
    "deepseek": 0.35,
    "qwen": 0.35
})

# Record a prediction
evaluator.record_prediction("openai", "buy", 0.85)

# Verify a prediction
evaluator.verify_prediction("openai", 0, True)  # True if correct, False if incorrect

# Evaluate performance
updated_weights = evaluator.evaluate_performance()

# Get debate weights when models disagree
signals = {
    "openai": {"analysis": {"recommendation": "buy", "confidence": 80}},
    "deepseek": {"analysis": {"recommendation": "sell", "confidence": 75}},
    "qwen": {"analysis": {"recommendation": "hold", "confidence": 85}}
}
debate_weights = evaluator.get_debate_weights(signals)

# Generate daily report
report = evaluator.generate_daily_report()
```

## Benefits

- **Improved Decision Making**: By giving more weight to more accurate models, the trading bot makes better decisions
- **Automatic Adaptation**: The system automatically adapts to changing model performance
- **Transparency**: Performance reports provide transparency into model performance
- **Reduced Risk**: By identifying and reducing the influence of underperforming models, risk is reduced
