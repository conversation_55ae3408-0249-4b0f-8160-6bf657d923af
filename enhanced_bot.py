#!/usr/bin/env python
"""
Enhanced Trading Bot

This module integrates all the enhancements for improved performance with small accounts.
"""

import os
import sys
import time
import yaml
import logging
import argparse
import datetime
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/enhanced_bot.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Import enhanced modules
from simulation.paper_trading import PaperTradingSimulator
from simulation.simulator_integration import is_simulation_mode, initialize_simulator, adapt_bot_for_simulation
from ai_services.debate_enhancer import DebateEnhancer
from strategies.dynamic_tp_sl import DynamicTPSL
from strategies.micro_trade import MicroTradeManager
from strategies.trading_hours import TradingHoursManager
from core.resource_optimizer import ResourceOptimizer
from exchange.binance_error_handler import BinanceError<PERSON>and<PERSON>

def parse_arguments():
    """
    Parse command line arguments
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(description="Enhanced Trading Bot")
    parser.add_argument("--config", type=str, default="config/enhanced_config.yaml", help="Path to configuration file")
    parser.add_argument("--simulation", action="store_true", help="Run in simulation mode (paper trading)")
    parser.add_argument("--low-power", action="store_true", help="Run in low-power mode for resource-constrained hardware")
    return parser.parse_args()

def load_config(config_path):
    """
    Load configuration from YAML file
    
    Args:
        config_path (str): Path to configuration file
        
    Returns:
        dict: Configuration parameters
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return {}

def initialize_components(config, simulation_mode=False, low_power_mode=False):
    """
    Initialize bot components
    
    Args:
        config (dict): Configuration parameters
        simulation_mode (bool): Whether to run in simulation mode
        low_power_mode (bool): Whether to run in low-power mode
        
    Returns:
        dict: Initialized components
    """
    components = {}
    
    # Initialize resource optimizer
    resource_config = config.get("resources", {})
    resource_config["low_power_mode"] = low_power_mode
    components["resource_optimizer"] = ResourceOptimizer(resource_config)
    
    # Start resource monitoring
    components["resource_optimizer"].start_monitoring()
    
    # Apply low-power optimizations if needed
    if low_power_mode:
        components["resource_optimizer"].optimize_for_low_power()
    
    # Initialize Binance error handler
    error_config = config.get("error_handling", {})
    components["error_handler"] = BinanceErrorHandler(
        max_retries=error_config.get("max_retries", 5),
        retry_delay=error_config.get("retry_delay", 5)
    )
    
    # Initialize debate enhancer
    components["debate_enhancer"] = DebateEnhancer()
    
    # Initialize dynamic TP/SL manager
    components["tp_sl_manager"] = DynamicTPSL()
    
    # Initialize micro-trade manager
    components["micro_trade_manager"] = MicroTradeManager()
    
    # Initialize trading hours manager
    components["trading_hours_manager"] = TradingHoursManager()
    
    # Initialize simulator if in simulation mode
    if simulation_mode:
        components["simulator"] = initialize_simulator()
    
    return components

def main():
    """
    Main function
    """
    # Parse command line arguments
    args = parse_arguments()
    
    # Load configuration
    config = load_config(args.config)
    
    # Check if simulation mode is enabled
    simulation_mode = args.simulation or is_simulation_mode() or config.get("simulation_mode", False)
    
    # Check if low-power mode is enabled
    low_power_mode = args.low_power or config.get("resources", {}).get("low_power_mode", False)
    
    # Log startup information
    logger.info(f"Starting Enhanced Trading Bot")
    logger.info(f"Mode: {'SIMULATION' if simulation_mode else 'LIVE'}")
    logger.info(f"Low-power mode: {'ENABLED' if low_power_mode else 'DISABLED'}")
    
    try:
        # Initialize components
        components = initialize_components(config, simulation_mode, low_power_mode)
        
        # Check Binance API health
        if not simulation_mode:
            api_healthy = components["resource_optimizer"].check_binance_api_health()
            if not api_healthy:
                logger.warning("Binance API health check failed. Continuing with caution.")
        
        # Main loop
        logger.info("Entering main loop")
        
        while True:
            try:
                # Get current time
                now = datetime.datetime.now()
                
                # Log system health
                if now.second % 30 == 0:  # Every 30 seconds
                    system_info = components["resource_optimizer"].get_system_info()
                    logger.info(f"System health: RAM {system_info['memory']['usage_percent']}%, CPU {system_info['cpu']['usage_percent']}%")
                
                # Sleep to avoid high CPU usage
                time.sleep(1)
                
            except KeyboardInterrupt:
                logger.info("Keyboard interrupt detected. Shutting down...")
                break
            
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                time.sleep(5)
    
    except Exception as e:
        logger.error(f"Error during initialization: {e}")
    
    finally:
        # Cleanup
        logger.info("Shutting down...")
        
        # Stop resource monitoring
        if "resource_optimizer" in components:
            components["resource_optimizer"].stop_monitoring()
        
        logger.info("Shutdown complete")

if __name__ == "__main__":
    main()
