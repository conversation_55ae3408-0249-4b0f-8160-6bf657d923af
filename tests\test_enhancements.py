#!/usr/bin/env python
"""
Test Script for Trading Bot Enhancements

This script tests the enhancements made to the trading bot.
"""

import os
import sys
import time
import yaml
import logging
import datetime
import unittest
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/test_enhancements.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Import enhanced modules
from simulation.paper_trading import PaperTradingSimulator
from ai_services.debate_enhancer import DebateEnhancer
from strategies.dynamic_tp_sl import DynamicTPSL
from strategies.micro_trade import MicroTradeManager
from strategies.trading_hours import TradingHoursManager
from core.resource_optimizer import ResourceOptimizer
from exchange.binance_error_handler import BinanceErrorHandler

class TestPaperTradingMode(unittest.TestCase):
    """Test paper trading mode"""
    
    def setUp(self):
        """Set up test"""
        self.simulator = PaperTradingSimulator()
    
    def test_execute_trade(self):
        """Test execute_trade method"""
        # Execute a buy trade
        trade = self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.001,
            price=50000.0,
            stop_loss=49000.0,
            take_profit=51000.0
        )
        
        # Check trade
        self.assertIsNotNone(trade)
        self.assertEqual(trade["symbol"], "BTC/USDT")
        self.assertEqual(trade["side"], "buy")
        self.assertEqual(trade["quantity"], 0.001)
        self.assertEqual(trade["price"], 50000.0)
        self.assertEqual(trade["stop_loss"], 49000.0)
        self.assertEqual(trade["take_profit"], 51000.0)
        
        # Check balance
        self.assertEqual(self.simulator.balance, 50.0 - (0.001 * 50000.0))
        
        # Execute a sell trade
        sell_trade = self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="sell",
            quantity=0.001,
            price=51000.0
        )
        
        # Check sell trade
        self.assertIsNotNone(sell_trade)
        self.assertEqual(sell_trade["symbol"], "BTC/USDT")
        self.assertEqual(sell_trade["side"], "sell")
        
        # Check P&L
        self.assertEqual(len(self.simulator.trades), 2)
        self.assertEqual(len(self.simulator.open_positions), 0)
    
    def test_update_market_price(self):
        """Test update_market_price method"""
        # Execute a buy trade
        trade = self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.001,
            price=50000.0,
            stop_loss=49000.0,
            take_profit=51000.0
        )
        
        # Update market price (no trigger)
        triggered_trades = self.simulator.update_market_price("BTC/USDT", 50500.0)
        self.assertEqual(len(triggered_trades), 0)
        
        # Update market price (take profit trigger)
        triggered_trades = self.simulator.update_market_price("BTC/USDT", 51000.0)
        self.assertEqual(len(triggered_trades), 1)
        self.assertEqual(triggered_trades[0]["reason"], "take_profit")
    
    def test_generate_report(self):
        """Test generate_report method"""
        # Execute some trades
        self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="buy",
            quantity=0.001,
            price=50000.0,
            stop_loss=49000.0,
            take_profit=51000.0
        )
        
        self.simulator.execute_trade(
            symbol="BTC/USDT",
            side="sell",
            quantity=0.001,
            price=51000.0
        )
        
        # Generate report
        report = self.simulator.generate_report()
        
        # Check report
        self.assertIsNotNone(report)
        self.assertEqual(report["trade_statistics"]["total_trades"], 2)
        self.assertEqual(report["trade_statistics"]["completed_trades"], 1)

class TestDebateMode(unittest.TestCase):
    """Test enhanced debate mode"""
    
    def setUp(self):
        """Set up test"""
        self.debate_enhancer = DebateEnhancer()
    
    def test_record_model_accuracy(self):
        """Test record_model_accuracy method"""
        # Record model accuracy
        model_accuracies = {
            "openai": 0.72,
            "deepseek": 0.68,
            "qwen": 0.65
        }
        
        file_path = self.debate_enhancer.record_model_accuracy(model_accuracies)
        
        # Check file path
        self.assertIsNotNone(file_path)
        self.assertTrue(os.path.exists(file_path))
    
    def test_get_debate_weights(self):
        """Test get_debate_weights method"""
        # Record model accuracy
        model_accuracies = {
            "openai": 0.72,
            "deepseek": 0.68,
            "qwen": 0.65
        }
        
        self.debate_enhancer.record_model_accuracy(model_accuracies)
        
        # Get debate weights
        base_weights = {
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        }
        
        debate_weights = self.debate_enhancer.get_debate_weights(base_weights)
        
        # Check weights
        self.assertIsNotNone(debate_weights)
        self.assertEqual(sum(debate_weights.values()), 1.0)
        
        # Check if top model got extra weight
        top_model = max(model_accuracies, key=model_accuracies.get)
        self.assertGreater(debate_weights[top_model], base_weights[top_model])

class TestDynamicTPSL(unittest.TestCase):
    """Test dynamic TP/SL system"""
    
    def setUp(self):
        """Set up test"""
        self.tp_sl_manager = DynamicTPSL()
    
    def test_determine_market_condition(self):
        """Test determine_market_condition method"""
        # Test stable market
        condition = self.tp_sl_manager.determine_market_condition(0.5, 0.8, "BTC")
        self.assertEqual(condition, "stable")
        
        # Test volatile market
        condition = self.tp_sl_manager.determine_market_condition(2.0, 2.0, "BTC")
        self.assertEqual(condition, "volatile")
        
        # Test with different currency
        condition = self.tp_sl_manager.determine_market_condition(0.5, 0.8, "SOL")
        self.assertEqual(condition, "stable")
        
        condition = self.tp_sl_manager.determine_market_condition(0.5, 4.0, "SOL")
        self.assertEqual(condition, "volatile")
    
    def test_get_tp_sl_levels(self):
        """Test get_tp_sl_levels method"""
        # Test stable market
        tp, sl = self.tp_sl_manager.get_tp_sl_levels("stable", 50000.0, "buy")
        self.assertEqual(tp, 50000.0 * 1.007)  # 0.7% take profit
        self.assertEqual(sl, 50000.0 * 0.992)  # 0.8% stop loss
        
        # Test volatile market
        tp, sl = self.tp_sl_manager.get_tp_sl_levels("volatile", 50000.0, "buy")
        self.assertEqual(tp, 50000.0 * 1.01)  # 1.0% take profit
        self.assertEqual(sl, 50000.0 * 0.988)  # 1.2% stop loss
        
        # Test sell side
        tp, sl = self.tp_sl_manager.get_tp_sl_levels("stable", 50000.0, "sell")
        self.assertEqual(tp, 50000.0 * 0.993)  # 0.7% take profit
        self.assertEqual(sl, 50000.0 * 1.008)  # 0.8% stop loss

class TestMicroTradeMode(unittest.TestCase):
    """Test micro-trade mode"""
    
    def setUp(self):
        """Set up test"""
        self.micro_trade_manager = MicroTradeManager()
    
    def test_determine_account_type(self):
        """Test determine_account_type method"""
        # Test very small account
        account_type = self.micro_trade_manager.determine_account_type(20.0)
        self.assertEqual(account_type, "very_small_account")
        
        # Test small account
        account_type = self.micro_trade_manager.determine_account_type(30.0)
        self.assertEqual(account_type, "small_account")
        
        # Test normal account
        account_type = self.micro_trade_manager.determine_account_type(60.0)
        self.assertEqual(account_type, "normal_account")
    
    def test_calculate_position_size(self):
        """Test calculate_position_size method"""
        # Test very small account
        position_size = self.micro_trade_manager.calculate_position_size(20.0, 50000.0, 49500.0)
        self.assertLess(position_size, 0.0001)  # Very small position
        
        # Test small account
        position_size = self.micro_trade_manager.calculate_position_size(30.0, 50000.0, 49500.0)
        self.assertGreater(position_size, 0.0001)  # Larger position
        
        # Test normal account
        position_size = self.micro_trade_manager.calculate_position_size(60.0, 50000.0, 49500.0)
        self.assertGreater(position_size, 0.0002)  # Even larger position
    
    def test_get_leverage(self):
        """Test get_leverage method"""
        # Test very small account
        leverage = self.micro_trade_manager.get_leverage(20.0)
        self.assertEqual(leverage, 1.5)
        
        # Test small account
        leverage = self.micro_trade_manager.get_leverage(30.0)
        self.assertEqual(leverage, 2.0)
        
        # Test normal account
        leverage = self.micro_trade_manager.get_leverage(60.0)
        self.assertEqual(leverage, 3.0)
    
    def test_get_stacking_entry_percentages(self):
        """Test get_stacking_entry_percentages method"""
        # Test very small account
        first, second = self.micro_trade_manager.get_stacking_entry_percentages(20.0)
        self.assertEqual(first, 30)
        self.assertEqual(second, 70)
        
        # Test small account
        first, second = self.micro_trade_manager.get_stacking_entry_percentages(30.0)
        self.assertEqual(first, 40)
        self.assertEqual(second, 60)
        
        # Test normal account
        first, second = self.micro_trade_manager.get_stacking_entry_percentages(60.0)
        self.assertEqual(first, 50)
        self.assertEqual(second, 50)

class TestTradingHours(unittest.TestCase):
    """Test trading hours"""
    
    def setUp(self):
        """Set up test"""
        self.trading_hours_manager = TradingHoursManager()
    
    def test_is_trading_time(self):
        """Test is_trading_time method"""
        # Mock current hour
        current_hour = datetime.datetime.utcnow().hour
        
        # Test BTC/USDT
        if 9 <= current_hour < 17:
            self.assertTrue(self.trading_hours_manager.is_trading_time("BTC/USDT"))
        else:
            self.assertFalse(self.trading_hours_manager.is_trading_time("BTC/USDT"))
        
        # Test default
        self.assertTrue(self.trading_hours_manager.is_trading_time("XRP/USDT"))
    
    def test_avoid_market_open_close(self):
        """Test avoid_market_open_close method"""
        # This test depends on the current time
        # Just check that it returns a boolean
        result = self.trading_hours_manager.avoid_market_open_close()
        self.assertIsInstance(result, bool)

class TestResourceOptimizer(unittest.TestCase):
    """Test resource optimizer"""
    
    def setUp(self):
        """Set up test"""
        self.resource_optimizer = ResourceOptimizer()
    
    def test_get_system_info(self):
        """Test get_system_info method"""
        # Get system info
        system_info = self.resource_optimizer.get_system_info()
        
        # Check system info
        self.assertIsNotNone(system_info)
        self.assertIn("cpu", system_info)
        self.assertIn("memory", system_info)
        self.assertIn("disk", system_info)
    
    def test_check_binance_api_health(self):
        """Test check_binance_api_health method"""
        # Check Binance API health
        is_healthy = self.resource_optimizer.check_binance_api_health()
        
        # Check result
        self.assertIsInstance(is_healthy, bool)

class TestBinanceErrorHandler(unittest.TestCase):
    """Test Binance error handler"""
    
    def setUp(self):
        """Set up test"""
        self.error_handler = BinanceErrorHandler()
    
    def test_log_error(self):
        """Test log_error method"""
        # Log an error
        error_entry = self.error_handler.log_error(
            error_type="TestError",
            error_message="Test error message",
            context={"test": True}
        )
        
        # Check error entry
        self.assertIsNotNone(error_entry)
        self.assertEqual(error_entry["type"], "TestError")
        self.assertEqual(error_entry["message"], "Test error message")
        self.assertEqual(error_entry["context"], {"test": True})
    
    def test_get_error_statistics(self):
        """Test get_error_statistics method"""
        # Log some errors
        self.error_handler.log_error("TestError1", "Test error message 1")
        self.error_handler.log_error("TestError2", "Test error message 2")
        self.error_handler.log_error("TestError1", "Test error message 3")
        
        # Get error statistics
        statistics = self.error_handler.get_error_statistics()
        
        # Check statistics
        self.assertIsNotNone(statistics)
        self.assertEqual(statistics["total_errors"], 3)
        self.assertEqual(statistics["error_types"]["TestError1"], 2)
        self.assertEqual(statistics["error_types"]["TestError2"], 1)

def run_tests():
    """Run tests"""
    unittest.main()

if __name__ == "__main__":
    run_tests()
