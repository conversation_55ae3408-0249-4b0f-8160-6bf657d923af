"""
Unit tests for trading hours functionality.
"""

import unittest
import os
import sys
import datetime
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategies.trading_hours import TradingHoursManager


class TestTradingHours(unittest.TestCase):
    """Test cases for trading hours functionality."""

    def setUp(self):
        """Set up test environment before each test."""
        # Create a mock config for testing
        self.mock_config = {
            "trading_hours": {
                "BTC/USDT": {"start": 9, "end": 17},
                "ETH/USDT": {"start": 8, "end": 16},
                "SOL/USDT": {"start": 13, "end": 20},
                "BNB/USDT": {"start": 10, "end": 18},
                "DEFAULT": {"start": 0, "end": 24}
            },
            "market_windows": [
                {
                    "name": "US Stock Market Open",
                    "start_hour": 13,
                    "start_minute": 15,
                    "end_hour": 13,
                    "end_minute": 45
                },
                {
                    "name": "US Stock Market Close",
                    "start_hour": 20,
                    "start_minute": 45,
                    "end_hour": 21,
                    "end_minute": 15
                }
            ]
        }
        
        # Patch the _load_config method to return our mock config
        with patch('strategies.trading_hours.TradingHoursManager._load_config', return_value=self.mock_config):
            self.trading_hours_manager = TradingHoursManager()
    
    def test_initialization(self):
        """Test that the trading hours manager initializes correctly."""
        self.assertEqual(self.trading_hours_manager.trading_hours["BTC/USDT"]["start"], 9)
        self.assertEqual(self.trading_hours_manager.trading_hours["BTC/USDT"]["end"], 17)
        self.assertEqual(len(self.trading_hours_manager.market_windows), 2)
    
    @patch('datetime.datetime')
    def test_is_trading_time_within_hours(self, mock_datetime):
        """Test if current time is within trading hours."""
        # Set current hour to 10 (within BTC/USDT trading hours)
        mock_datetime.utcnow.return_value = datetime.datetime(2023, 6, 15, 10, 0, 0)
        
        # Check if it's trading time for BTC/USDT
        is_trading_time = self.trading_hours_manager.is_trading_time("BTC/USDT")
        
        # Should be trading time
        self.assertTrue(is_trading_time)
    
    @patch('datetime.datetime')
    def test_is_trading_time_outside_hours(self, mock_datetime):
        """Test if current time is outside trading hours."""
        # Set current hour to 8 (outside BTC/USDT trading hours)
        mock_datetime.utcnow.return_value = datetime.datetime(2023, 6, 15, 8, 0, 0)
        
        # Check if it's trading time for BTC/USDT
        is_trading_time = self.trading_hours_manager.is_trading_time("BTC/USDT")
        
        # Should not be trading time
        self.assertFalse(is_trading_time)
    
    @patch('datetime.datetime')
    def test_is_trading_time_default_hours(self, mock_datetime):
        """Test if current time is within default trading hours."""
        # Set current hour to 3 (within DEFAULT trading hours)
        mock_datetime.utcnow.return_value = datetime.datetime(2023, 6, 15, 3, 0, 0)
        
        # Check if it's trading time for XRP/USDT (not explicitly defined)
        is_trading_time = self.trading_hours_manager.is_trading_time("XRP/USDT")
        
        # Should be trading time (using DEFAULT hours)
        self.assertTrue(is_trading_time)
    
    @patch('datetime.datetime')
    def test_is_market_open_close_window_within_window(self, mock_datetime):
        """Test if current time is within a market open/close window."""
        # Set current time to US Stock Market Open window
        mock_datetime.utcnow.return_value = datetime.datetime(2023, 6, 15, 13, 30, 0)
        
        # Check if it's within a market window
        is_window, window_name = self.trading_hours_manager.is_market_open_close_window()
        
        # Should be within a market window
        self.assertTrue(is_window)
        self.assertEqual(window_name, "US Stock Market Open")
    
    @patch('datetime.datetime')
    def test_is_market_open_close_window_outside_window(self, mock_datetime):
        """Test if current time is outside market open/close windows."""
        # Set current time outside any market window
        mock_datetime.utcnow.return_value = datetime.datetime(2023, 6, 15, 15, 0, 0)
        
        # Check if it's within a market window
        is_window, window_name = self.trading_hours_manager.is_market_open_close_window()
        
        # Should not be within a market window
        self.assertFalse(is_window)
        self.assertIsNone(window_name)
    
    @patch('datetime.datetime')
    def test_avoid_market_open_close_within_window(self, mock_datetime):
        """Test if trading should be avoided during market open/close."""
        # Set current time to US Stock Market Open window
        mock_datetime.utcnow.return_value = datetime.datetime(2023, 6, 15, 13, 30, 0)
        
        # Check if trading should be avoided
        avoid_trading = self.trading_hours_manager.avoid_market_open_close()
        
        # Should avoid trading
        self.assertTrue(avoid_trading)
    
    @patch('datetime.datetime')
    def test_avoid_market_open_close_outside_window(self, mock_datetime):
        """Test if trading should be avoided outside market open/close."""
        # Set current time outside any market window
        mock_datetime.utcnow.return_value = datetime.datetime(2023, 6, 15, 15, 0, 0)
        
        # Check if trading should be avoided
        avoid_trading = self.trading_hours_manager.avoid_market_open_close()
        
        # Should not avoid trading
        self.assertFalse(avoid_trading)
    
    def test_get_optimal_trading_hours(self):
        """Test getting optimal trading hours for a symbol."""
        # Get optimal trading hours for BTC/USDT
        optimal_hours = self.trading_hours_manager.get_optimal_trading_hours("BTC/USDT")
        
        # Check that optimal hours exclude market open/close windows
        self.assertIn(10, optimal_hours)  # 10 AM is within BTC/USDT hours and not in any market window
        self.assertIn(11, optimal_hours)  # 11 AM is within BTC/USDT hours and not in any market window
        self.assertIn(12, optimal_hours)  # 12 PM is within BTC/USDT hours and not in any market window
        
        # 13 (1 PM) might be excluded due to US Stock Market Open window
        if 13 in optimal_hours:
            # If 13 is included, it's because the market window doesn't cover the entire hour
            pass
        
        self.assertIn(14, optimal_hours)  # 2 PM is within BTC/USDT hours and not in any market window
        self.assertIn(15, optimal_hours)  # 3 PM is within BTC/USDT hours and not in any market window
        self.assertIn(16, optimal_hours)  # 4 PM is within BTC/USDT hours and not in any market window
        
        # Check that hours outside trading hours are not included
        self.assertNotIn(8, optimal_hours)   # 8 AM is outside BTC/USDT hours
        self.assertNotIn(18, optimal_hours)  # 6 PM is outside BTC/USDT hours


if __name__ == "__main__":
    unittest.main()
