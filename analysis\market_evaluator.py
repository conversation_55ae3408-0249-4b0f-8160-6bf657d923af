"""
Market Evaluator Module

This module provides functions for evaluating market conditions,
detecting market regimes, and identifying sudden market changes.
"""

import os
import logging
import numpy as np
import pandas as pd
from analysis.indicators import (
    is_market_volatile,
    is_atr_high,
    is_atr_rising_rapidly,
    calculate_bollinger_bands,
    is_bollinger_squeeze,
    is_bollinger_breakout,
    calculate_fibonacci_retracement,
    identify_fibonacci_support_resistance,
    calculate_roc
)

# Set up logging
logger = logging.getLogger("market_evaluator")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/market_evaluator.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)


def detect_market_regime(data):
    """
    Determine market type (Stable/Volatile) using market data
    
    Parameters:
    data (dict): Market data including prices, indicators, and volumes
    
    Returns:
    str: Market regime type ("stable", "volatile", or "unclear")
    """
    try:
        # Extract relevant data
        close_prices = data.get("close_prices", [])
        high_prices = data.get("high_prices", [])
        low_prices = data.get("low_prices", [])
        volumes = data.get("volume", [])
        atr_values = data.get("atr_values", [])
        rsi_values = data.get("rsi_values", [])
        
        if not close_prices or len(close_prices) < 20:
            logger.warning("Insufficient data for market regime detection")
            return "unclear"
        
        # Calculate volatility metrics
        if not atr_values and len(high_prices) > 14 and len(low_prices) > 14 and len(close_prices) > 14:
            from analysis.indicators import calculate_atr
            atr_values = calculate_atr(high_prices, low_prices, close_prices)
        
        # Calculate ROC (Rate of Change)
        roc_values = calculate_roc(close_prices)
        
        # Calculate volume change
        if len(volumes) > 20:
            avg_volume = sum(volumes[-20:-1]) / 19
            current_volume = volumes[-1]
            volume_change = ((current_volume - avg_volume) / avg_volume) * 100
        else:
            volume_change = 0
        
        # Calculate Bollinger Bands
        upper_band, middle_band, lower_band = calculate_bollinger_bands(close_prices)
        
        # Check for Bollinger Band squeeze
        bb_squeeze = False
        if len(upper_band) > 0 and len(lower_band) > 0:
            bb_squeeze = is_bollinger_squeeze(upper_band, lower_band)
        
        # Check for Bollinger Band breakout
        bb_breakout = False
        if len(upper_band) > 0 and len(lower_band) > 0:
            upper_breakout, lower_breakout = is_bollinger_breakout(close_prices, upper_band, lower_band)
            bb_breakout = upper_breakout or lower_breakout
        
        # Check RSI extremes
        rsi_extreme = False
        if len(rsi_values) > 0:
            current_rsi = rsi_values[-1]
            rsi_extreme = current_rsi < 30 or current_rsi > 70
        
        # Check ATR for high volatility
        atr_high = False
        if len(atr_values) > 0:
            atr_high = is_atr_high(atr_values)
        
        # Check ROC for rapid price changes
        roc_high = False
        if len(roc_values) > 0:
            current_roc = roc_values[-1]
            roc_high = abs(current_roc) > 1.5
        
        # Determine market regime based on multiple factors
        volatility_score = 0
        
        if atr_high:
            volatility_score += 2
        
        if roc_high:
            volatility_score += 2
        
        if volume_change > 30:
            volatility_score += 1
        
        if bb_breakout:
            volatility_score += 2
        
        if rsi_extreme:
            volatility_score += 1
        
        if bb_squeeze:
            volatility_score -= 1  # Squeeze often precedes volatility but isn't volatile itself
        
        # Classify market regime
        if volatility_score >= 4:
            logger.info(f"Detected VOLATILE market regime (score: {volatility_score})")
            return "volatile"
        elif volatility_score <= 1:
            logger.info(f"Detected STABLE market regime (score: {volatility_score})")
            return "stable"
        else:
            logger.info(f"Detected UNCLEAR market regime (score: {volatility_score})")
            return "unclear"
    
    except Exception as e:
        logger.error(f"Error detecting market regime: {e}")
        return "unclear"


def detect_sudden_changes(data):
    """
    Detect sudden market changes
    
    Parameters:
    data (dict): Market data including prices, indicators, and volumes
    
    Returns:
    dict: Information about sudden market changes
    """
    try:
        # Extract relevant data
        close_prices = data.get("close_prices", [])
        rsi_values = data.get("rsi_values", [])
        macd_line = data.get("macd_line", [])
        signal_line = data.get("signal_line", [])
        volumes = data.get("volume", [])
        
        if not close_prices or len(close_prices) < 20:
            logger.warning("Insufficient data for sudden change detection")
            return {}
        
        sudden_changes = {}
        
        # Check for sudden price changes
        if len(close_prices) > 5:
            price_change_pct = ((close_prices[-1] - close_prices[-5]) / close_prices[-5]) * 100
            if abs(price_change_pct) > 3.0:
                sudden_changes["price"] = {
                    "change_percent": price_change_pct,
                    "direction": "up" if price_change_pct > 0 else "down"
                }
        
        # Check for sudden RSI changes
        if len(rsi_values) > 5:
            rsi_change = abs(rsi_values[-1] - rsi_values[-5])
            if rsi_change > 20:
                sudden_changes["rsi"] = {
                    "change": rsi_change,
                    "current": rsi_values[-1],
                    "previous": rsi_values[-5]
                }
        
        # Check for sudden MACD changes
        if len(macd_line) > 5 and len(signal_line) > 5:
            macd_change = abs(macd_line[-1] - macd_line[-5])
            signal_change = abs(signal_line[-1] - signal_line[-5])
            
            # Check for crossover
            crossover = (macd_line[-2] < signal_line[-2] and macd_line[-1] > signal_line[-1]) or \
                        (macd_line[-2] > signal_line[-2] and macd_line[-1] < signal_line[-1])
            
            if macd_change > 1.5 or signal_change > 1.5 or crossover:
                sudden_changes["macd"] = {
                    "macd_change": macd_change,
                    "signal_change": signal_change,
                    "crossover": crossover
                }
        
        # Check for sudden volume changes
        if len(volumes) > 5:
            avg_volume = sum(volumes[-6:-1]) / 5
            current_volume = volumes[-1]
            volume_change_pct = ((current_volume - avg_volume) / avg_volume) * 100
            
            if volume_change_pct > 100:  # Volume doubled
                sudden_changes["volume"] = {
                    "change_percent": volume_change_pct,
                    "current": current_volume,
                    "average": avg_volume
                }
        
        # Log sudden changes
        if sudden_changes:
            logger.info(f"Detected sudden market changes: {list(sudden_changes.keys())}")
        
        return sudden_changes
    
    except Exception as e:
        logger.error(f"Error detecting sudden changes: {e}")
        return {}


def calculate_ema_change(data):
    """
    Calculate EMA change percentage
    
    Parameters:
    data (dict): Market data including EMA values
    
    Returns:
    float: EMA change percentage
    """
    try:
        ema50 = data.get("ema50", [])
        
        if len(ema50) < 5:
            return 0
        
        ema_change_pct = ((ema50[-1] - ema50[-5]) / ema50[-5]) * 100
        return abs(ema_change_pct)
    
    except Exception as e:
        logger.error(f"Error calculating EMA change: {e}")
        return 0


def calculate_rsi_change(data):
    """
    Calculate RSI change
    
    Parameters:
    data (dict): Market data including RSI values
    
    Returns:
    float: RSI change
    """
    try:
        rsi_values = data.get("rsi_values", [])
        
        if len(rsi_values) < 5:
            return 0
        
        rsi_change = abs(rsi_values[-1] - rsi_values[-5])
        return rsi_change
    
    except Exception as e:
        logger.error(f"Error calculating RSI change: {e}")
        return 0


def calculate_macd_change(data):
    """
    Calculate MACD change
    
    Parameters:
    data (dict): Market data including MACD values
    
    Returns:
    float: MACD change
    """
    try:
        macd_line = data.get("macd_line", [])
        
        if len(macd_line) < 5:
            return 0
        
        macd_change = abs(macd_line[-1] - macd_line[-5])
        return macd_change
    
    except Exception as e:
        logger.error(f"Error calculating MACD change: {e}")
        return 0


def calculate_volume_change(data):
    """
    Calculate volume change percentage
    
    Parameters:
    data (dict): Market data including volume values
    
    Returns:
    float: Volume change percentage
    """
    try:
        volumes = data.get("volume", [])
        
        if len(volumes) < 20:
            return 0
        
        avg_volume = sum(volumes[-21:-1]) / 20
        current_volume = volumes[-1]
        
        if avg_volume == 0:
            return 0
        
        volume_change_pct = ((current_volume - avg_volume) / avg_volume) * 100
        return volume_change_pct
    
    except Exception as e:
        logger.error(f"Error calculating volume change: {e}")
        return 0


def evaluate_market_condition(data, symbol=None):
    """
    Comprehensive evaluation of market condition
    
    Parameters:
    data (dict): Market data including prices, indicators, and volumes
    symbol (str, optional): Trading pair symbol
    
    Returns:
    dict: Market condition evaluation
    """
    try:
        # Detect market regime
        regime = detect_market_regime(data)
        
        # Detect sudden changes
        changes = detect_sudden_changes(data)
        
        # Determine if trading should be avoided
        avoid_trading = regime == "volatile" and len(changes) > 0
        
        # Adjust risk based on market condition
        if regime == "volatile":
            risk_adjustment = 0.5  # Reduce risk by 50% in volatile markets
        elif regime == "stable":
            risk_adjustment = 1.0  # Normal risk in stable markets
        else:
            risk_adjustment = 0.75  # Reduce risk by 25% in unclear markets
        
        # Determine optimal take profit and stop loss levels
        if regime == "volatile":
            take_profit_pct = 1.0  # Higher take profit in volatile markets
            stop_loss_pct = 1.2    # Wider stop loss in volatile markets
        else:
            take_profit_pct = 0.7  # Lower take profit in stable markets
            stop_loss_pct = 0.8    # Tighter stop loss in stable markets
        
        # Adjust for specific currency if provided
        if symbol:
            from analysis.indicators import CURRENCY_ATR_MULTIPLIERS
            
            # Extract base currency from trading pair
            base_currency = symbol.split('/')[0] if '/' in symbol else symbol
            
            # Get currency-specific multiplier
            multiplier = CURRENCY_ATR_MULTIPLIERS.get(base_currency, CURRENCY_ATR_MULTIPLIERS["DEFAULT"])
            
            # Adjust take profit and stop loss based on currency volatility
            take_profit_pct *= multiplier
            stop_loss_pct *= multiplier
        
        # Create market condition evaluation
        evaluation = {
            "regime": regime,
            "sudden_changes": changes,
            "avoid_trading": avoid_trading,
            "risk_adjustment": risk_adjustment,
            "take_profit_pct": take_profit_pct,
            "stop_loss_pct": stop_loss_pct
        }
        
        logger.info(f"Market condition evaluation: {regime} regime, avoid trading: {avoid_trading}")
        
        return evaluation
    
    except Exception as e:
        logger.error(f"Error evaluating market condition: {e}")
        return {
            "regime": "unclear",
            "sudden_changes": {},
            "avoid_trading": True,
            "risk_adjustment": 0.5,
            "take_profit_pct": 0.7,
            "stop_loss_pct": 0.8
        }
