"""
Circuit Breaker Module

This module provides a circuit breaker pattern implementation for handling
errors and preventing cascading failures in the trading system.
"""

import time
import logging
import threading
from enum import Enum
from typing import Dict, Any, Optional, Callable, List, Tuple

# Configure logging
logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """
    Circuit breaker states
    """
    CLOSED = 0  # Normal operation
    OPEN = 1    # Circuit is open, no operations allowed
    HALF_OPEN = 2  # Testing if circuit can be closed again

class CircuitBreaker:
    """
    Circuit breaker for error handling with exponential backoff
    """
    
    def __init__(self, 
                 failure_threshold: int = 3, 
                 reset_timeout: int = 30,
                 half_open_timeout: int = 60,
                 max_backoff: int = 300,
                 backoff_factor: float = 2.0):
        """
        Initialize the circuit breaker
        
        Args:
            failure_threshold (int): Number of failures before opening circuit
            reset_timeout (int): Initial timeout before attempting reset (seconds)
            half_open_timeout (int): Timeout for half-open state (seconds)
            max_backoff (int): Maximum backoff time (seconds)
            backoff_factor (float): Exponential backoff factor
        """
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.half_open_timeout = half_open_timeout
        self.max_backoff = max_backoff
        self.backoff_factor = backoff_factor
        
        # Initialize state
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0
        self.next_attempt_time = 0
        self.current_backoff = reset_timeout
        
        # Initialize lock for thread safety
        self.lock = threading.RLock()
        
        # Initialize stats
        self.stats = {
            "total_failures": 0,
            "circuit_trips": 0,
            "successful_resets": 0,
            "last_state_change": time.time()
        }
        
        logger.info(f"CircuitBreaker initialized with threshold {failure_threshold}, reset timeout {reset_timeout}s")
    
    def record_failure(self) -> None:
        """
        Record a failure and potentially open the circuit
        """
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            self.stats["total_failures"] += 1
            
            logger.info(f"Failure recorded ({self.failure_count}/{self.failure_threshold})")
            
            # Check if threshold reached
            if self.state == CircuitState.CLOSED and self.failure_count >= self.failure_threshold:
                self._trip_breaker()
    
    def record_success(self) -> None:
        """
        Record a success and potentially close the circuit
        """
        with self.lock:
            # Reset failure count
            self.failure_count = 0
            
            # If in half-open state, close the circuit
            if self.state == CircuitState.HALF_OPEN:
                self._close_breaker()
    
    def is_allowed(self) -> bool:
        """
        Check if operation is allowed
        
        Returns:
            bool: True if operation is allowed, False otherwise
        """
        with self.lock:
            current_time = time.time()
            
            # If closed, always allow
            if self.state == CircuitState.CLOSED:
                return True
            
            # If open, check if reset timeout has elapsed
            elif self.state == CircuitState.OPEN:
                if current_time >= self.next_attempt_time:
                    # Transition to half-open
                    self._half_open_breaker()
                    return True
                else:
                    # Still open
                    return False
            
            # If half-open, allow one test operation
            elif self.state == CircuitState.HALF_OPEN:
                return True
            
            # Unknown state
            return False
    
    def get_remaining_timeout(self) -> int:
        """
        Get remaining timeout before next attempt
        
        Returns:
            int: Remaining timeout in seconds
        """
        with self.lock:
            if self.state == CircuitState.CLOSED:
                return 0
            
            current_time = time.time()
            remaining = max(0, int(self.next_attempt_time - current_time))
            return remaining
    
    def _trip_breaker(self) -> None:
        """
        Trip the circuit breaker (transition to OPEN state)
        """
        self.state = CircuitState.OPEN
        self.next_attempt_time = time.time() + self.current_backoff
        self.stats["circuit_trips"] += 1
        self.stats["last_state_change"] = time.time()
        
        # Apply exponential backoff
        self.current_backoff = min(self.max_backoff, self.current_backoff * self.backoff_factor)
        
        logger.warning(f"Circuit OPENED due to {self.failure_count} consecutive failures. "
                      f"Next attempt in {self.current_backoff}s")
    
    def _half_open_breaker(self) -> None:
        """
        Transition to HALF_OPEN state
        """
        self.state = CircuitState.HALF_OPEN
        self.next_attempt_time = time.time() + self.half_open_timeout
        self.stats["last_state_change"] = time.time()
        
        logger.info(f"Circuit HALF-OPEN. Testing if service is operational.")
    
    def _close_breaker(self) -> None:
        """
        Close the circuit breaker (transition to CLOSED state)
        """
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.current_backoff = self.reset_timeout  # Reset backoff
        self.stats["successful_resets"] += 1
        self.stats["last_state_change"] = time.time()
        
        logger.info(f"Circuit CLOSED. Normal operation resumed.")
    
    def get_state(self) -> CircuitState:
        """
        Get current circuit state
        
        Returns:
            CircuitState: Current state
        """
        with self.lock:
            return self.state
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get circuit breaker statistics
        
        Returns:
            Dict[str, Any]: Statistics
        """
        with self.lock:
            stats = self.stats.copy()
            stats["current_state"] = self.state.name
            stats["failure_count"] = self.failure_count
            stats["current_backoff"] = self.current_backoff
            
            if self.state != CircuitState.CLOSED:
                stats["remaining_timeout"] = self.get_remaining_timeout()
            
            return stats
    
    def reset(self) -> None:
        """
        Force reset the circuit breaker to closed state
        """
        with self.lock:
            self._close_breaker()
            logger.info("Circuit breaker manually reset to CLOSED state")

class CircuitBreakerRegistry:
    """
    Registry for managing multiple circuit breakers
    """
    
    def __init__(self):
        """
        Initialize the circuit breaker registry
        """
        self.breakers = {}
        self.lock = threading.RLock()
    
    def get_breaker(self, name: str, create_if_missing: bool = True) -> Optional[CircuitBreaker]:
        """
        Get a circuit breaker by name
        
        Args:
            name (str): Circuit breaker name
            create_if_missing (bool): Create if missing
            
        Returns:
            CircuitBreaker: Circuit breaker instance
        """
        with self.lock:
            if name in self.breakers:
                return self.breakers[name]
            
            if create_if_missing:
                breaker = CircuitBreaker()
                self.breakers[name] = breaker
                return breaker
            
            return None
    
    def get_all_breakers(self) -> Dict[str, CircuitBreaker]:
        """
        Get all circuit breakers
        
        Returns:
            Dict[str, CircuitBreaker]: All circuit breakers
        """
        with self.lock:
            return self.breakers.copy()
    
    def reset_all(self) -> None:
        """
        Reset all circuit breakers
        """
        with self.lock:
            for breaker in self.breakers.values():
                breaker.reset()
            
            logger.info("All circuit breakers reset")

# Global registry
registry = CircuitBreakerRegistry()

def get_circuit_breaker(name: str) -> CircuitBreaker:
    """
    Get a circuit breaker by name
    
    Args:
        name (str): Circuit breaker name
        
    Returns:
        CircuitBreaker: Circuit breaker instance
    """
    return registry.get_breaker(name)

def reset_all_circuit_breakers() -> None:
    """
    Reset all circuit breakers
    """
    registry.reset_all()
