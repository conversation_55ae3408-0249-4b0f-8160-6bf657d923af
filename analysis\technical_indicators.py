    def _calculate_trend(self, ema50_values, ema200_values):
        """
        Calculate trend based on EMA values
        
        Args:
            ema50_values (list): List of EMA50 values
            ema200_values (list): List of EMA200 values
            
        Returns:
            dict: Trend information
        """
        # Check if we have enough data
        if len(ema50_values) < 5 or len(ema200_values) < 5:
            return {"is_uptrend": False, "is_downtrend": False, "is_flat": True}
        
        # Get recent values
        recent_ema50 = ema50_values[-5:]
        recent_ema200 = ema200_values[-5:]
        
        # Calculate slopes
        ema50_slope = (recent_ema50[-1] - recent_ema50[0]) / 5
        ema200_slope = (recent_ema200[-1] - recent_ema200[0]) / 5
        
        # Determine trend
        is_uptrend = recent_ema50[-1] > recent_ema200[-1] and ema50_slope > 0
        is_downtrend = recent_ema50[-1] < recent_ema200[-1] and ema50_slope < 0
        is_flat = abs(ema50_slope) < 0.1 * recent_ema50[-1] / 100
