class MultiAIConnector:
    """
    AI connector that manages multiple AI services with account rotation and fallback
    """

    def __init__(self, key_manager=None, model_evaluator=None):
        """
        Initialize the multi-AI connector

        Args:
            key_manager: KeyManager instance for secure API key management
            model_evaluator: AIModelEvaluator instance for model evaluation
        """
        self.service_manager = AIServiceManager(key_manager=key_manager)

        # Initialize model evaluator if not provided
        if model_evaluator is None:
            from ai_services.model_evaluator import AIModelEvaluator
            self.model_evaluator = AIModelEvaluator(base_weights={
                "openai": 0.30,
                "deepseek": 0.35,
                "qwen": 0.35
            })
        else:
            self.model_evaluator = model_evaluator

        # Configure system prompts for each service
        self.system_prompts = {
            "openai": "You are a financial market analyst. Analyze the provided market data and indicators to provide a trading recommendation. Be concise and focus on actionable insights.",
            "deepseek": "You are a financial market analyst. Analyze the provided market data and indicators to provide a trading recommendation. Be concise and focus on actionable insights.",
            "qwen": "You are a financial market analyst. Analyze the provided market data and indicators to provide a trading recommendation. Be concise and focus on actionable insights."
        }

        # Configure max tokens for each service
        self.max_tokens = {
            "openai": 500,
            "deepseek": 500,
            "qwen": 500
        }

        # Get current weights from model evaluator
        self.weights = self.model_evaluator.get_current_weights()

        logger.info(f"MultiAIConnector initialized with weights: {self.weights}")

    def _format_market_data(self, market_data):
        """
        Format market data for AI analysis

        Args:
            market_data (dict): Market data

        Returns:
            str: Formatted market data
        """
        symbol = market_data.get("symbol", "BTC/USDT")
        price = market_data.get("price", 0)

        # Extract indicators
        indicators = market_data.get("indicators", {})
        ema50 = indicators.get("ema50", None)
        ema200 = indicators.get("ema200", None)
        is_uptrend = indicators.get("is_uptrend", False)
        is_downtrend = indicators.get("is_downtrend", False)
        is_flat = indicators.get("is_flat", True)

        # Format data
        formatted_data = f"""
Market Data for {symbol}:
Current Price: ${price:.2f}

Technical Indicators:
- EMA50: {f"{ema50:.2f}" if ema50 is not None else 'N/A'}
- EMA200: {f"{ema200:.2f}" if ema200 is not None else 'N/A'}
- Trend: {"Uptrend" if is_uptrend else "Downtrend" if is_downtrend else "Flat/Neutral"}

Based on this data, provide a trading recommendation (buy, sell, or hold) with a confidence level (0-100%) and a brief explanation.
"""
        return formatted_data

    def _call_openai_api(self, account, formatted_data, system_prompt=None, user_prompt=None):
        """
        Call OpenAI API for market analysis

        Args:
            account (AIServiceAccount): OpenAI account to use
            formatted_data (str): Formatted market data
            system_prompt (str, optional): Custom system prompt to use
            user_prompt (str, optional): Additional user prompt to append

        Returns:
            dict: Analysis result
        """
        try:
            # In a real implementation, this would make an actual API call
            # For simulation, we'll generate a random response
            import random

            # Use provided system prompt or default
            actual_system_prompt = system_prompt or self.system_prompts["openai"]
            
            # Combine formatted data with user prompt if provided
            actual_user_prompt = formatted_data
            if user_prompt:
                actual_user_prompt = f"{formatted_data}\n\n{user_prompt}"

            # Simulate API call
            time.sleep(0.5)  # Simulate API latency

            # Mark account as used
            account.mark_used(tokens_used=random.randint(100, 500))

            # Generate random sentiment
            sentiment = random.choice(["bullish", "bearish", "neutral"])

            # Generate random recommendation
            if sentiment == "bullish":
                recommendation = random.choices(["buy", "hold"], weights=[0.7, 0.3])[0]
            elif sentiment == "bearish":
                recommendation = random.choices(["sell", "hold"], weights=[0.7, 0.3])[0]
            else:
                recommendation = "hold"

            # Generate random confidence
            confidence = random.randint(50, 95)

            # Generate explanation
            explanations = {
                "bullish": [
                    "Price is showing strong upward momentum with increasing volume.",
                    "Recent price action has broken through key resistance levels.",
                    "Market sentiment is positive with strong buying pressure."
                ],
                "bearish": [
                    "Price is showing downward momentum with increasing selling volume.",
                    "Recent price action has broken below key support levels.",
                    "Market sentiment is negative with strong selling pressure."
                ],
                "neutral": [
                    "Price is consolidating within a range with no clear direction.",
                    "Volume is decreasing, indicating lack of conviction from buyers or sellers.",
                    "Market is waiting for a catalyst to determine the next move."
                ]
            }

            explanation = random.choice(explanations[sentiment])

            # Create analysis result
            analysis = {
                "sentiment": sentiment,
                "recommendation": recommendation,
                "confidence": confidence,
                "explanation": explanation
            }

            return {
                "service": "openai",
                "account_id": account.account_id,
                "analysis": analysis,
                "timestamp": datetime.datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            account.mark_error()

            # If too many errors, disable the account
            if account.error_count >= 3:
                account.disable()

            return None
