"""
Error Handler Module

This module provides comprehensive error handling functionality
for the trading bot, including error logging, recovery strategies,
and emergency procedures.
"""

import os
import sys
import time
import logging
import traceback
import datetime
import json
from pathlib import Path

# Set up logging
logger = logging.getLogger("error_handler")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/error_handler.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Define error categories
ERROR_CATEGORIES = {
    "API_ERROR": {
        "description": "Errors related to API communication",
        "recovery_attempts": 3,
        "backoff_seconds": 5,
        "critical": False
    },
    "NETWORK_ERROR": {
        "description": "Network connectivity issues",
        "recovery_attempts": 5,
        "backoff_seconds": 10,
        "critical": False
    },
    "AUTHENTICATION_ERROR": {
        "description": "API authentication failures",
        "recovery_attempts": 2,
        "backoff_seconds": 5,
        "critical": True
    },
    "INSUFFICIENT_FUNDS": {
        "description": "Insufficient funds for trading",
        "recovery_attempts": 0,
        "backoff_seconds": 0,
        "critical": True
    },
    "ORDER_REJECTED": {
        "description": "Order rejected by exchange",
        "recovery_attempts": 1,
        "backoff_seconds": 2,
        "critical": False
    },
    "RATE_LIMIT": {
        "description": "API rate limit exceeded",
        "recovery_attempts": 1,
        "backoff_seconds": 60,
        "critical": False
    },
    "SYSTEM_ERROR": {
        "description": "Internal system errors",
        "recovery_attempts": 2,
        "backoff_seconds": 5,
        "critical": True
    },
    "DATA_ERROR": {
        "description": "Data processing or validation errors",
        "recovery_attempts": 1,
        "backoff_seconds": 2,
        "critical": False
    },
    "UNKNOWN_ERROR": {
        "description": "Unclassified errors",
        "recovery_attempts": 1,
        "backoff_seconds": 5,
        "critical": False
    }
}


class TradingBotError(Exception):
    """Base exception class for trading bot errors."""
    
    def __init__(self, message, category="UNKNOWN_ERROR", error_code=None, details=None):
        """
        Initialize a trading bot error
        
        Parameters:
        message (str): Error message
        category (str): Error category
        error_code (str, optional): Specific error code
        details (dict, optional): Additional error details
        """
        self.message = message
        self.category = category
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = datetime.datetime.now().isoformat()
        
        # Call the base class constructor
        super().__init__(self.message)


class APIError(TradingBotError):
    """Exception raised for API communication errors."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "API_ERROR", error_code, details)


class NetworkError(TradingBotError):
    """Exception raised for network connectivity issues."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "NETWORK_ERROR", error_code, details)


class AuthenticationError(TradingBotError):
    """Exception raised for API authentication failures."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "AUTHENTICATION_ERROR", error_code, details)


class InsufficientFundsError(TradingBotError):
    """Exception raised for insufficient funds."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "INSUFFICIENT_FUNDS", error_code, details)


class OrderRejectedError(TradingBotError):
    """Exception raised for rejected orders."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "ORDER_REJECTED", error_code, details)


class RateLimitError(TradingBotError):
    """Exception raised for API rate limit exceeded."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "RATE_LIMIT", error_code, details)


class SystemError(TradingBotError):
    """Exception raised for internal system errors."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "SYSTEM_ERROR", error_code, details)


class DataError(TradingBotError):
    """Exception raised for data processing or validation errors."""
    
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message, "DATA_ERROR", error_code, details)


def log_error(error, context=None):
    """
    Log an error with context information
    
    Parameters:
    error (Exception): The error to log
    context (dict, optional): Additional context information
    
    Returns:
    str: Error ID
    """
    try:
        # Generate error ID
        error_id = f"ERR_{int(time.time())}_{hash(str(error)) % 10000:04d}"
        
        # Get error details
        if isinstance(error, TradingBotError):
            error_type = error.category
            error_message = error.message
            error_details = error.details
        else:
            error_type = error.__class__.__name__
            error_message = str(error)
            error_details = {}
        
        # Get stack trace
        stack_trace = traceback.format_exc()
        
        # Create error log entry
        error_log = {
            "error_id": error_id,
            "timestamp": datetime.datetime.now().isoformat(),
            "error_type": error_type,
            "error_message": error_message,
            "stack_trace": stack_trace,
            "context": context or {},
            "details": error_details
        }
        
        # Log to file
        error_log_dir = Path("logs/errors")
        error_log_dir.mkdir(parents=True, exist_ok=True)
        
        error_log_file = error_log_dir / f"{error_id}.json"
        with open(error_log_file, 'w') as f:
            json.dump(error_log, f, indent=2)
        
        # Log to console
        logger.error(f"Error {error_id}: {error_type} - {error_message}")
        
        return error_id
    
    except Exception as e:
        # Fallback logging if error logging fails
        logger.critical(f"Error logging failed: {e}")
        logger.critical(f"Original error: {error}")
        return "ERR_UNKNOWN"


def handle_error(error, context=None):
    """
    Handle an error based on its category
    
    Parameters:
    error (Exception): The error to handle
    context (dict, optional): Additional context information
    
    Returns:
    dict: Error handling result
    """
    try:
        # Log the error
        error_id = log_error(error, context)
        
        # Determine error category
        if isinstance(error, TradingBotError):
            category = error.category
        else:
            category = "UNKNOWN_ERROR"
        
        # Get error handling strategy
        strategy = ERROR_CATEGORIES.get(category, ERROR_CATEGORIES["UNKNOWN_ERROR"])
        
        # Determine if error is critical
        is_critical = strategy.get("critical", False)
        
        # Determine recovery attempts
        recovery_attempts = strategy.get("recovery_attempts", 0)
        
        # Determine backoff time
        backoff_seconds = strategy.get("backoff_seconds", 5)
        
        # Log handling strategy
        logger.info(f"Handling error {error_id} ({category}): critical={is_critical}, recovery_attempts={recovery_attempts}, backoff={backoff_seconds}s")
        
        # Return handling result
        return {
            "error_id": error_id,
            "category": category,
            "is_critical": is_critical,
            "recovery_attempts": recovery_attempts,
            "backoff_seconds": backoff_seconds,
            "handled": True
        }
    
    except Exception as e:
        # Fallback handling if error handling fails
        logger.critical(f"Error handling failed: {e}")
        logger.critical(f"Original error: {error}")
        
        return {
            "error_id": "ERR_UNKNOWN",
            "category": "SYSTEM_ERROR",
            "is_critical": True,
            "recovery_attempts": 0,
            "backoff_seconds": 0,
            "handled": False
        }


def execute_with_error_handling(func, *args, max_retries=3, retry_delay=5, context=None, **kwargs):
    """
    Execute a function with error handling and automatic retries
    
    Parameters:
    func (callable): Function to execute
    *args: Positional arguments for the function
    max_retries (int): Maximum number of retry attempts
    retry_delay (int): Delay between retries in seconds
    context (dict, optional): Additional context information
    **kwargs: Keyword arguments for the function
    
    Returns:
    tuple: (result, error) - result of the function or None if failed, error if any
    """
    retries = 0
    last_error = None
    
    while retries <= max_retries:
        try:
            # Execute the function
            result = func(*args, **kwargs)
            
            # If successful, return the result
            return result, None
        
        except Exception as e:
            # Handle the error
            last_error = e
            error_result = handle_error(e, context)
            
            # Check if error is critical
            if error_result.get("is_critical", False):
                logger.error(f"Critical error occurred, aborting: {e}")
                return None, last_error
            
            # Determine if we should retry
            if retries < max_retries:
                # Calculate backoff time
                backoff = error_result.get("backoff_seconds", retry_delay) * (retries + 1)
                
                logger.warning(f"Retry {retries + 1}/{max_retries} after {backoff}s: {e}")
                
                # Wait before retrying
                time.sleep(backoff)
                
                retries += 1
            else:
                logger.error(f"Max retries ({max_retries}) reached: {e}")
                return None, last_error
    
    return None, last_error


def emergency_shutdown(reason, context=None):
    """
    Perform emergency shutdown
    
    Parameters:
    reason (str): Reason for emergency shutdown
    context (dict, optional): Additional context information
    
    Returns:
    bool: True if shutdown was successful
    """
    try:
        # Log the emergency shutdown
        logger.critical(f"EMERGENCY SHUTDOWN: {reason}")
        
        # Create shutdown log entry
        shutdown_log = {
            "timestamp": datetime.datetime.now().isoformat(),
            "reason": reason,
            "context": context or {}
        }
        
        # Log to file
        shutdown_log_dir = Path("logs")
        shutdown_log_file = shutdown_log_dir / "emergency_shutdown.json"
        
        with open(shutdown_log_file, 'w') as f:
            json.dump(shutdown_log, f, indent=2)
        
        # Perform cleanup
        # TODO: Implement cleanup logic (close positions, cancel orders, etc.)
        
        return True
    
    except Exception as e:
        # Fallback logging if emergency shutdown fails
        logger.critical(f"Emergency shutdown failed: {e}")
        return False
