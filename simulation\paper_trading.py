"""
Paper Trading Simulation Module

This module implements a comprehensive paper trading simulation mode
that tracks trades, calculates P&L, and generates detailed reports.
"""

import os
import json
import logging
import datetime
import pandas as pd
from pathlib import Path
import yaml

logger = logging.getLogger(__name__)

class PaperTradingSimulator:
    """
    Paper Trading Simulator for testing trading strategies without real execution
    """

    def __init__(self, config_path="config/simulation_config.yaml"):
        """
        Initialize the paper trading simulator

        Args:
            config_path (str): Path to simulation configuration file
        """
        # Load configuration
        self.config = self._load_config(config_path)
        self.max_trades = self.config.get("max_simulation_trades", 50)
        self.report_frequency = self.config.get("report_frequency", "daily")

        # Create reports directory
        self.reports_dir = Path(self.config.get("save_reports_to", "reports/simulation"))
        self.reports_dir.mkdir(parents=True, exist_ok=True)

        # Initialize simulation state
        self.trades = []
        self.open_positions = {}
        self.balance = self.config.get("initial_balance", 50.0)  # Default 50 USDT
        self.starting_balance = self.balance
        self.trade_count = 0

        # AI model tracking
        self.ai_predictions = {
            "openai": [],
            "deepseek": [],
            "qwen": []
        }

        # Debate mode tracking
        self.debate_sessions = []

        # Initialize report timestamps
        self.last_report_time = datetime.datetime.now()
        self.simulation_start_time = datetime.datetime.now()

        logger.info(f"Paper Trading Simulator initialized with {self.balance} USDT")

    def _load_config(self, config_path):
        """
        Load simulation configuration from YAML file

        Args:
            config_path (str): Path to configuration file

        Returns:
            dict: Configuration parameters
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Error loading simulation config: {e}")
            return {
                "simulation_mode": True,
                "max_simulation_trades": 50,
                "initial_balance": 50.0,
                "report_frequency": "daily"
            }

    def execute_trade(self, symbol, side, quantity, price, stop_loss=None, take_profit=None, ai_signals=None,
                   is_stacked_entry=False, stack_id=None, trailing_stop=None, leverage=1.0):
        """
        Execute a simulated trade with enhanced features

        Args:
            symbol (str): Trading pair symbol
            side (str): Trade side ('buy' or 'sell')
            quantity (float): Trade quantity
            price (float): Trade price
            stop_loss (float, optional): Stop loss price
            take_profit (float, optional): Take profit price
            ai_signals (dict, optional): AI signals that led to this trade
            is_stacked_entry (bool, optional): Whether this is a stacked entry
            stack_id (str, optional): ID of the original trade for stacked entries
            trailing_stop (dict, optional): Trailing stop configuration
            leverage (float, optional): Leverage used for the trade

        Returns:
            dict: Trade information
        """
        # Check if max trades reached
        if self.trade_count >= self.max_trades:
            logger.info(f"Maximum number of simulation trades reached ({self.max_trades})")
            return None

        # Calculate trade value
        trade_value = quantity * price

        # Apply leverage for margin calculation
        margin_required = trade_value / leverage if leverage > 0 else trade_value

        # Check if enough balance for buy
        if side.lower() == 'buy' and margin_required > self.balance:
            logger.warning(f"Insufficient balance for trade: {margin_required} USDT required (leverage: {leverage}x), {self.balance} USDT available")
            return None

        # Create trade ID
        trade_id = f"sim_{len(self.trades) + 1}"

        # If this is a stacked entry, use the provided stack ID
        if is_stacked_entry and stack_id:
            related_id = stack_id
        else:
            related_id = None

        # Create trade object
        trade = {
            "id": trade_id,
            "symbol": symbol,
            "side": side.lower(),
            "quantity": quantity,
            "price": price,
            "value": trade_value,
            "margin_used": margin_required,
            "leverage": leverage,
            "timestamp": datetime.datetime.now().isoformat(),
            "stop_loss": stop_loss,
            "take_profit": take_profit,
            "trailing_stop": trailing_stop,
            "status": "open",
            "is_stacked_entry": is_stacked_entry,
            "related_trade_id": related_id,
            "ai_signals": ai_signals
        }

        # Update balance
        if side.lower() == 'buy':
            self.balance -= trade_value
            self.open_positions[trade["id"]] = trade
        else:  # sell
            self.balance += trade_value
            # Find matching position to close
            for pos_id, position in list(self.open_positions.items()):
                if position["symbol"] == symbol and position["side"] == "buy":
                    # Calculate profit/loss
                    buy_value = position["value"]
                    sell_value = trade_value
                    pnl = sell_value - buy_value
                    pnl_percent = (pnl / buy_value) * 100

                    # Update position
                    position["exit_price"] = price
                    position["exit_timestamp"] = trade["timestamp"]
                    position["pnl"] = pnl
                    position["pnl_percent"] = pnl_percent
                    position["status"] = "closed"

                    # Remove from open positions
                    del self.open_positions[pos_id]
                    break

        # Add to trades list
        self.trades.append(trade)
        self.trade_count += 1

        # Log trade
        logger.info(f"Simulated {side.upper()} trade: {quantity} {symbol} at {price} (Value: {trade_value:.2f} USDT)")

        # Check if report should be generated
        self._check_report_generation()

        return trade

    def update_market_price(self, symbol, current_price, indicators=None):
        """
        Update market price and check for stop loss/take profit triggers with smart exit rules

        Args:
            symbol (str): Trading pair symbol
            current_price (float): Current market price
            indicators (dict, optional): Current technical indicators

        Returns:
            list: Triggered trades
        """
        triggered_trades = []

        # Check open positions for the symbol
        for trade_id, trade in list(self.open_positions.items()):
            if trade["symbol"] != symbol:
                continue

            # Calculate current P&L
            if trade["side"] == "buy":
                # Calculate P&L
                unrealized_pnl = (current_price - trade["price"]) * trade["quantity"]
                unrealized_pnl_percent = ((current_price - trade["price"]) / trade["price"]) * 100

                # Update trailing stop if configured
                if trade.get("trailing_stop") and unrealized_pnl_percent > 0:
                    # Get trailing stop configuration
                    ts_config = trade["trailing_stop"]
                    activation_percent = ts_config.get("activation_percent", 0.3)
                    trail_percent = ts_config.get("trail_percent", 0.2)

                    # Check if trailing stop should be activated
                    if unrealized_pnl_percent >= activation_percent:
                        # Calculate new stop loss level
                        if not ts_config.get("activated", False):
                            # First activation - move to breakeven
                            new_stop_loss = trade["price"]
                            ts_config["activated"] = True
                            ts_config["highest_price"] = current_price
                            logger.info(f"Trailing stop activated for {trade_id} - moved to breakeven")
                        else:
                            # Already activated - check if price moved higher
                            if current_price > ts_config["highest_price"]:
                                # Update highest price
                                ts_config["highest_price"] = current_price

                                # Calculate new stop loss based on trail percentage
                                price_movement = current_price - ts_config["highest_price"]
                                trail_amount = current_price * (trail_percent / 100)
                                new_stop_loss = current_price - trail_amount

                                # Ensure new stop loss is higher than current
                                if new_stop_loss > trade["stop_loss"]:
                                    logger.info(f"Trailing stop updated for {trade_id}: {trade['stop_loss']} -> {new_stop_loss}")
                                    trade["stop_loss"] = new_stop_loss

                # Check for smart exit based on indicators
                if indicators and self.config.get("use_smart_exit", True):
                    # Check for negative trend signals
                    rsi = indicators.get("rsi")
                    macd_hist = indicators.get("macd_histogram")

                    # RSI overbought and falling
                    rsi_exit = rsi and rsi > 70 and indicators.get("rsi_slope", 0) < 0

                    # MACD histogram turning negative
                    macd_exit = macd_hist and macd_hist < 0 and indicators.get("macd_hist_slope", 0) < 0

                    # Execute smart exit if conditions met
                    if (rsi_exit or macd_exit) and unrealized_pnl_percent > 0:
                        sell_trade = self.execute_trade(
                            symbol=symbol,
                            side="sell",
                            quantity=trade["quantity"],
                            price=current_price,
                            leverage=trade["leverage"]
                        )
                        if sell_trade:
                            sell_trade["reason"] = "smart_exit"
                            triggered_trades.append(sell_trade)
                            logger.info(f"Smart exit triggered for {trade_id} (RSI: {rsi_exit}, MACD: {macd_exit})")
                            continue  # Skip further checks for this trade

                # Check stop loss
                if trade["stop_loss"] and current_price <= trade["stop_loss"]:
                    # Execute stop loss
                    sell_trade = self.execute_trade(
                        symbol=symbol,
                        side="sell",
                        quantity=trade["quantity"],
                        price=trade["stop_loss"],
                        leverage=trade["leverage"]
                    )
                    if sell_trade:
                        sell_trade["reason"] = "stop_loss"
                        triggered_trades.append(sell_trade)
                        continue  # Skip further checks for this trade

                # Check take profit
                elif trade["take_profit"] and current_price >= trade["take_profit"]:
                    # Check if partial take profit is enabled
                    if self.config.get("use_partial_take_profit", False):
                        # Take 50% profit
                        partial_quantity = trade["quantity"] * 0.5

                        # Execute partial take profit
                        sell_trade = self.execute_trade(
                            symbol=symbol,
                            side="sell",
                            quantity=partial_quantity,
                            price=trade["take_profit"],
                            leverage=trade["leverage"]
                        )

                        if sell_trade:
                            sell_trade["reason"] = "partial_take_profit"
                            triggered_trades.append(sell_trade)

                            # Update remaining position
                            trade["quantity"] -= partial_quantity
                            trade["value"] = trade["quantity"] * trade["price"]

                            # Move stop loss to breakeven
                            trade["stop_loss"] = trade["price"]

                            logger.info(f"Partial take profit executed for {trade_id} - moved stop loss to breakeven")
                    else:
                        # Execute full take profit
                        sell_trade = self.execute_trade(
                            symbol=symbol,
                            side="sell",
                            quantity=trade["quantity"],
                            price=trade["take_profit"],
                            leverage=trade["leverage"]
                        )
                        if sell_trade:
                            sell_trade["reason"] = "take_profit"
                            triggered_trades.append(sell_trade)

            # Update unrealized P&L
            trade["current_price"] = current_price
            trade["unrealized_pnl"] = (current_price - trade["price"]) * trade["quantity"]
            trade["unrealized_pnl_percent"] = ((current_price - trade["price"]) / trade["price"]) * 100

        return triggered_trades

    def record_ai_prediction(self, model, prediction, confidence, actual_outcome=None):
        """
        Record AI model prediction for accuracy tracking

        Args:
            model (str): AI model name
            prediction (str): Prediction ('buy', 'sell', 'hold')
            confidence (float): Confidence level (0-1)
            actual_outcome (str, optional): Actual outcome if known
        """
        if model not in self.ai_predictions:
            self.ai_predictions[model] = []

        self.ai_predictions[model].append({
            "timestamp": datetime.datetime.now().isoformat(),
            "prediction": prediction,
            "confidence": confidence,
            "actual_outcome": actual_outcome,
            "verified": actual_outcome is not None
        })

    def record_debate_session(self, models_involved, recommendations, weights, final_decision):
        """
        Record debate session for effectiveness tracking

        Args:
            models_involved (list): List of models involved
            recommendations (dict): Recommendations from each model
            weights (dict): Weights used for each model
            final_decision (str): Final decision after debate
        """
        self.debate_sessions.append({
            "timestamp": datetime.datetime.now().isoformat(),
            "models_involved": models_involved,
            "recommendations": recommendations,
            "weights": weights,
            "final_decision": final_decision,
            "verified": False,
            "was_correct": None
        })

    def verify_prediction(self, model, prediction_index, was_correct):
        """
        Verify if a prediction was correct

        Args:
            model (str): AI model name
            prediction_index (int): Index of the prediction
            was_correct (bool): Whether the prediction was correct
        """
        if model in self.ai_predictions and prediction_index < len(self.ai_predictions[model]):
            self.ai_predictions[model][prediction_index]["verified"] = True
            self.ai_predictions[model][prediction_index]["was_correct"] = was_correct

    def verify_debate_session(self, session_index, was_correct):
        """
        Verify if a debate session led to a correct decision

        Args:
            session_index (int): Index of the debate session
            was_correct (bool): Whether the decision was correct
        """
        if session_index < len(self.debate_sessions):
            self.debate_sessions[session_index]["verified"] = True
            self.debate_sessions[session_index]["was_correct"] = was_correct

    def generate_report(self, report_type="daily"):
        """
        Generate a detailed report of simulation performance

        Args:
            report_type (str): Report type ('daily', 'full')

        Returns:
            dict: Report data
        """
        # Calculate current equity
        open_positions_value = sum(
            trade["quantity"] * trade.get("current_price", trade["price"])
            for trade in self.open_positions.values()
        )
        total_equity = self.balance + open_positions_value

        # Calculate P&L
        pnl_amount = total_equity - self.starting_balance
        pnl_percent = (pnl_amount / self.starting_balance) * 100 if self.starting_balance > 0 else 0

        # Calculate trade statistics
        completed_trades = [t for t in self.trades if t.get("status") == "closed"]
        winning_trades = [t for t in completed_trades if t.get("pnl", 0) > 0]
        losing_trades = [t for t in completed_trades if t.get("pnl", 0) <= 0]

        win_rate = len(winning_trades) / len(completed_trades) if completed_trades else 0

        # Calculate AI model accuracy
        ai_accuracy = {}
        for model, predictions in self.ai_predictions.items():
            verified_predictions = [p for p in predictions if p.get("verified", False)]
            correct_predictions = [p for p in verified_predictions if p.get("was_correct", False)]

            if verified_predictions:
                accuracy = len(correct_predictions) / len(verified_predictions)
                ai_accuracy[model] = accuracy

        # Calculate debate mode effectiveness
        debate_stats = {
            "total_sessions": len(self.debate_sessions),
            "verified_sessions": len([s for s in self.debate_sessions if s.get("verified", False)]),
            "correct_decisions": len([s for s in self.debate_sessions if s.get("was_correct", True)]),
            "accuracy": 0
        }

        verified_debates = [s for s in self.debate_sessions if s.get("verified", False)]
        if verified_debates:
            debate_stats["accuracy"] = len([s for s in verified_debates if s.get("was_correct", False)]) / len(verified_debates)

        # Create report
        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "report_type": report_type,
            "simulation_duration": str(datetime.datetime.now() - self.simulation_start_time),
            "balance": self.balance,
            "open_positions_value": open_positions_value,
            "total_equity": total_equity,
            "pnl_amount": pnl_amount,
            "pnl_percent": pnl_percent,
            "trade_statistics": {
                "total_trades": len(self.trades),
                "completed_trades": len(completed_trades),
                "open_trades": len(self.open_positions),
                "winning_trades": len(winning_trades),
                "losing_trades": len(losing_trades),
                "win_rate": win_rate
            },
            "ai_model_accuracy": ai_accuracy,
            "debate_mode_statistics": debate_stats,
            "model_conflicts": {
                "total": len(self.debate_sessions),
                "frequency": len(self.debate_sessions) / max(1, len(self.trades))
            }
        }

        # Save report to file
        report_file = self.reports_dir / f"simulation_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=4)

        logger.info(f"Simulation report generated: {report_file}")

        # Update last report time
        self.last_report_time = datetime.datetime.now()

        return report

    def execute_stacked_entry(self, original_trade_id, confirmation_percent=0.5, indicators=None):
        """
        Execute a stacked entry based on trend confirmation

        Args:
            original_trade_id (str): ID of the original trade
            confirmation_percent (float): Percentage of original position to add
            indicators (dict, optional): Current technical indicators

        Returns:
            dict: Stacked entry trade information
        """
        # Check if original trade exists
        if original_trade_id not in self.open_positions:
            logger.warning(f"Cannot execute stacked entry: Original trade {original_trade_id} not found")
            return None

        # Get original trade
        original_trade = self.open_positions[original_trade_id]

        # Check if this is a buy position
        if original_trade["side"] != "buy":
            logger.warning(f"Cannot execute stacked entry: Original trade {original_trade_id} is not a buy position")
            return None

        # Check if trend confirmation is valid
        if indicators:
            # Check for trend confirmation
            ema50 = indicators.get("ema50")
            ema200 = indicators.get("ema200")
            rsi = indicators.get("rsi")

            # Verify uptrend
            is_uptrend = (ema50 and ema200 and ema50 > ema200 and
                         indicators.get("ema50_slope", 0) > 0)

            # Verify RSI is not overbought
            rsi_ok = rsi is None or (rsi and rsi < 70)

            # Only execute stacked entry if trend is confirmed
            if not (is_uptrend and rsi_ok):
                logger.info(f"Skipping stacked entry for {original_trade_id}: Trend not confirmed")
                return None

        # Calculate quantity for stacked entry
        stacked_quantity = original_trade["quantity"] * confirmation_percent

        # Get current price
        current_price = original_trade["current_price"]

        # Calculate new stop loss and take profit
        new_stop_loss = original_trade["stop_loss"]
        new_take_profit = original_trade["take_profit"]

        # Execute stacked entry
        stacked_trade = self.execute_trade(
            symbol=original_trade["symbol"],
            side="buy",
            quantity=stacked_quantity,
            price=current_price,
            stop_loss=new_stop_loss,
            take_profit=new_take_profit,
            is_stacked_entry=True,
            stack_id=original_trade_id,
            leverage=original_trade["leverage"]
        )

        if stacked_trade:
            logger.info(f"Executed stacked entry for {original_trade_id}: {stacked_quantity} at {current_price}")

        return stacked_trade

    def _check_report_generation(self):
        """Check if a report should be generated based on frequency"""
        now = datetime.datetime.now()

        if self.report_frequency == "hourly":
            if (now - self.last_report_time).total_seconds() >= 3600:  # 1 hour
                self.generate_report("hourly")

        elif self.report_frequency == "daily":
            if (now - self.last_report_time).total_seconds() >= 86400:  # 24 hours
                self.generate_report("daily")

        elif self.report_frequency == "weekly":
            if (now - self.last_report_time).total_seconds() >= 604800:  # 7 days
                self.generate_report("weekly")
