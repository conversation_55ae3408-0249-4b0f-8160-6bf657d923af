# Trading Bot Enhancements

This document provides detailed information about the enhancements made to the trading bot to improve performance with small accounts.

## Table of Contents

1. [Paper-Trading Mode](#paper-trading-mode)
2. [Enhanced Debate Mode](#enhanced-debate-mode)
3. [Dynamic TP/SL System](#dynamic-tpsl-system)
4. [Micro-Trade Mode](#micro-trade-mode)
5. [Customized Trading Hours](#customized-trading-hours)
6. [Technical Improvements](#technical-improvements)
7. [Configuration](#configuration)
8. [Integration](#integration)

## Paper-Trading Mode

The paper-trading mode allows you to test trading strategies without risking real money. It simulates trades, calculates P&L, and generates detailed reports.

### Features

- Simulates trades without executing real orders
- Logs all suggested signals
- Calculates hypothetical P&L
- Generates daily reports with:
  - Number of proposed trades
  - Accuracy of each AI model
  - Frequency of model conflicts
  - Effectiveness of the Debate Mode
- Easy switching between simulation and live modes

### Usage

To enable paper-trading mode, you can:

1. Set `simulation_mode: true` in the configuration file
2. Use the `--simulation` command line argument
3. Set the `SIMULATION_MODE` environment variable to `true`

Example:

```bash
python enhanced_bot.py --simulation
```

### Reports

Paper-trading reports are saved to the `reports/simulation` directory and include:

- Trade statistics (total trades, win rate, etc.)
- AI model accuracy
- Debate mode effectiveness
- P&L calculations

## Enhanced Debate Mode

The enhanced debate mode improves the decision-making process when AI models disagree by logging detailed analyses, tracking model accuracy, and adjusting weights based on historical performance.

### Features

- Logs each model's daily accuracy to a file
- Automatically updates weights based on weekly historical performance
- Adds a temporary extra weight (+10%) for the top-performing model over the last 7 days
- Improves debate logs with detailed analyses from each model

### Weight Bounds

The weights for each model are kept within the following bounds:

- OpenAI: 25-35%
- DeepSeek: 30-40%
- Qwen: 30-40%

### Accuracy Tracking

Model accuracy is tracked daily and saved to `data/ai_performance/accuracy_YYYY-MM-DD.json` files with the following format:

```json
{
  "date": "2025-05-03",
  "openai_accuracy": 0.72,
  "deepseek_accuracy": 0.68,
  "qwen_accuracy": 0.65
}
```

## Dynamic TP/SL System

The dynamic TP/SL system adjusts take profit and stop loss levels based on market conditions, providing better risk management in different market environments.

### Features

- Adjusts TP/SL levels based on market conditions (stable or volatile)
- Uses ATR and ROC to determine market condition
- Applies currency-specific multipliers for different volatility profiles
- Logs market type and TP/SL values used in trade logs

### Market Conditions

- **Stable Market**:
  - Take Profit: 0.7%
  - Stop Loss: 0.8%

- **Volatile Market**:
  - Take Profit: 1.0%
  - Stop Loss: 1.2%

### Currency Multipliers

Different currencies have different volatility profiles, so the system applies multipliers to the ATR and ROC thresholds:

- BTC: 1.0x (base)
- ETH: 1.5x
- SOL: 3.0x
- BNB: 1.8x
- Others: 2.0x (default)

## Micro-Trade Mode

The micro-trade mode is designed specifically for small accounts (less than $50) to optimize position sizing, leverage, and stacking entries.

### Features

- Adjusts position sizing based on account balance
- Auto-adjusts leverage based on account size
- Modifies stacking entries for small accounts

### Position Sizing

- **Very Small Accounts** (< $25):
  - Risk: 0.5% of account balance
  - Maximum position size: 0.5% of account balance
  - Leverage: 1.5x

- **Small Accounts** ($25-$50):
  - Risk: 1.0% of account balance
  - Leverage: 2.0x

- **Normal Accounts** (> $50):
  - Risk: 2.0% of account balance
  - Leverage: 3.0x

### Stacking Entries

- **Very Small Accounts**:
  - First entry: 30% of calculated size
  - Second entry: 70% upon trend confirmation

- **Small Accounts**:
  - First entry: 40% of calculated size
  - Second entry: 60% upon trend confirmation

- **Normal Accounts**:
  - First entry: 50% of calculated size
  - Second entry: 50% upon trend confirmation

## Customized Trading Hours

The customized trading hours feature allows you to define specific trading hours for each symbol and avoid trading during major market open/close windows.

### Features

- Define specific trading hours for each symbol
- Avoid trading during major market open/close windows
- Get optimal trading hours for each symbol

### Trading Hours

- BTC/USDT: 9 AM - 5 PM UTC
- ETH/USDT: 8 AM - 4 PM UTC
- SOL/USDT: 1 PM - 8 PM UTC
- BNB/USDT: 10 AM - 6 PM UTC
- Default: 24-hour trading

### Market Windows to Avoid

- US Stock Market Open: 13:15 - 13:45 UTC
- US Stock Market Close: 20:45 - 21:15 UTC
- Asian Markets Open: 00:45 - 01:15 UTC

## Technical Improvements

Various technical improvements have been made to optimize RAM and CPU usage, enhance error handling for Binance connectivity, and improve overall stability.

### Features

- Optimizes RAM usage to eliminate recurring warnings
- Improves CPU monitoring, especially on low-power hardware
- Enhances error handling for Binance connectivity
- Adds health-check for Binance API

### Resource Optimization

- Monitors RAM and CPU usage
- Applies optimizations when usage exceeds thresholds
- Provides low-power mode for resource-constrained hardware

### Binance Connectivity

- Implements exponential backoff for retries
- Checks time synchronization with Binance server
- Verifies API key permissions
- Logs detailed error information for troubleshooting

## Configuration

All enhancements are configurable through YAML configuration files:

- `config/enhanced_config.yaml`: Main configuration file
- `config/simulation_config.yaml`: Paper-trading mode configuration
- `config/ai_config.yaml`: AI models and debate mode configuration
- `config/tp_sl_config.yaml`: Dynamic TP/SL system configuration
- `config/micro_trade_config.yaml`: Micro-trade mode configuration
- `config/trading_hours_config.yaml`: Trading hours configuration

## Integration

The enhancements are integrated through the `enhanced_bot.py` script, which brings all the components together.

### Usage

```bash
# Run in live mode
python enhanced_bot.py

# Run in simulation mode
python enhanced_bot.py --simulation

# Run in low-power mode
python enhanced_bot.py --low-power

# Specify a custom configuration file
python enhanced_bot.py --config path/to/config.yaml
```

### Components

The bot initializes the following components:

- Resource Optimizer
- Binance Error Handler
- Debate Enhancer
- Dynamic TP/SL Manager
- Micro-Trade Manager
- Trading Hours Manager
- Paper Trading Simulator (if in simulation mode)
