#!/usr/bin/env python
"""
Bot Capabilities Summary

This script provides a comprehensive summary of the trading bot's capabilities
by asking the AI service to analyze and explain the bot's functionality.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import modules
sys.path.append(str(Path(__file__).resolve().parent.parent))

from ai_services.ai_connector import AIServiceManager

def get_bot_capabilities_summary():
    """
    Get a comprehensive summary of the trading bot's capabilities
    
    Returns:
        str: Detailed summary of the bot's capabilities
    """
    ai = AIServiceManager()
    
    question = """
    Provide me a comprehensive and accurate description of everything the bot does from the beginning to the end:
    How does the market analysis work?  
    What are the indicators that it uses? 
    How does artificial intelligence merge in its decisions? 
    How does it specify the entry and exit points? 
    How does it manage risks? 
    What are the properties of capital management? 
    How does it behave during sudden fluctuations? 
    When does it stop trading? 
    How does it deal with frequent losses? 
    How can it make profits with a small balance like $12? 
    I want an explanation in an understandable, detailed, and organized language.
    """
    
    response = ai.ask_ai(question)
    return response

def main():
    """Main function"""
    print("\n=== Trading Bot Capabilities Summary ===\n")
    summary = get_bot_capabilities_summary()
    print(summary)
    
    # Save to file
    output_file = "bot_capabilities_summary.txt"
    with open(output_file, "w") as f:
        f.write(summary)
    
    print(f"\nSummary saved to {output_file}")

if __name__ == "__main__":
    main()
