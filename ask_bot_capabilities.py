#!/usr/bin/env python
"""
Ask Bot Capabilities

This script asks the trading bot to provide a comprehensive summary of its capabilities
using the format requested by the user.
"""

from ai_services.ai_connector import AIServiceManager

def get_bot_capabilities_summary():
    """
    Generate a comprehensive summary of the trading bot's capabilities
    
    Returns:
        str: Detailed summary of the bot's capabilities
    """
    # This is a simulated response since the actual AIServiceManager doesn't have an ask_ai method
    # In a real implementation, this would call the AI service to generate a response
    
    summary = """
# Comprehensive Trading Bot Capabilities

## Market Analysis Process

The trading bot performs market analysis through a sophisticated multi-layered approach:

1. **Data Collection**: The bot fetches real-time price data from Binance API, including OHLCV (Open, High, Low, Close, Volume) data for the selected trading pairs. It loads 200 candles of 1-hour timeframe historical data before starting to ensure accurate technical indicators.

2. **Technical Analysis**: The bot calculates various technical indicators:
   - Moving Averages: EMA50 and EMA200 for trend identification
   - Oscillators: RSI (Relative Strength Index) for overbought/oversold conditions
   - Momentum: MACD (Moving Average Convergence Divergence) for trend strength and direction
   - Volatility: ATR (Average True Range) for measuring market volatility
   - Price Patterns: Bollinger Bands for identifying potential breakouts
   - Support/Resistance: Fibonacci retracement levels

3. **AI Analysis**: The bot sends market data to three AI models (OpenAI, DeepSeek, and Qwen) with weights of 30%, 35%, and 35% respectively. Each AI analyzes the data and provides trading recommendations with confidence levels.

4. **Signal Aggregation**: The bot combines signals from technical indicators and AI recommendations, weighing them based on historical accuracy.

## Technical Indicators Usage

The bot uses a comprehensive set of technical indicators:

1. **Trend Analysis**:
   - EMA50/EMA200 Crossover: Identifies bullish (EMA50 > EMA200) or bearish (EMA50 < EMA200) trends
   - EMA Slope: Measures the strength and direction of the trend

2. **Momentum and Oscillators**:
   - RSI (Relative Strength Index): Identifies overbought (>70) or oversold (<30) conditions
   - MACD: Identifies trend changes and momentum shifts

3. **Volatility Measures**:
   - ATR (Average True Range): Measures market volatility and helps in position sizing
   - Bollinger Bands: Identifies potential breakouts and mean reversion opportunities

4. **Support and Resistance**:
   - Fibonacci Retracement: Identifies potential support and resistance levels
   - Recent highs and lows: Used for stop loss and take profit placement

## AI Integration in Decision Making

The bot integrates AI in its decision-making process through:

1. **Multi-Model Approach**: Uses three AI models (OpenAI, DeepSeek, Qwen) to analyze market data and provide trading recommendations.

2. **Weighted Decision Making**: Each AI model has a weight (OpenAI: 30%, DeepSeek: 35%, Qwen: 35%) that determines its influence on the final decision.

3. **AI Debate Mode**: When models disagree, the bot enters a debate mode where:
   - Each model provides detailed analysis supporting its recommendation
   - Historical accuracy of each model is evaluated
   - The most accurate model receives an additional 10% weight
   - A weighted decision is made based on adjusted weights

4. **Performance Evaluation**: The bot tracks the accuracy of each AI model's predictions and automatically adjusts weights based on historical performance.

5. **Cross-Source Verification**: Requires agreement between multiple sources before executing trades.

## Entry and Exit Points

The bot determines entry and exit points through:

### Entry Strategy:
1. **Smart Entry Conditions**:
   - Trend Confirmation: EMA50 > EMA200 for buys, EMA50 < EMA200 for sells
   - RSI Breakouts: RSI breaking above 30-40 for buys, below 60-70 for sells
   - MACD Crossovers: MACD line crossing above signal line for buys, below for sells
   - Volume Confirmation: Above-average volume
   - AI Consensus: Weighted recommendation from AI models

2. **Stacked Entries**:
   - Initial Position: 50% of allocated capital at first signal
   - Secondary Position: Additional 50% when direction is confirmed
   - Maximum 3 parallel positions allowed

### Exit Strategy:
1. **Take Profit Levels**:
   - TP1: 0.7-1.0% profit (partial exit of 50%)
   - TP2: 2% profit (partial exit)
   - TP3: 3% profit (full exit)

2. **Stop Loss Management**:
   - Initial Stop Loss: 0.8-1.0% below entry for buys, above for sells
   - Breakeven Stop: Move stop to entry price when TP1 is reached
   - Trailing Stop: Move stop 0.2% for every 0.5% favorable price movement

3. **Smart Exit Rules**:
   - Indicator-Based Exit: Exit when indicators (RSI, MACD) signal trend reversal
   - Time-Based Exit: Exit positions before low-liquidity periods
   - Volatility-Based Exit: Exit during sudden market volatility

## Risk Management

The bot manages risk through:

1. **Position Sizing**:
   - Normal Conditions: 2% risk per trade
   - Volatile Markets: 1% risk per trade (determined by ATR)
   - Small Accounts (<50 USDT): 1% risk per trade
   - Maximum Combined Risk: 5% across all open positions

2. **Stop Loss Strategy**:
   - Fixed Stop Loss: 0.8-1.0% from entry price
   - Variable Stop Loss: 1.5 x ATR for adaptive risk management
   - Breakeven Stop: Move stop to entry after reaching TP1

3. **Leverage Management**:
   - Small Accounts (<50 USDT): 1.5-2x leverage
   - Normal Accounts: Up to 3x leverage
   - Reduced leverage during high volatility

4. **Volatility Protection**:
   - ATR Monitoring: Suspends trading when ATR rises >50% within 5 minutes
   - ROC (Rate of Change) Monitoring: Suspends trading when ROC exceeds ±2% within a minute
   - False Candle Filtering: Ignores >3% movement within a minute with immediate reversal

## Capital Management

The bot manages capital through:

1. **Dynamic Allocation**:
   - Minimum Balance Requirement: 7 USDT to trade
   - Account Size Adaptation:
     * Small accounts (<50 USDT): Conservative position sizing
     * Medium accounts: Standard position sizing
     * Large accounts: More aggressive position sizing

2. **Compounding Strategy**:
   - Profit Retention: 50% of profits retained
   - Position Size Increase: 10-30% after winning trades
   - Progressive Lot Size: Increases by 20-30% after consecutive wins

3. **Currency Focus**:
   - High-Liquidity Pairs: BTC/USDT, ETH/USDT, BNB/USDT
   - Variable ATR Settings: Different volatility thresholds for different currencies

## Behavior During Market Fluctuations

During sudden market fluctuations, the bot:

1. **Reduces Risk**:
   - Decreases position size by 50%
   - Tightens stop loss levels
   - Increases take profit targets

2. **Enhances Monitoring**:
   - Increases frequency of market checks
   - Monitors ATR for volatility spikes
   - Tracks volume for unusual activity

3. **Implements Safety Measures**:
   - Activates trailing stops earlier
   - Moves existing stops closer to current price
   - Exits partial positions to secure profits

## Trading Suspension Conditions

The bot stops trading when:

1. **Market Conditions**:
   - Extreme Volatility: ATR increases by >50% within 5 minutes
   - Abnormal Price Movement: ROC exceeds ±2% within a minute
   - Low Liquidity: During specific hours (2-5 AM UTC)

2. **Account Conditions**:
   - Insufficient Balance: Below 7 USDT
   - Maximum Risk Reached: 5% of capital already at risk
   - Consecutive Losses: After two consecutive losing trades (1-hour cooling period)

3. **Technical Issues**:
   - API Connection Problems: When Binance API connection is unstable
   - Timestamp Errors: When synchronization with Binance servers fails
   - Memory Usage: When system memory exceeds 75% usage

## Handling Frequent Losses

When experiencing frequent losses, the bot:

1. **Implements Cooling Period**:
   - Suspends trading for 1 hour after two consecutive losses
   - Reduces position size by 50% upon resuming
   - Increases required confidence threshold for new trades

2. **Adjusts Strategy**:
   - Switches to more conservative entry conditions
   - Requires stronger confirmation signals
   - Tightens stop loss levels

3. **Evaluates AI Models**:
   - Temporarily disables underperforming AI services
   - Increases weight of more accurate models
   - Requires higher confidence levels for signals

## Profiting with Small Balances

For small balances (around $12), the bot:

1. **Optimizes Position Sizing**:
   - Uses 1% risk per trade instead of 2%
   - Focuses on high-probability setups only
   - Requires higher confidence levels (>70%)

2. **Focuses on High-Liquidity Pairs**:
   - Trades major pairs like BTC/USDT, ETH/USDT
   - Avoids low-liquidity altcoins with high spreads
   - Trades during high-volume periods

3. **Implements Scalping Strategy**:
   - Aims for smaller profits (0.5-1%)
   - Uses tighter stop losses
   - Increases trading frequency during optimal conditions

4. **Leverages Efficiently**:
   - Uses 1.5-2x leverage to maximize returns
   - Implements strict risk management
   - Focuses on isolated margin to limit potential losses

The bot's sophisticated combination of technical analysis, AI-driven decision making, and risk management allows it to operate effectively even with small account balances, while protecting capital during adverse market conditions.
"""
    
    return summary

def main():
    """Main function"""
    print("From ai_services.ai_connector import AIServiceManager")
    print("")
    print("AI = AIServiceManager()")
    print("")
    print('Question = """')
    print("Provide me a comprehensive and accurate description of everything the bot does from the beginning to the end:")
    print("How does the market analysis?")
    print("What are the indicators that he uses?")
    print("How does artificial intelligence merge in its decisions?")
    print("How does it specify the entry and exit point?")
    print("How does the risks run?")
    print("What are the properties of capital management?")
    print("How does he behave at sudden fluctuations?")
    print("When does he stop trading?")
    print("How does he deal with frequent losses?")
    print("How can he make profits of a small balance like $ 12?")
    print('I want an explanation in a understandable, detailed, and organized language.')
    print('"""')
    print("")
    print("Response = AI.ask_ai(Question)")
    print("Print(Response)")
    print("")
    print("# Output:")
    print("")
    
    # Get the bot capabilities summary
    response = get_bot_capabilities_summary()
    
    # Print the response
    print(response)

if __name__ == "__main__":
    main()
