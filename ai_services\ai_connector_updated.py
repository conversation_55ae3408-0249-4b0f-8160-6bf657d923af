"""
AI Connector for multiple AI services with account rotation and fallback logic
"""

import os
import time
import json
import logging
import datetime
import requests
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIServiceAccount:
    """
    Represents an AI service account with API key and usage tracking
    """

    def __init__(self, service_name, account_id, api_key, api_base=None, model=None):
        """
        Initialize an AI service account

        Args:
            service_name (str): Name of the AI service (openai, deepseek, qwen)
            account_id (str): Identifier for this account (e.g., "account1")
            api_key (str): API key for this account
            api_base (str, optional): Base URL for API requests
            model (str, optional): Model to use for this account
        """
        self.service_name = service_name
        self.account_id = account_id
        self.api_key = api_key
        self.api_base = api_base
        self.model = model

        # Usage tracking
        self.is_active = True
        self.last_used = None
        self.error_count = 0
        self.total_requests = 0
        self.total_tokens = 0

    def mark_used(self, tokens_used=0):
        """
        Mark this account as used

        Args:
            tokens_used (int): Number of tokens used in this request
        """
        self.last_used = datetime.datetime.now()
        self.total_requests += 1
        self.total_tokens += tokens_used

    def mark_error(self):
        """
        Mark this account as having an error
        """
        self.error_count += 1

    def disable(self):
        """
        Disable this account (e.g., when API key expires)
        """
        self.is_active = False
        logger.warning(f"{self.service_name} account {self.account_id} has been disabled")

    def __str__(self):
        return f"{self.service_name}-{self.account_id} (active: {self.is_active}, errors: {self.error_count})"

class AIServiceManager:
    """
    Manages multiple AI service accounts with rotation and fallback logic
    """

    def __init__(self, key_manager=None):
        """
        Initialize the AI service manager

        Args:
            key_manager: KeyManager instance for secure API key management
        """
        # Load environment variables
        load_dotenv()

        # Store key manager
        self.key_manager = key_manager

        # Initialize account lists for each service
        self.openai_accounts = []
        self.deepseek_accounts = []
        self.qwen_accounts = []

        # Load accounts from environment variables or key manager
        self._load_accounts()

        # Current account indices
        self.current_openai_idx = 0
        self.current_deepseek_idx = 0
        self.current_qwen_idx = 0

        logger.info(f"AIServiceManager initialized with {len(self.openai_accounts)} OpenAI accounts, "
                   f"{len(self.deepseek_accounts)} DeepSeek accounts, and {len(self.qwen_accounts)} Qwen accounts")

    def _load_accounts(self):
        """
        Load AI service accounts from environment variables
        """
        # Load OpenAI accounts
        for i in range(1, 4):  # Accounts 1-3
            api_key = os.getenv(f"OPENAI_API_KEY_{i}")
            if api_key:
                model = os.getenv(f"OPENAI_MODEL_{i}", "gpt-4")
                account = AIServiceAccount("openai", f"account{i}", api_key, model=model)
                self.openai_accounts.append(account)

        # Load DeepSeek accounts
        for i in range(1, 4):  # Accounts 1-3
            api_key = os.getenv(f"DEEPSEEK_API_KEY_{i}")
            if api_key:
                api_base = os.getenv(f"DEEPSEEK_API_BASE_{i}", "https://api.deepseek.com/v1")
                model = os.getenv(f"DEEPSEEK_MODEL_{i}", "deepseek-chat")
                account = AIServiceAccount("deepseek", f"account{i}", api_key, api_base=api_base, model=model)
                self.deepseek_accounts.append(account)

        # Load Qwen accounts
        for i in range(1, 4):  # Accounts 1-3
            api_key = os.getenv(f"QWEN_API_KEY_{i}")
            if api_key:
                api_base = os.getenv(f"QWEN_API_BASE_{i}", "https://api.qwen.ai/v1")
                model = os.getenv(f"QWEN_MODEL_{i}", "qwen-max")
                account = AIServiceAccount("qwen", f"account{i}", api_key, api_base=api_base, model=model)
                self.qwen_accounts.append(account)

    def get_next_openai_account(self):
        """
        Get the next available OpenAI account with fallback to Qwen

        Returns:
            AIServiceAccount: Next available account
        """
        # Try all OpenAI accounts
        for _ in range(len(self.openai_accounts)):
            account = self.openai_accounts[self.current_openai_idx]
            self.current_openai_idx = (self.current_openai_idx + 1) % max(1, len(self.openai_accounts))

            if account.is_active:
                return account

        # Fallback to first Qwen account
        if self.qwen_accounts and self.qwen_accounts[0].is_active:
            logger.warning("All OpenAI accounts are disabled. Falling back to Qwen account.")
            return self.qwen_accounts[0]

        # No available accounts
        logger.error("No available OpenAI or fallback Qwen accounts")
        return None

    def get_next_deepseek_account(self):
        """
        Get the next available DeepSeek account with fallback to Qwen

        Returns:
            AIServiceAccount: Next available account
        """
        # Try all DeepSeek accounts
        for _ in range(len(self.deepseek_accounts)):
            account = self.deepseek_accounts[self.current_deepseek_idx]
            self.current_deepseek_idx = (self.current_deepseek_idx + 1) % max(1, len(self.deepseek_accounts))

            if account.is_active:
                return account

        # Fallback to second Qwen account
        if len(self.qwen_accounts) >= 2 and self.qwen_accounts[1].is_active:
            logger.warning("All DeepSeek accounts are disabled. Falling back to Qwen account.")
            return self.qwen_accounts[1]

        # No available accounts
        logger.error("No available DeepSeek or fallback Qwen accounts")
        return None

    def get_next_qwen_account(self):
        """
        Get the next available Qwen account

        Returns:
            AIServiceAccount: Next available account
        """
        # Try all Qwen accounts
        for _ in range(len(self.qwen_accounts)):
            account = self.qwen_accounts[self.current_qwen_idx]
            self.current_qwen_idx = (self.current_qwen_idx + 1) % max(1, len(self.qwen_accounts))

            if account.is_active:
                return account

        # No available accounts
        logger.error("No available Qwen accounts")
        return None

    def get_account_status(self):
        """
        Get status of all accounts

        Returns:
            dict: Account status information
        """
        status = {
            "openai": [{"id": acc.account_id, "active": acc.is_active, "errors": acc.error_count}
                      for acc in self.openai_accounts],
            "deepseek": [{"id": acc.account_id, "active": acc.is_active, "errors": acc.error_count}
                        for acc in self.deepseek_accounts],
            "qwen": [{"id": acc.account_id, "active": acc.is_active, "errors": acc.error_count}
                    for acc in self.qwen_accounts]
        }
        return status
