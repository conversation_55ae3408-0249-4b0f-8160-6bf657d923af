"""
Error Recovery System

This module provides a comprehensive error recovery system for handling
various types of errors and implementing appropriate recovery strategies.
"""

import time
import logging
import traceback
import functools
import threading
from enum import Enum
from typing import Dict, Any, Optional, Callable, List, Tuple, Type, Union

from utils.circuit_breaker import get_circuit_breaker, CircuitState
from utils.time_sync import get_adjusted_timestamp

# Configure logging
logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """
    Error severity levels
    """
    LOW = 0      # Minor error, can continue
    MEDIUM = 1   # Significant error, may need recovery
    HIGH = 2     # Severe error, requires immediate action
    CRITICAL = 3 # Critical error, system should shut down

class ErrorCategory(Enum):
    """
    Error categories
    """
    NETWORK = 0       # Network-related errors
    API = 1           # API-related errors
    AUTHENTICATION = 2 # Authentication errors
    TIMESTAMP = 3     # Timestamp errors
    RATE_LIMIT = 4    # Rate limit errors
    DATA = 5          # Data-related errors
    SYSTEM = 6        # System-related errors
    UNKNOWN = 7       # Unknown errors

class ErrorRecoveryStrategy:
    """
    Base class for error recovery strategies
    """
    
    def __init__(self, name: str):
        """
        Initialize the recovery strategy
        
        Args:
            name (str): Strategy name
        """
        self.name = name
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the error
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            bool: True if this strategy can handle the error
        """
        return False
    
    def handle(self, error: Exception, context: Dict[str, Any]) -> Tuple[bool, Any]:
        """
        Handle the error
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            Tuple[bool, Any]: (Success, Result)
        """
        return False, None

class RetryStrategy(ErrorRecoveryStrategy):
    """
    Simple retry strategy
    """
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
        """
        Initialize the retry strategy
        
        Args:
            max_retries (int): Maximum number of retries
            delay (float): Initial delay between retries
            backoff_factor (float): Backoff factor for exponential backoff
        """
        super().__init__("retry")
        self.max_retries = max_retries
        self.delay = delay
        self.backoff_factor = backoff_factor
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the error
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            bool: True if this strategy can handle the error
        """
        # Can handle network errors, API errors, and rate limit errors
        category = context.get("category", ErrorCategory.UNKNOWN)
        return category in [ErrorCategory.NETWORK, ErrorCategory.API, ErrorCategory.RATE_LIMIT]
    
    def handle(self, error: Exception, context: Dict[str, Any]) -> Tuple[bool, Any]:
        """
        Handle the error with retry
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            Tuple[bool, Any]: (Success, Result)
        """
        # Get the function to retry
        func = context.get("func")
        args = context.get("args", [])
        kwargs = context.get("kwargs", {})
        
        if not func:
            logger.error("No function provided for retry")
            return False, None
        
        # Try to execute the function with retries
        current_delay = self.delay
        
        for attempt in range(self.max_retries):
            try:
                # Wait before retry
                if attempt > 0:
                    time.sleep(current_delay)
                    current_delay *= self.backoff_factor
                
                # Execute function
                result = func(*args, **kwargs)
                
                # Success
                return True, result
            
            except Exception as e:
                logger.warning(f"Retry attempt {attempt+1}/{self.max_retries} failed: {e}")
        
        # All retries failed
        logger.error(f"All {self.max_retries} retry attempts failed")
        return False, None

class TimestampErrorStrategy(ErrorRecoveryStrategy):
    """
    Strategy for handling timestamp errors
    """
    
    def __init__(self):
        """
        Initialize the timestamp error strategy
        """
        super().__init__("timestamp_error")
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the error
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            bool: True if this strategy can handle the error
        """
        # Check if this is a timestamp error
        category = context.get("category", ErrorCategory.UNKNOWN)
        return category == ErrorCategory.TIMESTAMP
    
    def handle(self, error: Exception, context: Dict[str, Any]) -> Tuple[bool, Any]:
        """
        Handle timestamp error
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            Tuple[bool, Any]: (Success, Result)
        """
        # Get the function to retry
        func = context.get("func")
        args = context.get("args", [])
        kwargs = context.get("kwargs", {})
        
        if not func:
            logger.error("No function provided for timestamp error recovery")
            return False, None
        
        try:
            # Get current timestamp
            current_timestamp = get_adjusted_timestamp(ms=True)
            
            # Update timestamp in kwargs
            if "params" in kwargs and isinstance(kwargs["params"], dict):
                kwargs["params"]["timestamp"] = current_timestamp
            
            # Execute function with updated timestamp
            result = func(*args, **kwargs)
            
            # Success
            return True, result
        
        except Exception as e:
            logger.error(f"Timestamp error recovery failed: {e}")
            return False, None

class CircuitBreakerStrategy(ErrorRecoveryStrategy):
    """
    Circuit breaker strategy for handling persistent errors
    """
    
    def __init__(self, breaker_name: str = "default"):
        """
        Initialize the circuit breaker strategy
        
        Args:
            breaker_name (str): Circuit breaker name
        """
        super().__init__("circuit_breaker")
        self.breaker_name = breaker_name
    
    def can_handle(self, error: Exception, context: Dict[str, Any]) -> bool:
        """
        Check if this strategy can handle the error
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            bool: True if this strategy can handle the error
        """
        # Can handle any error
        return True
    
    def handle(self, error: Exception, context: Dict[str, Any]) -> Tuple[bool, Any]:
        """
        Handle error with circuit breaker
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            Tuple[bool, Any]: (Success, Result)
        """
        # Get circuit breaker
        breaker = get_circuit_breaker(self.breaker_name)
        
        # Record failure
        breaker.record_failure()
        
        # Check if circuit is open
        if breaker.get_state() == CircuitState.OPEN:
            timeout = breaker.get_remaining_timeout()
            logger.warning(f"Circuit breaker open. Retry after {timeout}s")
            return False, None
        
        # Get the function to execute
        func = context.get("func")
        args = context.get("args", [])
        kwargs = context.get("kwargs", {})
        
        if not func:
            logger.error("No function provided for circuit breaker")
            return False, None
        
        try:
            # Execute function
            result = func(*args, **kwargs)
            
            # Record success
            breaker.record_success()
            
            # Success
            return True, result
        
        except Exception as e:
            logger.error(f"Circuit breaker execution failed: {e}")
            return False, None

class ErrorRecoveryManager:
    """
    Manager for error recovery strategies
    """
    
    def __init__(self):
        """
        Initialize the error recovery manager
        """
        self.strategies = []
        self.error_handlers = {}
        self.lock = threading.RLock()
        
        # Register default strategies
        self.register_strategy(RetryStrategy())
        self.register_strategy(TimestampErrorStrategy())
        self.register_strategy(CircuitBreakerStrategy())
        
        logger.info("ErrorRecoveryManager initialized")
    
    def register_strategy(self, strategy: ErrorRecoveryStrategy) -> None:
        """
        Register a recovery strategy
        
        Args:
            strategy (ErrorRecoveryStrategy): Recovery strategy
        """
        with self.lock:
            self.strategies.append(strategy)
            logger.info(f"Registered recovery strategy: {strategy.name}")
    
    def register_error_handler(self, error_type: Type[Exception], handler: Callable) -> None:
        """
        Register an error handler for a specific error type
        
        Args:
            error_type (Type[Exception]): Error type
            handler (Callable): Error handler function
        """
        with self.lock:
            self.error_handlers[error_type] = handler
            logger.info(f"Registered error handler for {error_type.__name__}")
    
    def categorize_error(self, error: Exception) -> Tuple[ErrorCategory, ErrorSeverity]:
        """
        Categorize an error
        
        Args:
            error (Exception): The error
            
        Returns:
            Tuple[ErrorCategory, ErrorSeverity]: Error category and severity
        """
        # Default category and severity
        category = ErrorCategory.UNKNOWN
        severity = ErrorSeverity.MEDIUM
        
        # Check error type and message
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        # Network errors
        if error_type in ["ConnectionError", "TimeoutError", "ConnectionRefusedError"]:
            category = ErrorCategory.NETWORK
            severity = ErrorSeverity.MEDIUM
        
        # API errors
        elif error_type in ["APIError", "RequestError"]:
            category = ErrorCategory.API
            severity = ErrorSeverity.MEDIUM
        
        # Authentication errors
        elif "auth" in error_message or "unauthorized" in error_message or "permission" in error_message:
            category = ErrorCategory.AUTHENTICATION
            severity = ErrorSeverity.HIGH
        
        # Timestamp errors
        elif "timestamp" in error_message:
            category = ErrorCategory.TIMESTAMP
            severity = ErrorSeverity.LOW
        
        # Rate limit errors
        elif "rate limit" in error_message or "too many requests" in error_message:
            category = ErrorCategory.RATE_LIMIT
            severity = ErrorSeverity.MEDIUM
        
        # Data errors
        elif "data" in error_message or "format" in error_message or "parse" in error_message:
            category = ErrorCategory.DATA
            severity = ErrorSeverity.MEDIUM
        
        # System errors
        elif error_type in ["MemoryError", "OSError", "IOError"]:
            category = ErrorCategory.SYSTEM
            severity = ErrorSeverity.HIGH
        
        return category, severity
    
    def handle_error(self, error: Exception, context: Dict[str, Any]) -> Tuple[bool, Any]:
        """
        Handle an error using appropriate recovery strategies
        
        Args:
            error (Exception): The error
            context (Dict[str, Any]): Error context
            
        Returns:
            Tuple[bool, Any]: (Success, Result)
        """
        # Categorize error
        category, severity = self.categorize_error(error)
        
        # Update context
        context["category"] = category
        context["severity"] = severity
        
        # Log error
        logger.error(f"Error: {error} (Category: {category.name}, Severity: {severity.name})")
        
        # Check if we have a specific handler for this error type
        error_type = type(error)
        if error_type in self.error_handlers:
            try:
                result = self.error_handlers[error_type](error, context)
                return True, result
            except Exception as e:
                logger.error(f"Error handler failed: {e}")
        
        # Try each strategy
        for strategy in self.strategies:
            if strategy.can_handle(error, context):
                logger.info(f"Trying recovery strategy: {strategy.name}")
                success, result = strategy.handle(error, context)
                
                if success:
                    logger.info(f"Recovery strategy {strategy.name} succeeded")
                    return True, result
        
        # All strategies failed
        logger.error("All recovery strategies failed")
        return False, None
    
    def with_recovery(self, func: Callable) -> Callable:
        """
        Decorator for functions that need error recovery
        
        Args:
            func (Callable): Function to decorate
            
        Returns:
            Callable: Decorated function
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as error:
                # Create context
                context = {
                    "func": func,
                    "args": args,
                    "kwargs": kwargs,
                    "error": error
                }
                
                # Handle error
                success, result = self.handle_error(error, context)
                
                if success:
                    return result
                else:
                    # Re-raise original error if recovery failed
                    raise
        
        return wrapper

# Global manager
recovery_manager = ErrorRecoveryManager()

def with_recovery(func: Callable) -> Callable:
    """
    Decorator for functions that need error recovery
    
    Args:
        func (Callable): Function to decorate
        
    Returns:
        Callable: Decorated function
    """
    return recovery_manager.with_recovery(func)

def handle_error(error: Exception, context: Dict[str, Any] = None) -> Tuple[bool, Any]:
    """
    Handle an error using appropriate recovery strategies
    
    Args:
        error (Exception): The error
        context (Dict[str, Any], optional): Error context
        
    Returns:
        Tuple[bool, Any]: (Success, Result)
    """
    if context is None:
        context = {}
    
    return recovery_manager.handle_error(error, context)

def register_error_handler(error_type: Type[Exception], handler: Callable) -> None:
    """
    Register an error handler for a specific error type
    
    Args:
        error_type (Type[Exception]): Error type
        handler (Callable): Error handler function
    """
    recovery_manager.register_error_handler(error_type, handler)

def register_recovery_strategy(strategy: ErrorRecoveryStrategy) -> None:
    """
    Register a recovery strategy
    
    Args:
        strategy (ErrorRecoveryStrategy): Recovery strategy
    """
    recovery_manager.register_strategy(strategy)
