"""
Advanced Position Sizer
Calculates optimal position sizes based on risk parameters and market conditions
"""

import os
import math
import logging
import numpy as np

# Set up logging
logger = logging.getLogger("position_sizer")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/position_sizer.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class AdvancedPositionSizer:
    """
    Calculates optimal position sizes based on risk parameters and market conditions
    """

    def __init__(self, exchange_api=None, margin_monitor=None):
        """
        Initialize the position sizer

        Args:
            exchange_api: Exchange API instance
            margin_monitor: Margin Monitor instance
        """
        self.exchange_api = exchange_api
        self.margin_monitor = margin_monitor
        self.min_position_size = 0.001  # Minimum position size in BTC
        self.min_notional = 10  # Minimum notional value in USDT

        # Risk parameters - adjusted as requested
        self.normal_risk_pct = 1.0  # Normal risk percentage (reduced from 2% to 1%)
        self.volatile_risk_pct = 0.5  # Volatile risk percentage (reduced from 1% to 0.5%)
        self.max_combined_risk_pct = 5.0  # Maximum combined risk percentage (5%)

        # Small account risk adjustment
        self.small_account_threshold = 50.0  # Small account threshold in USDT
        self.small_account_risk_pct = 1.0  # Small account risk percentage (1%)

        # Minimum balance to maintain
        self.min_balance_usdt = 7.0  # Minimum balance to maintain in USDT

        # Leverage settings
        self.max_leverage = 2.0  # Maximum leverage (reduced from 3x to 2x)
        self.small_account_max_leverage = 1.5  # Maximum leverage for small accounts

        # High-liquidity currencies
        self.high_liquidity_currencies = ["BTC", "ETH", "BNB"]

        logger.info("AdvancedPositionSizer initialized with updated risk parameters")

    def calculate_position_size(self, symbol, entry_price, stop_loss_price, account_balance, market_data=None):
        """
        Calculate optimal position size based on risk parameters

        Args:
            symbol (str): Trading pair symbol
            entry_price (float): Entry price
            stop_loss_price (float): Stop loss price
            account_balance (float): Account balance in USDT
            market_data (dict, optional): Market data for volatility assessment

        Returns:
            float: Position size in base currency
        """
        try:
            if account_balance <= self.min_balance_usdt:
                logger.warning(f"Account balance ({account_balance} USDT) is below minimum ({self.min_balance_usdt} USDT)")
                return 0.0

            if entry_price <= 0 or stop_loss_price <= 0:
                logger.warning(f"Invalid prices: entry={entry_price}, stop_loss={stop_loss_price}")
                return 0.0

            # Calculate available balance (account balance minus minimum balance)
            available_balance = account_balance - self.min_balance_usdt

            # Extract base currency from symbol
            base_currency = symbol.split('/')[0] if '/' in symbol else symbol

            # Check if it's a high-liquidity currency
            is_high_liquidity = base_currency in self.high_liquidity_currencies
            if is_high_liquidity:
                logger.info(f"Trading high-liquidity currency: {base_currency}")

            # Determine risk percentage based on market volatility, account size, and currency
            risk_pct = self._determine_risk_percentage(market_data, account_balance, symbol)

            # Calculate risk amount
            risk_amount = available_balance * (risk_pct / 100)

            # Calculate price difference
            price_diff = abs(entry_price - stop_loss_price)

            # Calculate risk per unit
            risk_per_unit = price_diff

            # Calculate base position size
            if risk_per_unit > 0:
                base_position_size = risk_amount / risk_per_unit
            else:
                logger.warning("Risk per unit is zero or negative")
                return 0.0

            # Apply intelligent allocation based on account size
            adjusted_position_size = self._adjust_for_account_size(base_position_size, available_balance, entry_price)

            # Apply margin safety if margin monitor is available
            if self.margin_monitor and symbol in self.margin_monitor.get_current_usage():
                adjusted_position_size = self.margin_monitor.get_safe_position_size(symbol, adjusted_position_size)

            # Apply combined risk limit
            adjusted_position_size = self._apply_combined_risk_limit(adjusted_position_size, symbol, entry_price)

            # Apply leverage limits based on account size
            max_leverage = self.max_leverage
            if account_balance < self.small_account_threshold:
                max_leverage = self.small_account_max_leverage
                logger.info(f"Small account detected, limiting leverage to {max_leverage}x")

            # Ensure minimum position size and notional value
            final_position_size = self._ensure_minimum_size(adjusted_position_size, entry_price)

            # Log detailed position sizing information
            logger.info(f"Position size calculation for {symbol}:")
            logger.info(f"  - Account balance: {account_balance:.2f} USDT")
            logger.info(f"  - Risk percentage: {risk_pct}%")
            logger.info(f"  - Risk amount: {risk_amount:.2f} USDT")
            logger.info(f"  - Entry price: {entry_price:.2f}")
            logger.info(f"  - Stop loss price: {stop_loss_price:.2f}")
            logger.info(f"  - Price difference: {price_diff:.2f}")
            logger.info(f"  - Base position size: {base_position_size:.6f}")
            logger.info(f"  - Adjusted position size: {adjusted_position_size:.6f}")
            logger.info(f"  - Final position size: {final_position_size:.6f}")
            logger.info(f"  - Max leverage: {max_leverage}x")

            return final_position_size

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0

    def _determine_risk_percentage(self, market_data, account_balance=None, symbol=None):
        """
        Determine risk percentage based on market volatility, account size, and currency

        Args:
            market_data (dict): Market data
            account_balance (float, optional): Account balance in USDT
            symbol (str, optional): Trading pair symbol

        Returns:
            float: Risk percentage
        """
        try:
            # Default risk percentage based on normal conditions
            risk_pct = self.normal_risk_pct

            # Adjust for small accounts
            if account_balance is not None and account_balance < self.small_account_threshold:
                logger.info(f"Small account detected ({account_balance} USDT < {self.small_account_threshold} USDT)")
                risk_pct = self.small_account_risk_pct
                logger.info(f"Using small account risk percentage: {risk_pct}%")

            # Extract currency from symbol if provided
            currency = None
            if symbol:
                # Extract base currency from trading pair (e.g., "BTC/USDT" -> "BTC")
                currency = symbol.split('/')[0] if '/' in symbol else symbol

                # Check if it's a high-liquidity currency
                if currency in self.high_liquidity_currencies:
                    logger.info(f"High-liquidity currency detected: {currency}")

            if not market_data:
                return risk_pct

            # Extract ATR values if available
            atr_values = []
            if "indicators" in market_data and "atr_values" in market_data["indicators"]:
                atr_values = market_data["indicators"]["atr_values"]
            elif "historical" in market_data:
                # Calculate ATR if not provided
                historical = market_data["historical"]
                high_prices = [candle.get("high", 0) for candle in historical]
                low_prices = [candle.get("low", 0) for candle in historical]
                close_prices = [candle.get("close", 0) for candle in historical]

                atr_values = self._calculate_atr(high_prices, low_prices, close_prices)

            # Check if market is volatile based on ATR, with currency-specific adjustments
            is_volatile = self._is_market_volatile(atr_values, currency=currency)

            if is_volatile:
                logger.info("Market is volatile, using reduced risk percentage")
                # For small accounts, further reduce risk in volatile markets
                if account_balance is not None and account_balance < self.small_account_threshold:
                    reduced_risk = self.volatile_risk_pct * 0.75  # Further reduce by 25%
                    logger.info(f"Small account in volatile market, further reducing risk: {reduced_risk}%")
                    return reduced_risk
                else:
                    return self.volatile_risk_pct
            else:
                logger.info(f"Market is normal, using standard risk percentage: {risk_pct}%")
                return risk_pct

        except Exception as e:
            logger.error(f"Error determining risk percentage: {e}")
            return self.volatile_risk_pct  # Conservative approach

    def _calculate_atr(self, high_prices, low_prices, close_prices, period=14):
        """
        Calculate Average True Range (ATR)

        Args:
            high_prices (list): List of high prices
            low_prices (list): List of low prices
            close_prices (list): List of close prices
            period (int): ATR period

        Returns:
            list: ATR values
        """
        try:
            if len(high_prices) < period + 1 or len(low_prices) < period + 1 or len(close_prices) < period + 1:
                return []

            # Calculate True Range
            tr_values = []
            for i in range(len(close_prices)):
                if i == 0:
                    tr = high_prices[i] - low_prices[i]
                else:
                    tr1 = high_prices[i] - low_prices[i]
                    tr2 = abs(high_prices[i] - close_prices[i-1])
                    tr3 = abs(low_prices[i] - close_prices[i-1])
                    tr = max(tr1, tr2, tr3)

                tr_values.append(tr)

            # Calculate ATR using simple moving average
            atr_values = []
            for i in range(len(tr_values)):
                if i < period - 1:
                    # Not enough data for ATR calculation
                    atr_values.append(None)
                else:
                    # Calculate ATR as average of TR values
                    atr = sum(tr_values[i-period+1:i+1]) / period
                    atr_values.append(atr)

            return atr_values

        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return []

    def _is_market_volatile(self, atr_values, lookback=5, threshold=1.5, currency=None):
        """
        Check if the market is volatile based on ATR, with currency-specific adjustments

        Args:
            atr_values (list): List of ATR values
            lookback (int): Number of periods to look back
            threshold (float): Threshold multiplier for volatility
            currency (str, optional): Currency symbol (e.g., "BTC", "ETH")

        Returns:
            bool: True if market is volatile, False otherwise
        """
        try:
            if len(atr_values) < lookback + 1:
                return False

            # Get recent ATR values
            recent_atr = [atr for atr in atr_values[-lookback-1:] if atr is not None]

            if len(recent_atr) < 2:
                return False

            # Calculate average ATR for the lookback period
            avg_atr = sum(recent_atr[:-1]) / len(recent_atr[:-1])

            # Check if current ATR is significantly higher than average
            current_atr = recent_atr[-1]

            # Adjust threshold based on currency
            adjusted_threshold = threshold
            if currency:
                # Currency-specific ATR multipliers
                currency_multipliers = {
                    "BTC": 1.0,    # Base multiplier for BTC
                    "ETH": 1.5,    # ETH is 1.5x more volatile than BTC
                    "BNB": 1.8,    # BNB is 1.8x more volatile than BTC
                    "SOL": 3.0,    # SOL is 3x more volatile than BTC
                    "XRP": 2.5,    # XRP is 2.5x more volatile than BTC
                    "ADA": 2.2,    # ADA is 2.2x more volatile than BTC
                    "DOT": 2.5,    # DOT is 2.5x more volatile than BTC
                    "DOGE": 3.5,   # DOGE is 3.5x more volatile than BTC
                    "AVAX": 2.8,   # AVAX is 2.8x more volatile than BTC
                    "MATIC": 2.5,  # MATIC is 2.5x more volatile than BTC
                    "LINK": 2.2,   # LINK is 2.2x more volatile than BTC
                    "UNI": 2.3,    # UNI is 2.3x more volatile than BTC
                    "ATOM": 2.4,   # ATOM is 2.4x more volatile than BTC
                    "LTC": 1.8,    # LTC is 1.8x more volatile than BTC
                }

                # Get multiplier for this currency (default to 2.0 if not found)
                multiplier = currency_multipliers.get(currency, 2.0)

                # Adjust threshold based on currency volatility
                adjusted_threshold = threshold / multiplier

                logger.info(f"Using currency-specific ATR threshold for {currency}: {adjusted_threshold:.2f} (base: {threshold}, multiplier: {multiplier})")

            # Check if market is volatile using adjusted threshold
            is_volatile = current_atr > avg_atr * adjusted_threshold

            # Calculate volatility percentage for logging
            volatility_pct = (current_atr / avg_atr - 1) * 100

            if is_volatile:
                logger.info(f"Market is volatile: current ATR is {volatility_pct:.1f}% above average")
            else:
                logger.debug(f"Market volatility is normal: current ATR is {volatility_pct:.1f}% relative to average")

            return is_volatile

        except Exception as e:
            logger.error(f"Error checking market volatility: {e}")
            return True  # Conservative approach: assume high volatility if error

    def _adjust_for_account_size(self, position_size, account_balance, entry_price):
        """
        Adjust position size based on account size

        Args:
            position_size (float): Base position size
            account_balance (float): Account balance in USDT
            entry_price (float): Entry price

        Returns:
            float: Adjusted position size
        """
        try:
            # Calculate position value
            position_value = position_size * entry_price

            # Calculate position size as percentage of account
            position_pct = (position_value / account_balance) * 100

            # Apply scaling based on account size
            if account_balance < 100:
                # Small account: cap at 20% of account
                max_pct = 20
            elif account_balance < 1000:
                # Medium account: cap at 15% of account
                max_pct = 15
            else:
                # Large account: cap at 10% of account
                max_pct = 10

            if position_pct > max_pct:
                # Scale down position size
                scale_factor = max_pct / position_pct
                adjusted_size = position_size * scale_factor
                logger.info(f"Scaling down position: {position_pct:.2f}% -> {max_pct:.2f}% of account")
                return adjusted_size

            return position_size

        except Exception as e:
            logger.error(f"Error adjusting for account size: {e}")
            return position_size * 0.5  # Conservative approach: halve the position size

    def _apply_combined_risk_limit(self, position_size, symbol, entry_price):
        """
        Apply combined risk limit across all positions

        Args:
            position_size (float): Position size
            symbol (str): Trading pair symbol
            entry_price (float): Entry price

        Returns:
            float: Adjusted position size
        """
        try:
            if not self.exchange_api:
                return position_size

            # Get all open positions
            open_positions = []
            try:
                # This would need to be implemented based on your exchange API
                if hasattr(self.exchange_api, 'get_open_positions'):
                    open_positions = self.exchange_api.get_open_positions()
            except Exception as e:
                logger.error(f"Error getting open positions: {e}")

            # Calculate total risk amount
            total_risk_amount = 0
            for position in open_positions:
                pos_symbol = position.get('symbol', '')
                pos_size = position.get('size', 0)
                pos_entry_price = position.get('entry_price', 0)
                pos_stop_loss = position.get('stop_loss', 0)

                if pos_size > 0 and pos_entry_price > 0 and pos_stop_loss > 0:
                    # Calculate risk for this position
                    risk_per_unit = abs(pos_entry_price - pos_stop_loss)
                    risk_amount = pos_size * risk_per_unit
                    total_risk_amount += risk_amount

            # Get account balance
            account_balance = 0
            try:
                if hasattr(self.exchange_api, 'get_balance'):
                    balance = self.exchange_api.get_balance()
                    if balance and 'total' in balance and 'USDT' in balance['total']:
                        account_balance = balance['total']['USDT']
            except Exception as e:
                logger.error(f"Error getting account balance: {e}")
                return position_size * 0.5  # Conservative approach

            if account_balance <= 0:
                return position_size * 0.5  # Conservative approach

            # Calculate new position risk
            new_position_risk = position_size * abs(entry_price - (entry_price * 0.98))  # Assume 2% stop loss

            # Calculate total risk percentage
            total_risk_pct = ((total_risk_amount + new_position_risk) / account_balance) * 100

            if total_risk_pct > self.max_combined_risk_pct:
                # Scale down position size to meet combined risk limit
                available_risk = (self.max_combined_risk_pct / 100) * account_balance - total_risk_amount
                if available_risk <= 0:
                    logger.warning(f"Combined risk limit reached ({total_risk_pct:.2f}% > {self.max_combined_risk_pct}%). No new positions allowed.")
                    return 0

                # Calculate adjusted position size
                adjusted_size = available_risk / abs(entry_price - (entry_price * 0.98))
                logger.info(f"Adjusting position size for combined risk limit: {position_size:.6f} -> {adjusted_size:.6f}")
                return adjusted_size

            return position_size

        except Exception as e:
            logger.error(f"Error applying combined risk limit: {e}")
            return position_size * 0.5  # Conservative approach

    def _ensure_minimum_size(self, position_size, entry_price):
        """
        Ensure position size meets minimum requirements

        Args:
            position_size (float): Position size
            entry_price (float): Entry price

        Returns:
            float: Adjusted position size
        """
        try:
            # Check minimum position size
            if position_size < self.min_position_size:
                logger.info(f"Position size {position_size:.6f} is below minimum {self.min_position_size}. Adjusting.")
                return 0  # Return 0 to indicate no trade

            # Check minimum notional value
            notional_value = position_size * entry_price
            if notional_value < self.min_notional:
                logger.info(f"Notional value {notional_value:.2f} USDT is below minimum {self.min_notional} USDT. Adjusting.")
                return 0  # Return 0 to indicate no trade

            return position_size

        except Exception as e:
            logger.error(f"Error ensuring minimum size: {e}")
            return 0  # Conservative approach
