"""
Binance Error Handler Module

This module implements enhanced error handling for Binance API connectivity.
"""

import time
import logging
import requests
import datetime
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class BinanceErrorHandler:
    """
    Binance Error Handler
    
    Handles errors and connectivity issues with Binance API.
    """
    
    def __init__(self, max_retries=5, retry_delay=5, error_log_path="logs/binance_errors.json"):
        """
        Initialize the Binance error handler
        
        Args:
            max_retries (int): Maximum number of retries
            retry_delay (int): Initial delay between retries (seconds)
            error_log_path (str): Path to error log file
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.error_log_path = Path(error_log_path)
        
        # Ensure log directory exists
        self.error_log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Error tracking
        self.errors = self._load_errors()
        
        # Connectivity state
        self.last_successful_connection = None
        self.consecutive_failures = 0
        
        logger.info("BinanceErrorHandler initialized")
    
    def _load_errors(self):
        """
        Load errors from log file
        
        Returns:
            list: List of errors
        """
        try:
            if self.error_log_path.exists():
                with open(self.error_log_path, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Error loading error log: {e}")
            return []
    
    def _save_errors(self):
        """
        Save errors to log file
        """
        try:
            with open(self.error_log_path, 'w') as f:
                json.dump(self.errors, f, indent=4)
        except Exception as e:
            logger.error(f"Error saving error log: {e}")
    
    def log_error(self, error_type, error_message, context=None):
        """
        Log an error
        
        Args:
            error_type (str): Type of error
            error_message (str): Error message
            context (dict, optional): Additional context
            
        Returns:
            dict: Error entry
        """
        # Create error entry
        error_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "type": error_type,
            "message": str(error_message),
            "context": context or {}
        }
        
        # Add to errors list
        self.errors.append(error_entry)
        
        # Save errors
        self._save_errors()
        
        # Update consecutive failures
        self.consecutive_failures += 1
        
        # Log error
        logger.error(f"Binance API error: {error_type} - {error_message}")
        
        return error_entry
    
    def handle_request_error(self, func, *args, **kwargs):
        """
        Handle request error with retry logic
        
        Args:
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Any: Function result or None if all retries fail
        """
        for attempt in range(self.max_retries):
            try:
                # Call function
                result = func(*args, **kwargs)
                
                # Reset consecutive failures on success
                self.consecutive_failures = 0
                self.last_successful_connection = datetime.datetime.now()
                
                return result
            
            except requests.exceptions.RequestException as e:
                # Log error
                error_type = type(e).__name__
                error_message = str(e)
                context = {
                    "attempt": attempt + 1,
                    "max_retries": self.max_retries,
                    "function": func.__name__ if hasattr(func, "__name__") else str(func)
                }
                
                self.log_error(error_type, error_message, context)
                
                # Check if we should retry
                if attempt < self.max_retries - 1:
                    # Calculate retry delay with exponential backoff
                    retry_delay = self.retry_delay * (2 ** attempt)
                    logger.info(f"Retrying in {retry_delay} seconds (attempt {attempt + 1}/{self.max_retries})...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"All {self.max_retries} retry attempts failed")
                    
                    # Check if we need to attempt recovery
                    if self.consecutive_failures >= 3:
                        self._attempt_recovery()
        
        return None
    
    def _attempt_recovery(self):
        """
        Attempt to recover from connectivity issues
        """
        logger.warning(f"Attempting to recover from connectivity issues (consecutive failures: {self.consecutive_failures})")
        
        # Check Binance API health
        try:
            response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
            
            if response.status_code == 200:
                logger.info("Binance API is reachable, but API calls are failing")
                
                # Check time synchronization
                self._check_time_synchronization()
            else:
                logger.warning(f"Binance API health check failed: Status code {response.status_code}")
        except Exception as e:
            logger.error(f"Binance API health check failed: {e}")
    
    def _check_time_synchronization(self):
        """
        Check time synchronization with Binance server
        
        Returns:
            bool: True if time is synchronized, False otherwise
        """
        try:
            # Get server time
            response = requests.get("https://api.binance.com/api/v3/time", timeout=5)
            
            if response.status_code == 200:
                server_time = response.json().get("serverTime", 0)
                local_time = int(time.time() * 1000)
                
                # Calculate time difference
                time_diff = abs(server_time - local_time)
                
                if time_diff > 1000:  # More than 1 second difference
                    logger.warning(f"Time synchronization issue detected: {time_diff}ms difference")
                    logger.warning(f"Server time: {server_time}, Local time: {local_time}")
                    return False
                else:
                    logger.info(f"Time is synchronized with Binance server (difference: {time_diff}ms)")
                    return True
            else:
                logger.warning(f"Failed to get server time: Status code {response.status_code}")
                return False
        
        except Exception as e:
            logger.error(f"Error checking time synchronization: {e}")
            return False
    
    def check_api_key_permissions(self, api_key, api_secret):
        """
        Check API key permissions
        
        Args:
            api_key (str): API key
            api_secret (str): API secret
            
        Returns:
            dict: API key permissions
        """
        try:
            import hmac
            import hashlib
            import time
            
            # Create timestamp
            timestamp = int(time.time() * 1000)
            
            # Create signature
            query_string = f"timestamp={timestamp}"
            signature = hmac.new(
                api_secret.encode('utf-8'),
                query_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            # Create request URL
            url = f"https://api.binance.com/api/v3/account?{query_string}&signature={signature}"
            
            # Send request
            headers = {
                "X-MBX-APIKEY": api_key
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                # Extract permissions from response
                account_info = response.json()
                permissions = {
                    "can_trade": True,
                    "can_withdraw": account_info.get("canWithdraw", False),
                    "can_deposit": account_info.get("canDeposit", False),
                    "account_type": account_info.get("accountType", "SPOT")
                }
                
                logger.info(f"API key permissions: {permissions}")
                return permissions
            else:
                logger.warning(f"Failed to check API key permissions: Status code {response.status_code}")
                return None
        
        except Exception as e:
            logger.error(f"Error checking API key permissions: {e}")
            return None
    
    def get_error_statistics(self):
        """
        Get error statistics
        
        Returns:
            dict: Error statistics
        """
        if not self.errors:
            return {
                "total_errors": 0,
                "error_types": {},
                "last_error": None
            }
        
        # Count error types
        error_types = {}
        for error in self.errors:
            error_type = error.get("type", "Unknown")
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        # Get last error
        last_error = self.errors[-1]
        
        # Calculate error rate
        if self.last_successful_connection:
            last_success = datetime.datetime.fromisoformat(self.last_successful_connection.isoformat())
            now = datetime.datetime.now()
            hours_since_last_success = (now - last_success).total_seconds() / 3600
        else:
            hours_since_last_success = 0
        
        # Create statistics
        statistics = {
            "total_errors": len(self.errors),
            "error_types": error_types,
            "last_error": last_error,
            "consecutive_failures": self.consecutive_failures,
            "hours_since_last_success": round(hours_since_last_success, 2)
        }
        
        return statistics
