"""
Paper Trading Test Script

This script runs the trading bot in paper trading mode for 24-48 hours
and generates a daily report of performance.
"""

import os
import sys
import time
import datetime
import json
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/paper_trading_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("paper_trading_test")

# Create logs directory if it doesn't exist
os.makedirs("logs", exist_ok=True)
os.makedirs("reports", exist_ok=True)

def run_paper_trading_test(duration_hours=24):
    """
    Run paper trading test for specified duration
    
    Args:
        duration_hours (int): Duration in hours
    """
    logger.info(f"Starting paper trading test for {duration_hours} hours")
    
    # Calculate end time
    start_time = datetime.datetime.now()
    end_time = start_time + datetime.timedelta(hours=duration_hours)
    
    logger.info(f"Test will run until: {end_time}")
    
    # Run the bot in paper trading mode
    try:
        # Import the bot module
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from bot import TradingBot
        
        # Initialize the bot in paper trading mode
        bot = TradingBot(paper_trading=True)
        
        # Run the bot until the end time
        while datetime.datetime.now() < end_time:
            # Run one iteration of the bot
            bot.run_iteration()
            
            # Sleep for 1 minute
            time.sleep(60)
            
            # Generate hourly report
            if datetime.datetime.now().minute == 0:
                generate_hourly_report(bot)
        
        # Generate final report
        generate_final_report(bot, start_time, end_time)
        
        logger.info("Paper trading test completed successfully")
        
    except Exception as e:
        logger.error(f"Error running paper trading test: {e}")
        return False
    
    return True

def generate_hourly_report(bot):
    """
    Generate hourly report
    
    Args:
        bot (TradingBot): Trading bot instance
    """
    try:
        # Get current time
        current_time = datetime.datetime.now()
        
        # Get bot statistics
        stats = bot.get_statistics()
        
        # Log statistics
        logger.info(f"Hourly report at {current_time}:")
        logger.info(f"  - Open positions: {stats['open_positions']}")
        logger.info(f"  - Completed trades: {stats['completed_trades']}")
        logger.info(f"  - Win rate: {stats['win_rate']:.2f}%")
        logger.info(f"  - Profit/Loss: {stats['pnl_amount']:.2f} USDT ({stats['pnl_percent']:.2f}%)")
        logger.info(f"  - AI model accuracy: {stats['ai_accuracy']}")
        
    except Exception as e:
        logger.error(f"Error generating hourly report: {e}")

def generate_final_report(bot, start_time, end_time):
    """
    Generate final report
    
    Args:
        bot (TradingBot): Trading bot instance
        start_time (datetime): Test start time
        end_time (datetime): Test end time
    """
    try:
        # Get bot statistics
        stats = bot.get_statistics()
        
        # Create report
        report = {
            "test_start": start_time.isoformat(),
            "test_end": end_time.isoformat(),
            "duration_hours": (end_time - start_time).total_seconds() / 3600,
            "statistics": stats,
            "trades": bot.get_trade_history(),
            "ai_performance": bot.get_ai_performance()
        }
        
        # Save report to file
        report_file = f"reports/paper_trading_report_{start_time.strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=4)
        
        # Generate human-readable summary
        summary_file = f"reports/paper_trading_summary_{start_time.strftime('%Y%m%d_%H%M%S')}.txt"
        with open(summary_file, 'w') as f:
            f.write(f"Paper Trading Test Summary\n")
            f.write(f"=========================\n\n")
            f.write(f"Test period: {start_time} to {end_time}\n")
            f.write(f"Duration: {report['duration_hours']:.2f} hours\n\n")
            
            f.write(f"Trading Statistics\n")
            f.write(f"-----------------\n")
            f.write(f"Total trades: {stats['total_trades']}\n")
            f.write(f"Winning trades: {stats['winning_trades']}\n")
            f.write(f"Losing trades: {stats['losing_trades']}\n")
            f.write(f"Win rate: {stats['win_rate']:.2f}%\n")
            f.write(f"Profit/Loss: {stats['pnl_amount']:.2f} USDT ({stats['pnl_percent']:.2f}%)\n")
            f.write(f"Average profit per winning trade: {stats.get('avg_profit', 0):.2f} USDT\n")
            f.write(f"Average loss per losing trade: {stats.get('avg_loss', 0):.2f} USDT\n")
            f.write(f"Risk-reward ratio: {stats.get('risk_reward_ratio', 0):.2f}\n\n")
            
            f.write(f"AI Model Performance\n")
            f.write(f"-------------------\n")
            for model, accuracy in stats['ai_accuracy'].items():
                f.write(f"{model}: {accuracy:.2f}%\n")
            
            f.write(f"\nDetailed Trade List\n")
            f.write(f"------------------\n")
            for i, trade in enumerate(report['trades']):
                f.write(f"Trade {i+1}:\n")
                f.write(f"  Symbol: {trade.get('symbol', 'N/A')}\n")
                f.write(f"  Side: {trade.get('side', 'N/A')}\n")
                f.write(f"  Entry price: ${trade.get('entry_price', 0):.2f}\n")
                f.write(f"  Exit price: ${trade.get('exit_price', 0):.2f}\n")
                f.write(f"  PnL: {trade.get('pnl_percent', 0):.2f}% (${trade.get('pnl_amount', 0):.2f})\n")
                f.write(f"  Entry time: {trade.get('entry_time', 'N/A')}\n")
                f.write(f"  Exit time: {trade.get('exit_time', 'N/A')}\n")
                f.write(f"  Exit reason: {trade.get('exit_reason', 'N/A')}\n\n")
        
        logger.info(f"Final report saved to {report_file}")
        logger.info(f"Summary report saved to {summary_file}")
        
    except Exception as e:
        logger.error(f"Error generating final report: {e}")

if __name__ == "__main__":
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Run paper trading test")
    parser.add_argument("--duration", type=int, default=24, help="Test duration in hours (default: 24)")
    args = parser.parse_args()
    
    # Run paper trading test
    run_paper_trading_test(args.duration)
