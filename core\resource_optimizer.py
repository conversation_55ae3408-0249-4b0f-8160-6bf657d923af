"""
Resource Optimizer Module

This module implements optimizations for RAM and CPU usage,
especially for low-power hardware.
"""

import os
import gc
import time
import psutil
import logging
import threading
from pathlib import Path

logger = logging.getLogger(__name__)

class ResourceOptimizer:
    """
    Resource Optimizer
    
    Optimizes RAM and CPU usage for better performance on low-power hardware.
    """
    
    def __init__(self, config=None):
        """
        Initialize the resource optimizer
        
        Args:
            config (dict, optional): Configuration parameters
        """
        # Default configuration
        self.config = config or {}
        
        # RAM optimization settings
        self.ram_threshold = self.config.get("ram_threshold", 75)  # 75% RAM usage threshold
        self.ram_critical_threshold = self.config.get("ram_critical_threshold", 90)  # 90% RAM usage critical threshold
        self.ram_check_interval = self.config.get("ram_check_interval", 60)  # 60 seconds
        
        # CPU optimization settings
        self.cpu_threshold = self.config.get("cpu_threshold", 85)  # 85% CPU usage threshold
        self.cpu_critical_threshold = self.config.get("cpu_critical_threshold", 95)  # 95% CPU usage critical threshold
        self.cpu_check_interval = self.config.get("cpu_check_interval", 30)  # 30 seconds
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        
        # Performance metrics
        self.metrics = {
            "ram_usage": [],
            "cpu_usage": [],
            "gc_collections": 0,
            "throttling_events": 0
        }
        
        logger.info("ResourceOptimizer initialized")
    
    def start_monitoring(self):
        """
        Start resource monitoring
        """
        if self.monitoring:
            logger.warning("Resource monitoring already started")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()
        
        logger.info("Resource monitoring started")
    
    def stop_monitoring(self):
        """
        Stop resource monitoring
        """
        if not self.monitoring:
            logger.warning("Resource monitoring not started")
            return
        
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        
        logger.info("Resource monitoring stopped")
    
    def _monitor_resources(self):
        """
        Monitor system resources
        """
        last_ram_check = 0
        last_cpu_check = 0
        
        while self.monitoring:
            current_time = time.time()
            
            # Check RAM usage
            if current_time - last_ram_check >= self.ram_check_interval:
                self._check_ram_usage()
                last_ram_check = current_time
            
            # Check CPU usage
            if current_time - last_cpu_check >= self.cpu_check_interval:
                self._check_cpu_usage()
                last_cpu_check = current_time
            
            # Sleep to avoid high CPU usage from the monitoring itself
            time.sleep(5)
    
    def _check_ram_usage(self):
        """
        Check RAM usage and optimize if necessary
        """
        try:
            # Get memory usage
            memory = psutil.virtual_memory()
            ram_percent = memory.percent
            
            # Store metric
            self.metrics["ram_usage"].append(ram_percent)
            if len(self.metrics["ram_usage"]) > 100:
                self.metrics["ram_usage"] = self.metrics["ram_usage"][-100:]
            
            logger.debug(f"RAM usage: {ram_percent:.1f}%")
            
            # Check if optimization is needed
            if ram_percent >= self.ram_critical_threshold:
                logger.warning(f"Critical RAM usage: {ram_percent:.1f}% >= {self.ram_critical_threshold}%")
                self._optimize_ram_critical()
            elif ram_percent >= self.ram_threshold:
                logger.warning(f"High RAM usage: {ram_percent:.1f}% >= {self.ram_threshold}%")
                self._optimize_ram()
        
        except Exception as e:
            logger.error(f"Error checking RAM usage: {e}")
    
    def _check_cpu_usage(self):
        """
        Check CPU usage and optimize if necessary
        """
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Store metric
            self.metrics["cpu_usage"].append(cpu_percent)
            if len(self.metrics["cpu_usage"]) > 100:
                self.metrics["cpu_usage"] = self.metrics["cpu_usage"][-100:]
            
            logger.debug(f"CPU usage: {cpu_percent:.1f}%")
            
            # Check if optimization is needed
            if cpu_percent >= self.cpu_critical_threshold:
                logger.warning(f"Critical CPU usage: {cpu_percent:.1f}% >= {self.cpu_critical_threshold}%")
                self._optimize_cpu_critical()
            elif cpu_percent >= self.cpu_threshold:
                logger.warning(f"High CPU usage: {cpu_percent:.1f}% >= {self.cpu_threshold}%")
                self._optimize_cpu()
        
        except Exception as e:
            logger.error(f"Error checking CPU usage: {e}")
    
    def _optimize_ram(self):
        """
        Optimize RAM usage
        """
        logger.info("Optimizing RAM usage")
        
        # Run garbage collection
        gc.collect()
        self.metrics["gc_collections"] += 1
        
        logger.info("RAM optimization completed")
    
    def _optimize_ram_critical(self):
        """
        Optimize RAM usage in critical situations
        """
        logger.warning("Critical RAM optimization in progress")
        
        # Run more aggressive garbage collection
        for i in range(3):
            gc.collect()
        self.metrics["gc_collections"] += 3
        
        # Clear caches if possible
        self._clear_caches()
        
        logger.warning("Critical RAM optimization completed")
    
    def _optimize_cpu(self):
        """
        Optimize CPU usage
        """
        logger.info("Optimizing CPU usage")
        
        # Throttle CPU-intensive operations
        self.metrics["throttling_events"] += 1
        
        logger.info("CPU optimization completed")
    
    def _optimize_cpu_critical(self):
        """
        Optimize CPU usage in critical situations
        """
        logger.warning("Critical CPU optimization in progress")
        
        # More aggressive CPU throttling
        self.metrics["throttling_events"] += 2
        
        logger.warning("Critical CPU optimization completed")
    
    def _clear_caches(self):
        """
        Clear various caches to free memory
        """
        # Clear Python's internal caches
        gc.collect()
        
        # Try to clear other caches
        try:
            import sys
            if hasattr(sys, "getsizeof"):
                # Clear function cache if exists
                for module_name in list(sys.modules.keys()):
                    module = sys.modules[module_name]
                    if hasattr(module, "cache_clear"):
                        try:
                            module.cache_clear()
                        except:
                            pass
        except Exception as e:
            logger.error(f"Error clearing caches: {e}")
    
    def get_system_info(self):
        """
        Get system information
        
        Returns:
            dict: System information
        """
        try:
            # Get CPU info
            cpu_count = psutil.cpu_count(logical=False)
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_freq = psutil.cpu_freq()
            if cpu_freq:
                cpu_freq_current = cpu_freq.current
                cpu_freq_max = cpu_freq.max
            else:
                cpu_freq_current = None
                cpu_freq_max = None
            
            # Get memory info
            memory = psutil.virtual_memory()
            total_memory_gb = memory.total / (1024 ** 3)
            available_memory_gb = memory.available / (1024 ** 3)
            
            # Get disk info
            disk = psutil.disk_usage('/')
            total_disk_gb = disk.total / (1024 ** 3)
            free_disk_gb = disk.free / (1024 ** 3)
            
            # Create system info dictionary
            system_info = {
                "cpu": {
                    "physical_cores": cpu_count,
                    "logical_cores": cpu_count_logical,
                    "frequency_current_mhz": cpu_freq_current,
                    "frequency_max_mhz": cpu_freq_max,
                    "usage_percent": psutil.cpu_percent(interval=1)
                },
                "memory": {
                    "total_gb": round(total_memory_gb, 2),
                    "available_gb": round(available_memory_gb, 2),
                    "usage_percent": memory.percent
                },
                "disk": {
                    "total_gb": round(total_disk_gb, 2),
                    "free_gb": round(free_disk_gb, 2),
                    "usage_percent": disk.percent
                }
            }
            
            return system_info
        
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {
                "error": str(e)
            }
    
    def check_binance_api_health(self):
        """
        Check Binance API health
        
        Returns:
            bool: True if API is healthy, False otherwise
        """
        try:
            import requests
            
            # Send a ping request to Binance API
            response = requests.get("https://api.binance.com/api/v3/ping", timeout=5)
            
            # Check if response is successful
            if response.status_code == 200:
                logger.info("Binance API health check: OK")
                return True
            else:
                logger.warning(f"Binance API health check failed: Status code {response.status_code}")
                return False
        
        except Exception as e:
            logger.error(f"Binance API health check failed: {e}")
            return False
    
    def optimize_for_low_power(self):
        """
        Apply optimizations for low-power hardware
        """
        logger.info("Applying optimizations for low-power hardware")
        
        # Set process priority
        try:
            import psutil
            process = psutil.Process(os.getpid())
            
            if os.name == 'nt':  # Windows
                process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            else:  # Unix-like
                process.nice(10)  # Lower priority (higher nice value)
            
            logger.info("Process priority adjusted for low-power mode")
        except Exception as e:
            logger.error(f"Error setting process priority: {e}")
        
        # Disable unnecessary background tasks
        self._disable_background_tasks()
        
        # Set conservative GC thresholds
        gc.set_threshold(700, 10, 10)  # More conservative than default
        
        logger.info("Low-power optimizations applied")
    
    def _disable_background_tasks(self):
        """
        Disable unnecessary background tasks
        """
        # This is a placeholder for disabling background tasks
        # In a real implementation, you would disable specific background tasks
        logger.info("Unnecessary background tasks disabled")
    
    def get_optimization_metrics(self):
        """
        Get optimization metrics
        
        Returns:
            dict: Optimization metrics
        """
        # Calculate average RAM and CPU usage
        avg_ram_usage = sum(self.metrics["ram_usage"]) / len(self.metrics["ram_usage"]) if self.metrics["ram_usage"] else 0
        avg_cpu_usage = sum(self.metrics["cpu_usage"]) / len(self.metrics["cpu_usage"]) if self.metrics["cpu_usage"] else 0
        
        # Create metrics dictionary
        metrics = {
            "avg_ram_usage": round(avg_ram_usage, 2),
            "avg_cpu_usage": round(avg_cpu_usage, 2),
            "gc_collections": self.metrics["gc_collections"],
            "throttling_events": self.metrics["throttling_events"],
            "current_ram_usage": round(psutil.virtual_memory().percent, 2),
            "current_cpu_usage": round(psutil.cpu_percent(interval=0), 2)
        }
        
        return metrics
