"""
Connection Handler Module

This module provides functionality for managing API connections,
handling connection errors, and implementing recovery strategies.
"""

import os
import time
import logging
import datetime
import json
import requests
import socket
import ntplib
from pathlib import Path
from requests.exceptions import RequestException, ConnectionError, Timeout, HTTPError
from utils.error_handler import (
    NetworkError,
    APIError,
    RateLimitError,
    AuthenticationError,
    log_error,
    handle_error
)

# Set up logging
logger = logging.getLogger("connection_handler")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/connection_handler.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Connection settings
DEFAULT_TIMEOUT = 10  # seconds
MAX_RETRIES = 3
RETRY_DELAY = 5  # seconds
RATE_LIMIT_COOLDOWN = 60  # seconds
CONNECTION_CHECK_INTERVAL = 300  # seconds (5 minutes)
NTP_SYNC_INTERVAL = 3600  # seconds (1 hour)

# NTP servers for time synchronization
NTP_SERVERS = [
    'pool.ntp.org',
    'time.google.com',
    'time.windows.com',
    'time.apple.com'
]

# API endpoints for connection testing
API_TEST_ENDPOINTS = {
    'binance': 'https://api.binance.com/api/v3/ping',
    'binance_testnet': 'https://testnet.binance.vision/api/v3/ping'
}


class ConnectionHandler:
    """
    Handles API connections, error recovery, and time synchronization
    """
    
    def __init__(self, exchange='binance', use_testnet=False):
        """
        Initialize the connection handler
        
        Parameters:
        exchange (str): Exchange name
        use_testnet (bool): Whether to use testnet
        """
        self.exchange = exchange
        self.use_testnet = use_testnet
        self.last_connection_check = 0
        self.last_ntp_sync = 0
        self.time_offset = 0
        self.connection_status = {
            'connected': False,
            'last_successful_connection': None,
            'consecutive_failures': 0,
            'rate_limit_hit': False,
            'rate_limit_reset': None
        }
        
        # Initialize connection
        self.check_connection()
        self.sync_time()
        
        logger.info(f"ConnectionHandler initialized for {exchange} {'testnet' if use_testnet else 'mainnet'}")
    
    def check_connection(self):
        """
        Check connection to the exchange API
        
        Returns:
        bool: True if connected, False otherwise
        """
        current_time = time.time()
        
        # Only check connection if enough time has passed since last check
        if current_time - self.last_connection_check < CONNECTION_CHECK_INTERVAL:
            return self.connection_status['connected']
        
        self.last_connection_check = current_time
        
        try:
            # Determine API endpoint
            endpoint_key = f"{self.exchange}_testnet" if self.use_testnet else self.exchange
            endpoint = API_TEST_ENDPOINTS.get(endpoint_key)
            
            if not endpoint:
                logger.warning(f"No test endpoint defined for {endpoint_key}")
                return False
            
            # Send ping request
            response = requests.get(endpoint, timeout=DEFAULT_TIMEOUT)
            
            # Check response
            if response.status_code == 200:
                self.connection_status['connected'] = True
                self.connection_status['last_successful_connection'] = current_time
                self.connection_status['consecutive_failures'] = 0
                
                logger.info(f"Connection to {self.exchange} API successful")
                return True
            else:
                self.connection_status['connected'] = False
                self.connection_status['consecutive_failures'] += 1
                
                logger.warning(f"Connection to {self.exchange} API failed: HTTP {response.status_code}")
                return False
        
        except Exception as e:
            self.connection_status['connected'] = False
            self.connection_status['consecutive_failures'] += 1
            
            logger.error(f"Connection to {self.exchange} API failed: {e}")
            return False
    
    def sync_time(self):
        """
        Synchronize time with NTP servers
        
        Returns:
        bool: True if time was synchronized, False otherwise
        """
        current_time = time.time()
        
        # Only sync time if enough time has passed since last sync
        if current_time - self.last_ntp_sync < NTP_SYNC_INTERVAL:
            return True
        
        self.last_ntp_sync = current_time
        
        for server in NTP_SERVERS:
            try:
                # Create NTP client
                client = ntplib.NTPClient()
                
                # Query NTP server
                response = client.request(server, timeout=DEFAULT_TIMEOUT)
                
                # Calculate time offset
                self.time_offset = response.offset
                
                logger.info(f"Time synchronized with {server}: offset {self.time_offset:.6f}s")
                return True
            
            except Exception as e:
                logger.warning(f"Failed to sync time with {server}: {e}")
                continue
        
        logger.error("Failed to sync time with any NTP server")
        return False
    
    def get_server_time(self):
        """
        Get current time adjusted for server offset
        
        Returns:
        float: Current time in seconds since epoch, adjusted for server offset
        """
        return time.time() + self.time_offset
    
    def handle_request_error(self, error, context=None):
        """
        Handle request error and determine appropriate action
        
        Parameters:
        error (Exception): The error to handle
        context (dict, optional): Additional context information
        
        Returns:
        dict: Error handling result
        """
        # Determine error type
        if isinstance(error, ConnectionError):
            error_type = NetworkError(f"Connection error: {error}", details=context)
        elif isinstance(error, Timeout):
            error_type = NetworkError(f"Request timeout: {error}", details=context)
        elif isinstance(error, HTTPError):
            # Check for specific HTTP errors
            if hasattr(error, 'response') and error.response is not None:
                status_code = error.response.status_code
                
                if status_code == 401:
                    error_type = AuthenticationError(f"Authentication failed: {error}", details=context)
                elif status_code == 429:
                    # Rate limit exceeded
                    error_type = RateLimitError(f"Rate limit exceeded: {error}", details=context)
                    
                    # Set rate limit status
                    self.connection_status['rate_limit_hit'] = True
                    self.connection_status['rate_limit_reset'] = time.time() + RATE_LIMIT_COOLDOWN
                else:
                    error_type = APIError(f"HTTP error {status_code}: {error}", details=context)
            else:
                error_type = APIError(f"HTTP error: {error}", details=context)
        else:
            error_type = APIError(f"API error: {error}", details=context)
        
        # Handle the error
        return handle_error(error_type, context)
    
    def execute_request(self, request_func, *args, max_retries=None, retry_delay=None, context=None, **kwargs):
        """
        Execute a request with error handling and automatic retries
        
        Parameters:
        request_func (callable): Function to execute the request
        *args: Positional arguments for the function
        max_retries (int, optional): Maximum number of retry attempts
        retry_delay (int, optional): Delay between retries in seconds
        context (dict, optional): Additional context information
        **kwargs: Keyword arguments for the function
        
        Returns:
        tuple: (response, error) - response if successful, error if failed
        """
        # Use default values if not provided
        max_retries = max_retries if max_retries is not None else MAX_RETRIES
        retry_delay = retry_delay if retry_delay is not None else RETRY_DELAY
        
        # Check if rate limited
        if self.connection_status['rate_limit_hit']:
            if self.connection_status['rate_limit_reset'] and time.time() < self.connection_status['rate_limit_reset']:
                wait_time = self.connection_status['rate_limit_reset'] - time.time()
                logger.warning(f"Rate limit in effect, waiting {wait_time:.2f}s before retrying")
                time.sleep(wait_time)
            
            # Reset rate limit status
            self.connection_status['rate_limit_hit'] = False
            self.connection_status['rate_limit_reset'] = None
        
        # Execute request with retries
        retries = 0
        last_error = None
        
        while retries <= max_retries:
            try:
                # Check connection before making request
                if not self.check_connection():
                    logger.warning("Connection check failed, attempting request anyway")
                
                # Execute the request
                response = request_func(*args, **kwargs)
                
                # Update connection status
                self.connection_status['connected'] = True
                self.connection_status['last_successful_connection'] = time.time()
                self.connection_status['consecutive_failures'] = 0
                
                return response, None
            
            except Exception as e:
                # Handle the error
                last_error = e
                error_result = self.handle_request_error(e, context)
                
                # Check if error is critical
                if error_result.get("is_critical", False):
                    logger.error(f"Critical error occurred, aborting: {e}")
                    return None, last_error
                
                # Determine if we should retry
                if retries < max_retries:
                    # Calculate backoff time
                    backoff = error_result.get("backoff_seconds", retry_delay) * (retries + 1)
                    
                    logger.warning(f"Retry {retries + 1}/{max_retries} after {backoff}s: {e}")
                    
                    # Wait before retrying
                    time.sleep(backoff)
                    
                    retries += 1
                else:
                    logger.error(f"Max retries ({max_retries}) reached: {e}")
                    return None, last_error
        
        return None, last_error
    
    def recover_connection(self):
        """
        Attempt to recover connection to the exchange API
        
        Returns:
        bool: True if connection was recovered, False otherwise
        """
        logger.info("Attempting to recover connection...")
        
        # Reset connection status
        self.connection_status['connected'] = False
        
        # Sync time
        self.sync_time()
        
        # Check connection
        if self.check_connection():
            logger.info("Connection recovered successfully")
            return True
        
        # Try alternative endpoints
        logger.info("Trying alternative endpoints...")
        
        # TODO: Implement alternative endpoint logic
        
        # Check connection again
        if self.check_connection():
            logger.info("Connection recovered using alternative endpoint")
            return True
        
        logger.error("Failed to recover connection")
        return False
