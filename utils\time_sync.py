"""
Time Synchronization Module

This module provides utilities for synchronizing time with NTP servers
and handling timestamp errors in API requests.
"""

import os
import time
import socket
import struct
import logging
import datetime
import threading
from typing import Optional, List, Tuple, Dict, Any

# Configure logging
logger = logging.getLogger(__name__)

# NTP servers for time synchronization
NTP_SERVERS = [
    'pool.ntp.org',
    'time.google.com',
    'time.windows.com',
    'time.apple.com'
]

# NTP constants
NTP_PORT = 123
NTP_PACKET_FORMAT = '!12I'
NTP_DELTA = 2208988800  # 1970-01-01 00:00:00 - 1900-01-01 00:00:00
NTP_QUERY = b'\x1b' + 47 * b'\0'

class TimeSync:
    """
    Time synchronization utility for handling timestamp errors
    """
    
    def __init__(self, ntp_servers: List[str] = None, sync_interval: int = 3600):
        """
        Initialize the time synchronization utility
        
        Args:
            ntp_servers (List[str], optional): List of NTP servers to use
            sync_interval (int, optional): Interval between synchronizations in seconds
        """
        self.ntp_servers = ntp_servers or NTP_SERVERS
        self.sync_interval = sync_interval
        self.time_offset = 0
        self.last_sync = 0
        self.sync_lock = threading.Lock()
        self.sync_thread = None
        self.stop_event = threading.Event()
        
        # Perform initial synchronization
        self.sync_time()
        
        logger.info("TimeSync initialized")
    
    def sync_time(self) -> bool:
        """
        Synchronize time with NTP servers
        
        Returns:
            bool: True if synchronization was successful, False otherwise
        """
        with self.sync_lock:
            try:
                # Try each NTP server until one succeeds
                for server in self.ntp_servers:
                    try:
                        # Get NTP time
                        ntp_time = self._get_ntp_time(server)
                        if ntp_time:
                            # Calculate offset
                            local_time = time.time()
                            self.time_offset = ntp_time - local_time
                            self.last_sync = local_time
                            
                            logger.info(f"Time synchronized with {server}. Offset: {self.time_offset:.3f} seconds")
                            return True
                    except Exception as e:
                        logger.warning(f"Failed to sync time with {server}: {e}")
                
                logger.error("Failed to synchronize time with any NTP server")
                return False
            
            except Exception as e:
                logger.error(f"Error synchronizing time: {e}")
                return False
    
    def _get_ntp_time(self, server: str) -> Optional[float]:
        """
        Get time from an NTP server
        
        Args:
            server (str): NTP server address
            
        Returns:
            float: NTP time in seconds since the epoch
        """
        try:
            # Create socket
            client = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            client.settimeout(5)
            
            # Send request
            client.sendto(NTP_QUERY, (server, NTP_PORT))
            
            # Receive response
            msg, _ = client.recvfrom(1024)
            
            # Parse response
            unpacked = struct.unpack(NTP_PACKET_FORMAT, msg[0:48])
            
            # Extract time
            t = unpacked[10] + float(unpacked[11]) / 2**32 - NTP_DELTA
            
            return t
        
        except Exception as e:
            logger.warning(f"Error getting NTP time from {server}: {e}")
            return None
        
        finally:
            client.close()
    
    def get_adjusted_time(self) -> float:
        """
        Get time adjusted by the NTP offset
        
        Returns:
            float: Adjusted time in seconds since the epoch
        """
        # Check if we need to resync
        if time.time() - self.last_sync > self.sync_interval:
            self.sync_time()
        
        return time.time() + self.time_offset
    
    def get_adjusted_timestamp(self, ms: bool = False) -> int:
        """
        Get timestamp adjusted by the NTP offset
        
        Args:
            ms (bool): Whether to return timestamp in milliseconds
            
        Returns:
            int: Adjusted timestamp
        """
        adjusted_time = self.get_adjusted_time()
        
        if ms:
            return int(adjusted_time * 1000)
        else:
            return int(adjusted_time)
    
    def start_sync_thread(self) -> None:
        """
        Start a background thread for periodic time synchronization
        """
        if self.sync_thread and self.sync_thread.is_alive():
            logger.warning("Sync thread is already running")
            return
        
        self.stop_event.clear()
        self.sync_thread = threading.Thread(target=self._sync_thread_func, daemon=True)
        self.sync_thread.start()
        
        logger.info(f"Time sync thread started with interval {self.sync_interval} seconds")
    
    def stop_sync_thread(self) -> None:
        """
        Stop the background synchronization thread
        """
        if self.sync_thread and self.sync_thread.is_alive():
            self.stop_event.set()
            self.sync_thread.join(timeout=5)
            logger.info("Time sync thread stopped")
    
    def _sync_thread_func(self) -> None:
        """
        Background thread function for periodic time synchronization
        """
        while not self.stop_event.is_set():
            try:
                # Sync time
                self.sync_time()
                
                # Sleep until next sync
                for _ in range(self.sync_interval):
                    if self.stop_event.is_set():
                        break
                    time.sleep(1)
            
            except Exception as e:
                logger.error(f"Error in sync thread: {e}")
                time.sleep(60)  # Sleep for a minute before retrying
    
    def adjust_timestamp_for_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Adjust timestamp in request parameters
        
        Args:
            params (Dict[str, Any]): Request parameters
            
        Returns:
            Dict[str, Any]: Adjusted parameters
        """
        # Create a copy of the parameters
        adjusted_params = params.copy()
        
        # Add or update timestamp
        if 'timestamp' in adjusted_params:
            adjusted_params['timestamp'] = self.get_adjusted_timestamp(ms=True)
        
        return adjusted_params

# Global instance
time_sync = TimeSync()

def get_adjusted_timestamp(ms: bool = False) -> int:
    """
    Get timestamp adjusted by the NTP offset
    
    Args:
        ms (bool): Whether to return timestamp in milliseconds
        
    Returns:
        int: Adjusted timestamp
    """
    return time_sync.get_adjusted_timestamp(ms=ms)

def adjust_request_timestamp(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Adjust timestamp in request parameters
    
    Args:
        params (Dict[str, Any]): Request parameters
        
    Returns:
        Dict[str, Any]: Adjusted parameters
    """
    return time_sync.adjust_timestamp_for_request(params)

def start_time_sync() -> None:
    """
    Start background time synchronization
    """
    time_sync.start_sync_thread()

def stop_time_sync() -> None:
    """
    Stop background time synchronization
    """
    time_sync.stop_sync_thread()
