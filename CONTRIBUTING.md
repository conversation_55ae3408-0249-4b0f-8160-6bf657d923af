# Contributing to <PERSON><PERSON>Bo<PERSON> Enhanced

Thank you for your interest in contributing to SP.Bot Enhanced! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Environment](#development-environment)
- [Coding Standards](#coding-standards)
- [Pull Request Process](#pull-request-process)
- [Testing](#testing)
- [Reporting Bugs](#reporting-bugs)
- [Feature Requests](#feature-requests)
- [Documentation](#documentation)

## Code of Conduct

By participating in this project, you agree to maintain a respectful and inclusive environment for everyone. Please be considerate of others and their perspectives.

## Getting Started

1. Fork the repository on GitHub
2. Clone your fork locally
3. Set up the development environment
4. Create a new branch for your feature or bug fix
5. Make your changes
6. Run tests to ensure your changes don't break existing functionality
7. Submit a pull request

## Development Environment

### Prerequisites

- Python 3.8 or higher
- Git
- A Binance account (for testing)

### Setup

1. Clone your fork of the repository:
   ```bash
   git clone https://github.com/yourusername/sp.bot.git
   cd sp.bot
   ```

2. Create a virtual environment:
   ```bash
   python -m venv .venv
   ```

3. Activate the virtual environment:
   - Windows:
     ```bash
     .venv\Scripts\activate
     ```
   - Linux/Mac:
     ```bash
     source .venv/bin/activate
     ```

4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

5. Install development dependencies:
   ```bash
   pip install -r requirements-dev.txt
   ```

## Coding Standards

We follow PEP 8 style guidelines for Python code. Please ensure your code adheres to these standards.

### General Guidelines

- Use 4 spaces for indentation (not tabs)
- Maximum line length of 100 characters
- Use meaningful variable and function names
- Write docstrings for all functions, classes, and modules
- Add comments for complex code sections
- Keep functions small and focused on a single task
- Use type hints where appropriate

### Documentation

- All functions, classes, and modules should have docstrings
- Use Google-style docstrings
- Include parameter descriptions, return types, and exceptions raised
- Provide examples for complex functions

## Pull Request Process

1. Create a new branch for your feature or bug fix:
   ```bash
   git checkout -b feature/your-feature-name
   ```
   or
   ```bash
   git checkout -b fix/your-bug-fix
   ```

2. Make your changes and commit them with clear, descriptive commit messages:
   ```bash
   git commit -m "Add feature: description of your feature"
   ```

3. Push your branch to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

4. Submit a pull request to the main repository
   - Provide a clear description of the changes
   - Reference any related issues
   - Explain the purpose and benefits of your changes

5. Address any feedback from code reviews
   - Make requested changes
   - Push additional commits to your branch
   - The pull request will update automatically

6. Once approved, your pull request will be merged by a maintainer

## Testing

All new features and bug fixes should include appropriate tests.

### Running Tests

```bash
pytest tests/
```

For more comprehensive testing with coverage reports:

```bash
pytest tests/ -v --cov=. --cov-report=html
```

### Writing Tests

- Place tests in the `tests/` directory
- Name test files with the prefix `test_`
- Use descriptive test function names that explain what is being tested
- Include both positive and negative test cases
- Mock external dependencies (like API calls)

## Reporting Bugs

When reporting bugs, please include:

1. A clear, descriptive title
2. Steps to reproduce the bug
3. Expected behavior
4. Actual behavior
5. Screenshots or logs (if applicable)
6. Environment information:
   - Python version
   - Operating system
   - Bot version
   - Any relevant configuration settings

## Feature Requests

When requesting new features, please include:

1. A clear, descriptive title
2. A detailed description of the proposed feature
3. The problem it solves or the value it adds
4. Any potential implementation ideas
5. Any relevant references or examples

## Documentation

Improvements to documentation are always welcome. This includes:

- README updates
- Inline code comments
- Function docstrings
- User guides
- API documentation
- Example configurations

Thank you for contributing to SP.Bot Enhanced!
