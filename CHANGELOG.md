# Changelog

All notable changes to SP.Bot Enhanced will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2023-06-15

### Added
- AI model integration with three providers:
  - OpenAI (30% weight)
  - DeepSeek (35% weight)
  - Qwen (35% weight)
- Debate Mode for resolving conflicting signals
- Paper Trading Mode for risk-free testing
- Symbol-specific trading hours
- Bollinger Bands indicator
- Fibonacci Retracement indicator
- Market regime detection (stable/volatile)
- Stacked entry strategy (50% initial, 50% confirmation)
- Dynamic risk management based on account size
- Comprehensive unit testing framework
- Daily reporting system
- Long-term AI model evaluation
- Enhanced error handling and connection recovery
- Security enhancements for API key storage
- Memory optimization for low-end hardware

### Changed
- Reduced Qwen weight from 50% to 35%
- Increased DeepSeek weight from 20% to 35%
- Adjusted take profit levels from 1% to 0.7-1.0%
- Adjusted stop loss levels from 0.5% to 0.8-1.0%
- Reduced risk for small accounts from 2% to 1%
- Reduced risk for very small accounts to 0.5%
- Lowered leverage for small accounts from 3x to 2x
- Lowered leverage for very small accounts to 1.5x
- Relaxed technical conditions from 4-of-5 to 3-of-5 requirements
- Improved trailing stop activation (0.3% vs 0.5%)
- Enhanced documentation with comprehensive README

### Fixed
- Memory leaks in long-running processes
- API connection errors and recovery
- Timestamp synchronization issues
- Error handling for rejected orders
- Resource monitoring for low-end hardware
- Path handling for cross-platform compatibility

## [1.5.0] - 2023-04-10

### Added
- Initial AI model integration
- Basic technical indicators (EMA, RSI, MACD, ATR)
- Isolated margin trading
- Dynamic position sizing
- Stop loss and take profit management
- Basic error handling
- Logging system

### Changed
- Improved EMA crossover strategy
- Enhanced risk management
- Updated API integration

### Fixed
- Various bug fixes and stability improvements

## [1.0.0] - 2023-02-01

### Added
- Initial release
- Basic trading functionality
- Binance API integration
- EMA crossover strategy
- Simple risk management
- Basic logging
