"""
Unit tests for market condition detection functionality.
"""

import unittest
import os
import sys
import numpy as np
import pandas as pd
from unittest.mock import patch, MagicMock

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from analysis.indicators import (
    is_market_volatile,
    is_atr_high,
    is_atr_rising_rapidly,
    calculate_bollinger_bands,
    is_bollinger_squeeze,
    is_bollinger_breakout,
    calculate_fibonacci_retracement,
    identify_fibonacci_support_resistance
)


class TestMarketCondition(unittest.TestCase):
    """Test cases for market condition detection functionality."""

    def setUp(self):
        """Set up test environment before each test."""
        # Create sample price data
        self.sample_prices = np.linspace(30000, 31000, 100)
        
        # Add some volatility
        np.random.seed(42)
        self.sample_prices += np.random.normal(0, 100, 100)
        
        # Create sample OHLC data
        self.sample_high = self.sample_prices + np.random.normal(0, 50, 100)
        self.sample_low = self.sample_prices - np.random.normal(0, 50, 100)
        self.sample_open = self.sample_prices - np.random.normal(0, 30, 100)
        self.sample_close = self.sample_prices.copy()
        
        # Create sample ATR values
        self.sample_atr = np.linspace(100, 200, 30)
        
        # Create volatile ATR values
        self.volatile_atr = np.linspace(100, 400, 30)
    
    def test_is_market_volatile_with_default_currency(self):
        """Test market volatility detection with default currency."""
        # Test with normal ATR values
        result = is_market_volatile(self.sample_atr)
        self.assertFalse(result)
        
        # Test with volatile ATR values
        result = is_market_volatile(self.volatile_atr)
        self.assertTrue(result)
    
    def test_is_market_volatile_with_specific_currency(self):
        """Test market volatility detection with specific currency."""
        # Test with BTC (base multiplier)
        result = is_market_volatile(self.volatile_atr, currency="BTC/USDT")
        self.assertTrue(result)
        
        # Test with SOL (3x more volatile)
        result = is_market_volatile(self.volatile_atr, currency="SOL/USDT")
        # SOL has higher threshold due to 3x multiplier, so might not be considered volatile
        self.assertFalse(result)
    
    def test_is_atr_high(self):
        """Test ATR high detection."""
        # Create ATR values with a spike at the end
        atr_values = np.linspace(100, 150, 30)
        atr_values[-1] = 300  # Spike at the end
        
        # Test with normal threshold
        result = is_atr_high(atr_values)
        self.assertTrue(result)
        
        # Test with higher threshold
        result = is_atr_high(atr_values, threshold=3.0)
        self.assertFalse(result)
    
    def test_is_atr_rising_rapidly(self):
        """Test ATR rising rapidly detection."""
        # Create ATR values with a gradual rise
        atr_values = np.linspace(100, 150, 30)
        
        # Test with normal threshold
        result = is_atr_rising_rapidly(atr_values)
        self.assertFalse(result)
        
        # Create ATR values with a rapid rise
        atr_values = np.linspace(100, 150, 30)
        atr_values[-1] = 300  # Rapid rise at the end
        
        # Test with normal threshold
        result = is_atr_rising_rapidly(atr_values)
        self.assertTrue(result)
    
    def test_calculate_bollinger_bands(self):
        """Test Bollinger Bands calculation."""
        # Calculate Bollinger Bands
        upper_band, middle_band, lower_band = calculate_bollinger_bands(self.sample_close)
        
        # Check that bands were calculated correctly
        self.assertEqual(len(upper_band), len(self.sample_close))
        self.assertEqual(len(middle_band), len(self.sample_close))
        self.assertEqual(len(lower_band), len(self.sample_close))
        
        # Check that upper band is above middle band
        self.assertTrue(all(u > m for u, m in zip(upper_band[20:], middle_band[20:])))
        
        # Check that lower band is below middle band
        self.assertTrue(all(l < m for l, m in zip(lower_band[20:], middle_band[20:])))
    
    def test_is_bollinger_squeeze(self):
        """Test Bollinger Band squeeze detection."""
        # Calculate Bollinger Bands
        upper_band, middle_band, lower_band = calculate_bollinger_bands(self.sample_close)
        
        # Test with normal bands
        result = is_bollinger_squeeze(upper_band, lower_band)
        self.assertFalse(result)
        
        # Create squeezed bands
        squeezed_upper = middle_band.copy()
        squeezed_lower = middle_band.copy()
        
        # Add small deviation
        squeezed_upper += 50
        squeezed_lower -= 50
        
        # Test with squeezed bands
        result = is_bollinger_squeeze(squeezed_upper, squeezed_lower)
        self.assertTrue(result)
    
    def test_is_bollinger_breakout(self):
        """Test Bollinger Band breakout detection."""
        # Calculate Bollinger Bands
        upper_band, middle_band, lower_band = calculate_bollinger_bands(self.sample_close)
        
        # Create prices that break above upper band
        breakout_prices = self.sample_close.copy()
        breakout_prices[-1] = upper_band[-1] * 1.1  # 10% above upper band
        
        # Test upper breakout
        upper_breakout, lower_breakout = is_bollinger_breakout(breakout_prices, upper_band, lower_band)
        self.assertTrue(upper_breakout)
        self.assertFalse(lower_breakout)
        
        # Create prices that break below lower band
        breakdown_prices = self.sample_close.copy()
        breakdown_prices[-1] = lower_band[-1] * 0.9  # 10% below lower band
        
        # Test lower breakout
        upper_breakout, lower_breakout = is_bollinger_breakout(breakdown_prices, upper_band, lower_band)
        self.assertFalse(upper_breakout)
        self.assertTrue(lower_breakout)
    
    def test_calculate_fibonacci_retracement(self):
        """Test Fibonacci retracement calculation."""
        # Calculate Fibonacci retracement
        high = 31000
        low = 30000
        fib_levels = calculate_fibonacci_retracement(high, low)
        
        # Check that levels were calculated correctly
        self.assertEqual(fib_levels[0], high)
        self.assertEqual(fib_levels[1], low)
        
        # Check 0.5 level (should be halfway between high and low)
        self.assertAlmostEqual(fib_levels[0.5], high - (high - low) * 0.5)
        
        # Check 0.618 level (golden ratio)
        self.assertAlmostEqual(fib_levels[0.618], high - (high - low) * 0.618)
    
    def test_identify_fibonacci_support_resistance(self):
        """Test Fibonacci support/resistance identification."""
        # Calculate Fibonacci retracement
        high = 31000
        low = 30000
        fib_levels = calculate_fibonacci_retracement(high, low)
        
        # Test price at 0.5 level
        price_at_fib = high - (high - low) * 0.5
        is_at_level, level, level_type = identify_fibonacci_support_resistance(price_at_fib, fib_levels)
        
        # Check that level was identified correctly
        self.assertTrue(is_at_level)
        self.assertEqual(level, 0.5)
        
        # Test price not at any Fibonacci level
        random_price = 30123
        is_at_level, level, level_type = identify_fibonacci_support_resistance(random_price, fib_levels)
        
        # Check that no level was identified
        self.assertFalse(is_at_level)


class TestMarketRegimeDetection(unittest.TestCase):
    """Test cases for market regime detection."""
    
    def setUp(self):
        """Set up test environment before each test."""
        # Create sample market data
        self.stable_market_data = {
            "close_prices": np.linspace(30000, 30500, 100),
            "high_prices": np.linspace(30050, 30550, 100),
            "low_prices": np.linspace(29950, 30450, 100),
            "volume": np.ones(100) * 100,
            "atr_values": np.ones(30) * 100,
            "rsi_values": np.linspace(40, 60, 14),
            "macd_line": np.linspace(-1, 1, 26),
            "signal_line": np.linspace(-0.5, 1.5, 26),
            "histogram": np.linspace(-0.5, 0.5, 26)
        }
        
        self.volatile_market_data = {
            "close_prices": np.linspace(30000, 32000, 100) + np.random.normal(0, 200, 100),
            "high_prices": np.linspace(30200, 32200, 100) + np.random.normal(0, 200, 100),
            "low_prices": np.linspace(29800, 31800, 100) + np.random.normal(0, 200, 100),
            "volume": np.ones(100) * 300,
            "atr_values": np.ones(30) * 300,
            "rsi_values": np.concatenate([np.linspace(30, 40, 7), np.linspace(60, 70, 7)]),
            "macd_line": np.concatenate([np.linspace(-3, -1, 13), np.linspace(1, 3, 13)]),
            "signal_line": np.concatenate([np.linspace(-2, 0, 13), np.linspace(0, 2, 13)]),
            "histogram": np.concatenate([np.linspace(-1, 0, 13), np.linspace(0, 1, 13)])
        }
    
    def test_detect_market_regime(self):
        """Test market regime detection."""
        # Import the function to test
        from analysis.market_evaluator import detect_market_regime
        
        # Test with stable market data
        regime = detect_market_regime(self.stable_market_data)
        self.assertEqual(regime, "stable")
        
        # Test with volatile market data
        regime = detect_market_regime(self.volatile_market_data)
        self.assertEqual(regime, "volatile")
    
    def test_detect_sudden_changes(self):
        """Test sudden market change detection."""
        # Import the function to test
        from analysis.market_evaluator import detect_sudden_changes
        
        # Test with stable market data
        changes = detect_sudden_changes(self.stable_market_data)
        self.assertEqual(len(changes), 0)
        
        # Create data with sudden changes
        sudden_change_data = self.stable_market_data.copy()
        sudden_change_data["rsi_values"] = np.concatenate([np.ones(7) * 40, np.ones(7) * 70])
        
        # Test with data containing sudden changes
        changes = detect_sudden_changes(sudden_change_data)
        self.assertGreater(len(changes), 0)
        self.assertIn("rsi", changes)


if __name__ == "__main__":
    unittest.main()
