"""
Enhanced Trading Hours Management with Rest Periods and Timezone Support
Manages trading schedules, rest periods, and market session optimization
"""

import os
import json
import logging
import datetime
import pytz
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import yaml

# Set up logging
logger = logging.getLogger("enhanced_trading_hours")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/enhanced_trading_hours.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)


@dataclass
class TradingSession:
    """Data class for trading sessions"""
    name: str
    start_hour: int
    start_minute: int
    end_hour: int
    end_minute: int
    timezone: str
    active: bool = True
    max_trades: Optional[int] = None
    rest_after_session: int = 0  # Minutes of rest after session


@dataclass
class RestPeriod:
    """Data class for rest periods"""
    name: str
    start_hour: int
    start_minute: int
    end_hour: int
    end_minute: int
    timezone: str
    reason: str
    active: bool = True


@dataclass
class TradingStats:
    """Data class for trading session statistics"""
    session_name: str
    trades_count: int
    successful_trades: int
    total_pnl: float
    average_pnl: float
    best_hour: Optional[int]
    worst_hour: Optional[int]


class EnhancedTradingHours:
    """
    Enhanced trading hours management with intelligent scheduling
    """
    
    def __init__(self, config_file="config/enhanced_trading_hours.yaml"):
        """
        Initialize enhanced trading hours manager
        
        Args:
            config_file (str): Path to trading hours configuration file
        """
        self.config_file = config_file
        self.trading_sessions = {}
        self.rest_periods = {}
        self.symbol_specific_hours = {}
        self.session_stats = {}
        self.last_trade_time = None
        self.consecutive_losses = 0
        self.forced_rest_until = None
        
        # Load configuration
        self.load_configuration()
        
        # Default timezone
        self.default_timezone = pytz.UTC
    
    def load_configuration(self):
        """Load trading hours configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = yaml.safe_load(f)
                
                # Load trading sessions
                for session_name, session_config in config.get('trading_sessions', {}).items():
                    self.trading_sessions[session_name] = TradingSession(
                        name=session_name,
                        start_hour=session_config['start_hour'],
                        start_minute=session_config.get('start_minute', 0),
                        end_hour=session_config['end_hour'],
                        end_minute=session_config.get('end_minute', 0),
                        timezone=session_config.get('timezone', 'UTC'),
                        active=session_config.get('active', True),
                        max_trades=session_config.get('max_trades'),
                        rest_after_session=session_config.get('rest_after_session', 0)
                    )
                
                # Load rest periods
                for rest_name, rest_config in config.get('rest_periods', {}).items():
                    self.rest_periods[rest_name] = RestPeriod(
                        name=rest_name,
                        start_hour=rest_config['start_hour'],
                        start_minute=rest_config.get('start_minute', 0),
                        end_hour=rest_config['end_hour'],
                        end_minute=rest_config.get('end_minute', 0),
                        timezone=rest_config.get('timezone', 'UTC'),
                        reason=rest_config.get('reason', 'Scheduled rest'),
                        active=rest_config.get('active', True)
                    )
                
                # Load symbol-specific hours
                self.symbol_specific_hours = config.get('symbol_specific_hours', {})
                
                logger.info(f"Trading hours configuration loaded from {self.config_file}")
            else:
                # Create default configuration
                self.create_default_configuration()
                
        except Exception as e:
            logger.error(f"Error loading trading hours configuration: {e}")
            self.create_default_configuration()
    
    def create_default_configuration(self):
        """Create default trading hours configuration"""
        # Default trading sessions
        self.trading_sessions = {
            'asian_session': TradingSession(
                name='asian_session',
                start_hour=0,
                start_minute=0,
                end_hour=8,
                end_minute=0,
                timezone='UTC',
                active=True,
                max_trades=5,
                rest_after_session=120  # 2 hours rest
            ),
            'european_session': TradingSession(
                name='european_session',
                start_hour=8,
                start_minute=0,
                end_hour=16,
                end_minute=0,
                timezone='UTC',
                active=True,
                max_trades=10,
                rest_after_session=120
            ),
            'american_session': TradingSession(
                name='american_session',
                start_hour=16,
                start_minute=0,
                end_hour=24,
                end_minute=0,
                timezone='UTC',
                active=True,
                max_trades=8,
                rest_after_session=120
            )
        }
        
        # Default rest periods
        self.rest_periods = {
            'low_liquidity_1': RestPeriod(
                name='low_liquidity_1',
                start_hour=2,
                start_minute=0,
                end_hour=5,
                end_minute=0,
                timezone='UTC',
                reason='Low liquidity period',
                active=True
            ),
            'weekend_rest': RestPeriod(
                name='weekend_rest',
                start_hour=22,
                start_minute=0,
                end_hour=23,
                end_minute=59,
                timezone='UTC',
                reason='Weekend preparation',
                active=True
            )
        }
        
        # Symbol-specific hours
        self.symbol_specific_hours = {
            'BTC/USDT': {
                'preferred_hours': [9, 10, 11, 13, 14, 15, 16, 17],
                'avoid_hours': [2, 3, 4, 5],
                'max_trades_per_hour': 2
            },
            'ETH/USDT': {
                'preferred_hours': [8, 9, 10, 14, 15, 16],
                'avoid_hours': [2, 3, 4, 5, 22, 23],
                'max_trades_per_hour': 3
            }
        }
        
        # Save default configuration
        self.save_configuration()
    
    def save_configuration(self):
        """Save current configuration to file"""
        try:
            config = {
                'trading_sessions': {
                    name: {
                        'start_hour': session.start_hour,
                        'start_minute': session.start_minute,
                        'end_hour': session.end_hour,
                        'end_minute': session.end_minute,
                        'timezone': session.timezone,
                        'active': session.active,
                        'max_trades': session.max_trades,
                        'rest_after_session': session.rest_after_session
                    }
                    for name, session in self.trading_sessions.items()
                },
                'rest_periods': {
                    name: {
                        'start_hour': period.start_hour,
                        'start_minute': period.start_minute,
                        'end_hour': period.end_hour,
                        'end_minute': period.end_minute,
                        'timezone': period.timezone,
                        'reason': period.reason,
                        'active': period.active
                    }
                    for name, period in self.rest_periods.items()
                },
                'symbol_specific_hours': self.symbol_specific_hours
            }
            
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                yaml.dump(config, f, indent=2)
            
            logger.info(f"Trading hours configuration saved to {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error saving trading hours configuration: {e}")
    
    def is_trading_allowed(self, symbol=None, current_time=None):
        """
        Check if trading is allowed at the current time
        
        Args:
            symbol (str): Trading symbol to check
            current_time (datetime): Current time (UTC if None)
        
        Returns:
            dict: Trading status and details
        """
        try:
            if current_time is None:
                current_time = datetime.datetime.now(pytz.UTC)
            
            # Ensure current_time is timezone-aware
            if current_time.tzinfo is None:
                current_time = pytz.UTC.localize(current_time)
            
            result = {
                'allowed': False,
                'reason': 'Unknown',
                'next_trading_time': None,
                'current_session': None,
                'time_until_next_session': None
            }
            
            # Check forced rest period
            if self.forced_rest_until and current_time < self.forced_rest_until:
                result['reason'] = f'Forced rest until {self.forced_rest_until}'
                result['next_trading_time'] = self.forced_rest_until
                return result
            
            # Check if in rest period
            rest_check = self._check_rest_periods(current_time)
            if rest_check['in_rest']:
                result['reason'] = rest_check['reason']
                result['next_trading_time'] = rest_check['end_time']
                return result
            
            # Check trading sessions
            session_check = self._check_trading_sessions(current_time)
            if not session_check['in_session']:
                result['reason'] = 'Outside trading hours'
                result['next_trading_time'] = session_check['next_session_start']
                result['time_until_next_session'] = session_check['time_until_next']
                return result
            
            # Check symbol-specific rules
            if symbol:
                symbol_check = self._check_symbol_specific_rules(symbol, current_time)
                if not symbol_check['allowed']:
                    result['reason'] = symbol_check['reason']
                    result['next_trading_time'] = symbol_check['next_allowed_time']
                    return result
            
            # Check session trade limits
            current_session = session_check['current_session']
            if current_session and current_session.max_trades:
                session_trades = self._get_session_trade_count(current_session.name, current_time)
                if session_trades >= current_session.max_trades:
                    result['reason'] = f'Session trade limit reached ({session_trades}/{current_session.max_trades})'
                    result['next_trading_time'] = session_check['next_session_start']
                    return result
            
            # All checks passed
            result['allowed'] = True
            result['reason'] = 'Trading allowed'
            result['current_session'] = current_session.name if current_session else None
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking trading hours: {e}")
            return {
                'allowed': False,
                'reason': f'Error: {str(e)}',
                'next_trading_time': None,
                'current_session': None
            }
    
    def _check_rest_periods(self, current_time):
        """Check if current time is in a rest period"""
        for period in self.rest_periods.values():
            if not period.active:
                continue
            
            # Convert to period timezone
            period_tz = pytz.timezone(period.timezone)
            local_time = current_time.astimezone(period_tz)
            
            # Check if in rest period
            start_time = local_time.replace(hour=period.start_hour, minute=period.start_minute, second=0, microsecond=0)
            end_time = local_time.replace(hour=period.end_hour, minute=period.end_minute, second=0, microsecond=0)
            
            # Handle overnight periods
            if end_time <= start_time:
                end_time += datetime.timedelta(days=1)
            
            if start_time <= local_time <= end_time:
                return {
                    'in_rest': True,
                    'reason': f'{period.reason} ({period.name})',
                    'end_time': end_time.astimezone(pytz.UTC)
                }
        
        return {'in_rest': False}
    
    def _check_trading_sessions(self, current_time):
        """Check current trading session"""
        current_session = None
        next_session_start = None
        min_time_until_next = None
        
        for session in self.trading_sessions.values():
            if not session.active:
                continue
            
            # Convert to session timezone
            session_tz = pytz.timezone(session.timezone)
            local_time = current_time.astimezone(session_tz)
            
            # Check if in session
            start_time = local_time.replace(hour=session.start_hour, minute=session.start_minute, second=0, microsecond=0)
            end_time = local_time.replace(hour=session.end_hour, minute=session.end_minute, second=0, microsecond=0)
            
            # Handle overnight sessions
            if end_time <= start_time:
                end_time += datetime.timedelta(days=1)
            
            if start_time <= local_time <= end_time:
                current_session = session
                break
            
            # Calculate next session start
            if local_time < start_time:
                time_until = start_time - local_time
            else:
                # Next day
                next_start = start_time + datetime.timedelta(days=1)
                time_until = next_start - local_time
            
            if min_time_until_next is None or time_until < min_time_until_next:
                min_time_until_next = time_until
                next_session_start = (local_time + time_until).astimezone(pytz.UTC)
        
        return {
            'in_session': current_session is not None,
            'current_session': current_session,
            'next_session_start': next_session_start,
            'time_until_next': min_time_until_next
        }
    
    def _check_symbol_specific_rules(self, symbol, current_time):
        """Check symbol-specific trading rules"""
        if symbol not in self.symbol_specific_hours:
            return {'allowed': True}
        
        rules = self.symbol_specific_hours[symbol]
        current_hour = current_time.hour
        
        # Check preferred hours
        if 'preferred_hours' in rules and current_hour not in rules['preferred_hours']:
            # Find next preferred hour
            preferred_hours = sorted(rules['preferred_hours'])
            next_hour = None
            
            for hour in preferred_hours:
                if hour > current_hour:
                    next_hour = hour
                    break
            
            if next_hour is None:
                next_hour = preferred_hours[0] + 24  # Next day
            
            next_time = current_time.replace(hour=next_hour % 24, minute=0, second=0, microsecond=0)
            if next_hour >= 24:
                next_time += datetime.timedelta(days=1)
            
            return {
                'allowed': False,
                'reason': f'Outside preferred hours for {symbol}',
                'next_allowed_time': next_time
            }
        
        # Check avoid hours
        if 'avoid_hours' in rules and current_hour in rules['avoid_hours']:
            # Find next non-avoided hour
            next_hour = current_hour + 1
            while next_hour % 24 in rules['avoid_hours']:
                next_hour += 1
            
            next_time = current_time.replace(hour=next_hour % 24, minute=0, second=0, microsecond=0)
            if next_hour >= 24:
                next_time += datetime.timedelta(days=1)
            
            return {
                'allowed': False,
                'reason': f'Avoiding hour {current_hour} for {symbol}',
                'next_allowed_time': next_time
            }
        
        # Check hourly trade limits
        if 'max_trades_per_hour' in rules:
            hourly_trades = self._get_hourly_trade_count(symbol, current_time)
            if hourly_trades >= rules['max_trades_per_hour']:
                next_time = current_time.replace(minute=0, second=0, microsecond=0) + datetime.timedelta(hours=1)
                return {
                    'allowed': False,
                    'reason': f'Hourly trade limit reached for {symbol} ({hourly_trades}/{rules["max_trades_per_hour"]})',
                    'next_allowed_time': next_time
                }
        
        return {'allowed': True}
    
    def _get_session_trade_count(self, session_name, current_time):
        """Get trade count for current session"""
        # This would integrate with the trading system to get actual trade counts
        # For now, return 0 as placeholder
        return 0
    
    def _get_hourly_trade_count(self, symbol, current_time):
        """Get trade count for current hour"""
        # This would integrate with the trading system to get actual trade counts
        # For now, return 0 as placeholder
        return 0
    
    def record_trade(self, symbol, pnl, session_name=None, trade_time=None):
        """
        Record a trade for statistics and limits tracking
        
        Args:
            symbol (str): Trading symbol
            pnl (float): Profit/loss of the trade
            session_name (str): Trading session name
            trade_time (datetime): Time of the trade
        """
        try:
            if trade_time is None:
                trade_time = datetime.datetime.now(pytz.UTC)
            
            self.last_trade_time = trade_time
            
            # Update consecutive losses counter
            if pnl < 0:
                self.consecutive_losses += 1
            else:
                self.consecutive_losses = 0
            
            # Check if forced rest is needed (after 2 consecutive losses)
            if self.consecutive_losses >= 2:
                self.forced_rest_until = trade_time + datetime.timedelta(hours=1)
                logger.warning(f"Forced rest period activated until {self.forced_rest_until} after {self.consecutive_losses} consecutive losses")
            
            # Update session statistics
            if session_name and session_name in self.session_stats:
                stats = self.session_stats[session_name]
                stats.trades_count += 1
                stats.total_pnl += pnl
                stats.average_pnl = stats.total_pnl / stats.trades_count
                
                if pnl > 0:
                    stats.successful_trades += 1
            
            logger.info(f"Trade recorded: {symbol}, P&L: {pnl:.2f}, Session: {session_name}")
            
        except Exception as e:
            logger.error(f"Error recording trade: {e}")
    
    def get_optimal_trading_hours(self, symbol=None, lookback_days=30):
        """
        Get optimal trading hours based on historical performance
        
        Args:
            symbol (str): Trading symbol
            lookback_days (int): Days to look back for analysis
        
        Returns:
            dict: Optimal trading hours analysis
        """
        try:
            # This would analyze historical trade data to find optimal hours
            # For now, return default recommendations
            
            if symbol in self.symbol_specific_hours:
                return {
                    'symbol': symbol,
                    'preferred_hours': self.symbol_specific_hours[symbol].get('preferred_hours', []),
                    'avoid_hours': self.symbol_specific_hours[symbol].get('avoid_hours', []),
                    'recommendation': 'Use configured symbol-specific hours'
                }
            
            # Default recommendations based on general market patterns
            return {
                'symbol': symbol or 'DEFAULT',
                'preferred_hours': [9, 10, 11, 13, 14, 15, 16, 17],  # Active market hours
                'avoid_hours': [2, 3, 4, 5, 22, 23],  # Low liquidity hours
                'recommendation': 'Use default market hours pattern'
            }
            
        except Exception as e:
            logger.error(f"Error getting optimal trading hours: {e}")
            return {
                'symbol': symbol or 'DEFAULT',
                'preferred_hours': [],
                'avoid_hours': [],
                'recommendation': 'Error in analysis'
            }
    
    def get_next_trading_window(self, symbol=None, current_time=None):
        """
        Get the next available trading window
        
        Args:
            symbol (str): Trading symbol
            current_time (datetime): Current time
        
        Returns:
            dict: Next trading window information
        """
        try:
            if current_time is None:
                current_time = datetime.datetime.now(pytz.UTC)
            
            # Check current status
            status = self.is_trading_allowed(symbol, current_time)
            
            if status['allowed']:
                return {
                    'status': 'currently_allowed',
                    'current_session': status['current_session'],
                    'next_check_time': current_time + datetime.timedelta(minutes=5)
                }
            
            return {
                'status': 'waiting',
                'reason': status['reason'],
                'next_trading_time': status['next_trading_time'],
                'wait_duration': status['next_trading_time'] - current_time if status['next_trading_time'] else None
            }
            
        except Exception as e:
            logger.error(f"Error getting next trading window: {e}")
            return {
                'status': 'error',
                'reason': str(e)
            }
    
    def update_session_performance(self, session_name, performance_data):
        """Update session performance statistics"""
        try:
            if session_name not in self.session_stats:
                self.session_stats[session_name] = TradingStats(
                    session_name=session_name,
                    trades_count=0,
                    successful_trades=0,
                    total_pnl=0.0,
                    average_pnl=0.0,
                    best_hour=None,
                    worst_hour=None
                )
            
            stats = self.session_stats[session_name]
            
            # Update with new performance data
            for key, value in performance_data.items():
                if hasattr(stats, key):
                    setattr(stats, key, value)
            
            logger.info(f"Updated performance for session {session_name}")
            
        except Exception as e:
            logger.error(f"Error updating session performance: {e}")
    
    def get_trading_summary(self):
        """Get summary of trading hours and performance"""
        try:
            summary = {
                'active_sessions': len([s for s in self.trading_sessions.values() if s.active]),
                'active_rest_periods': len([r for r in self.rest_periods.values() if r.active]),
                'symbol_specific_rules': len(self.symbol_specific_hours),
                'consecutive_losses': self.consecutive_losses,
                'forced_rest_until': self.forced_rest_until.isoformat() if self.forced_rest_until else None,
                'last_trade_time': self.last_trade_time.isoformat() if self.last_trade_time else None,
                'session_stats': {
                    name: {
                        'trades_count': stats.trades_count,
                        'successful_trades': stats.successful_trades,
                        'win_rate': stats.successful_trades / stats.trades_count if stats.trades_count > 0 else 0,
                        'total_pnl': stats.total_pnl,
                        'average_pnl': stats.average_pnl
                    }
                    for name, stats in self.session_stats.items()
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting trading summary: {e}")
            return {}
