"""
Technical indicators module for the trading bot
"""

import numpy as np
import pandas as pd
import logging
import os
import math
from typing import List, Tuple, Dict, Optional, Union
import datetime
import time
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import joblib

# Set up logging
logger = logging.getLogger("indicators")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/indicators.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

# Define currency-specific ATR multipliers
CURRENCY_ATR_MULTIPLIERS = {
    "BTC": 1.0,    # Base multiplier for BTC
    "ETH": 1.5,    # ETH is 1.5x more volatile than BTC
    "BNB": 1.8,    # BNB is 1.8x more volatile than BTC
    "SOL": 3.0,    # SOL is 3x more volatile than BTC
    "XRP": 2.5,    # XRP is 2.5x more volatile than BTC
    "ADA": 2.2,    # ADA is 2.2x more volatile than BTC
    "DOT": 2.5,    # DOT is 2.5x more volatile than BTC
    "DOGE": 3.5,   # DOGE is 3.5x more volatile than BTC
    "AVAX": 2.8,   # AVAX is 2.8x more volatile than BTC
    "MATIC": 2.5,  # MATIC is 2.5x more volatile than BTC
    "LINK": 2.2,   # LINK is 2.2x more volatile than BTC
    "UNI": 2.3,    # UNI is 2.3x more volatile than BTC
    "ATOM": 2.4,   # ATOM is 2.4x more volatile than BTC
    "LTC": 1.8,    # LTC is 1.8x more volatile than BTC
    "DEFAULT": 2.0 # Default multiplier for other currencies
}

# Define Fibonacci levels
FIBONACCI_LEVELS = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.618, 2.618]

def calculate_ema(prices, period):
    """
    Calculate Exponential Moving Average

    Args:
        prices (list): List of prices
        period (int): EMA period

    Returns:
        list: EMA values
    """
    try:
        prices_series = pd.Series(prices)
        ema = prices_series.ewm(span=period, adjust=False).mean().tolist()
        return ema
    except Exception as e:
        logger.error(f"Error calculating EMA: {e}")
        return []

def calculate_ema_slope(ema_values, lookback=5):
    """
    Calculate the slope of EMA

    Args:
        ema_values (list): List of EMA values
        lookback (int): Number of periods to look back

    Returns:
        float: Slope of EMA
    """
    try:
        if len(ema_values) < lookback + 1:
            return 0

        # Calculate the slope using linear regression
        y = ema_values[-lookback:]
        x = list(range(lookback))

        # Calculate slope using numpy's polyfit
        slope, _ = np.polyfit(x, y, 1)

        return slope
    except Exception as e:
        logger.error(f"Error calculating EMA slope: {e}")
        return 0

def is_ema_flat(ema_values, lookback=5, threshold=0.05):
    """
    Check if EMA is flat (horizontal)

    Args:
        ema_values (list): List of EMA values
        lookback (int): Number of periods to look back
        threshold (float): Threshold for slope to be considered flat

    Returns:
        bool: True if EMA is flat, False otherwise
    """
    try:
        slope = calculate_ema_slope(ema_values, lookback)
        return abs(slope) < threshold
    except Exception as e:
        logger.error(f"Error checking if EMA is flat: {e}")
        return True  # Conservative approach: assume flat if error

def calculate_rsi(prices, period=14):
    """
    Calculate Relative Strength Index

    Args:
        prices (list): List of prices
        period (int): RSI period

    Returns:
        list: RSI values
    """
    try:
        prices_series = pd.Series(prices)
        delta = prices_series.diff()

        # Make two series: one for gains and one for losses
        gain = delta.clip(lower=0)
        loss = -delta.clip(upper=0)

        # Calculate average gain and loss
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()

        # Calculate RS
        rs = avg_gain / avg_loss

        # Calculate RSI
        rsi = 100 - (100 / (1 + rs))

        return rsi.tolist()
    except Exception as e:
        logger.error(f"Error calculating RSI: {e}")
        return []

def calculate_macd(prices, fast_period=12, slow_period=26, signal_period=9):
    """
    Calculate Moving Average Convergence Divergence

    Args:
        prices (list): List of prices
        fast_period (int): Fast EMA period
        slow_period (int): Slow EMA period
        signal_period (int): Signal EMA period

    Returns:
        tuple: (MACD line, Signal line, Histogram)
    """
    try:
        prices_series = pd.Series(prices)

        # Calculate fast and slow EMAs
        ema_fast = prices_series.ewm(span=fast_period, adjust=False).mean()
        ema_slow = prices_series.ewm(span=slow_period, adjust=False).mean()

        # Calculate MACD line
        macd_line = ema_fast - ema_slow

        # Calculate signal line
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # Calculate histogram
        histogram = macd_line - signal_line

        return macd_line.tolist(), signal_line.tolist(), histogram.tolist()
    except Exception as e:
        logger.error(f"Error calculating MACD: {e}")
        return [], [], []

def calculate_atr(high_prices, low_prices, close_prices, period=14):
    """
    Calculate Average True Range

    Args:
        high_prices (list): List of high prices
        low_prices (list): List of low prices
        close_prices (list): List of close prices
        period (int): ATR period

    Returns:
        list: ATR values
    """
    try:
        if len(high_prices) != len(low_prices) or len(high_prices) != len(close_prices):
            logger.error("High, low, and close price lists must be of the same length")
            return []

        # Create DataFrame
        df = pd.DataFrame({
            'high': high_prices,
            'low': low_prices,
            'close': close_prices
        })

        # Calculate true range
        df['previous_close'] = df['close'].shift(1)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['previous_close'])
        df['tr3'] = abs(df['low'] - df['previous_close'])
        df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

        # Calculate ATR
        df['atr'] = df['tr'].rolling(window=period).mean()

        return df['atr'].tolist()
    except Exception as e:
        logger.error(f"Error calculating ATR: {e}")
        return []

def calculate_bollinger_bands(prices, period=20, std_dev=2.0, use_ema=False):
    """
    Calculate Bollinger Bands with enhanced options

    Args:
        prices (list): List of prices
        period (int): Period for moving average
        std_dev (float): Number of standard deviations
        use_ema (bool): Use EMA instead of SMA for middle band calculation

    Returns:
        tuple: (upper_band, middle_band, lower_band)
    """
    try:
        if len(prices) < period:
            logger.warning(f"Not enough data for Bollinger Bands calculation. Need at least {period} data points.")
            return [], [], []

        # Convert to pandas Series
        prices_series = pd.Series(prices)

        # Calculate middle band (SMA or EMA)
        if use_ema:
            middle_band = prices_series.ewm(span=period, adjust=False).mean()
            logger.debug(f"Using EMA for Bollinger Bands middle band calculation")
        else:
            middle_band = prices_series.rolling(window=period).mean()

        # Calculate standard deviation
        std = prices_series.rolling(window=period).std()

        # Calculate upper and lower bands
        upper_band = middle_band + (std_dev * std)
        lower_band = middle_band - (std_dev * std)

        # Calculate bandwidth (volatility indicator)
        bandwidth = (upper_band - lower_band) / middle_band * 100

        logger.debug(f"Calculated Bollinger Bands with period={period}, std_dev={std_dev}, use_ema={use_ema}")

        return upper_band.tolist(), middle_band.tolist(), lower_band.tolist(), bandwidth.tolist()
    except Exception as e:
        logger.error(f"Error calculating Bollinger Bands: {e}")
        return [], [], [], []

def is_bollinger_breakout(prices, upper_band, lower_band, lookback=3, volume=None, volume_threshold=1.5):
    """
    Check if price is breaking out of Bollinger Bands with enhanced volume confirmation

    Args:
        prices (list): List of prices
        upper_band (list): Upper Bollinger Band
        lower_band (list): Lower Bollinger Band
        lookback (int): Number of periods to look back
        volume (list, optional): List of volume data for confirmation
        volume_threshold (float): Volume threshold multiplier for confirmation

    Returns:
        tuple: (is_upper_breakout, is_lower_breakout, breakout_strength)
    """
    try:
        if len(prices) < lookback or len(upper_band) < lookback or len(lower_band) < lookback:
            return False, False, 0

        # Check for upper band breakout
        upper_breakout = prices[-1] > upper_band[-1] and all(prices[-i-1] <= upper_band[-i-1] for i in range(1, min(lookback, len(prices)-1)))

        # Check for lower band breakout
        lower_breakout = prices[-1] < lower_band[-1] and all(prices[-i-1] >= lower_band[-i-1] for i in range(1, min(lookback, len(prices)-1)))

        # Calculate breakout strength (percentage beyond the band)
        breakout_strength = 0
        if upper_breakout:
            breakout_strength = (prices[-1] - upper_band[-1]) / upper_band[-1] * 100
        elif lower_breakout:
            breakout_strength = (lower_band[-1] - prices[-1]) / lower_band[-1] * 100

        # Check volume confirmation if volume data is provided
        volume_confirmed = True
        if volume and len(volume) >= lookback:
            # Calculate average volume
            avg_volume = sum(volume[-lookback-1:-1]) / lookback
            current_volume = volume[-1]

            # Check if current volume is above threshold
            volume_confirmed = current_volume > avg_volume * volume_threshold

            if (upper_breakout or lower_breakout) and volume_confirmed:
                logger.info(f"Bollinger Band breakout confirmed with high volume: {current_volume:.2f} vs avg {avg_volume:.2f}")
            elif (upper_breakout or lower_breakout) and not volume_confirmed:
                logger.info(f"Bollinger Band breakout with low volume - potential false breakout")
                # Reduce breakout strength for low volume breakouts
                breakout_strength = breakout_strength * 0.5

        if upper_breakout:
            logger.info(f"Detected upper Bollinger Band breakout: price={prices[-1]:.2f}, upper_band={upper_band[-1]:.2f}, strength={breakout_strength:.2f}%")

        if lower_breakout:
            logger.info(f"Detected lower Bollinger Band breakout: price={prices[-1]:.2f}, lower_band={lower_band[-1]:.2f}, strength={breakout_strength:.2f}%")

        return upper_breakout and volume_confirmed, lower_breakout and volume_confirmed, breakout_strength
    except Exception as e:
        logger.error(f"Error checking Bollinger Band breakout: {e}")
        return False, False, 0

def is_bollinger_squeeze(upper_band, lower_band, lookback=20, threshold=0.3):
    """
    Check if Bollinger Bands are squeezing (narrowing)

    A squeeze indicates low volatility and potential for a breakout.

    Args:
        upper_band (list): Upper Bollinger Band
        lower_band (list): Lower Bollinger Band
        lookback (int): Number of periods to look back
        threshold (float): Threshold for band width reduction

    Returns:
        bool: True if bands are squeezing, False otherwise
    """
    try:
        if len(upper_band) < lookback + 1 or len(lower_band) < lookback + 1:
            return False

        # Calculate current band width
        current_width = upper_band[-1] - lower_band[-1]

        # Calculate average band width over lookback period
        past_widths = [upper_band[-i] - lower_band[-i] for i in range(2, lookback + 2)]
        avg_past_width = sum(past_widths) / len(past_widths)

        # Check if current width is significantly narrower than average
        is_squeeze = current_width < avg_past_width * (1 - threshold)

        if is_squeeze:
            width_reduction = (avg_past_width - current_width) / avg_past_width * 100
            logger.info(f"Detected Bollinger Band squeeze: width reduced by {width_reduction:.2f}%")

        return is_squeeze
    except Exception as e:
        logger.error(f"Error checking Bollinger Band squeeze: {e}")
        return False

def calculate_roc(prices, period=1):
    """
    Calculate Rate of Change

    Args:
        prices (list): List of prices
        period (int): ROC period

    Returns:
        list: ROC values in percentage
    """
    try:
        prices_series = pd.Series(prices)
        roc = prices_series.pct_change(periods=period) * 100
        return roc.tolist()
    except Exception as e:
        logger.error(f"Error calculating ROC: {e}")
        return []

def is_bullish_macd_crossover(macd_line, signal_line, histogram):
    """
    Check if there is a bullish MACD crossover

    Args:
        macd_line (list): MACD line values
        signal_line (list): Signal line values
        histogram (list): Histogram values

    Returns:
        bool: True if bullish crossover, False otherwise
    """
    try:
        if len(macd_line) < 2 or len(signal_line) < 2:
            return False

        # Check if MACD line crosses above signal line
        crossover = macd_line[-2] < signal_line[-2] and macd_line[-1] > signal_line[-1]

        # Check if histogram is positive
        positive_histogram = histogram[-1] > 0

        return crossover and positive_histogram
    except Exception as e:
        logger.error(f"Error checking bullish MACD crossover: {e}")
        return False

def is_bearish_macd_crossover(macd_line, signal_line, histogram):
    """
    Check if there is a bearish MACD crossover

    Args:
        macd_line (list): MACD line values
        signal_line (list): Signal line values
        histogram (list): Histogram values

    Returns:
        bool: True if bearish crossover, False otherwise
    """
    try:
        if len(macd_line) < 2 or len(signal_line) < 2:
            return False

        # Check if MACD line crosses below signal line
        crossover = macd_line[-2] > signal_line[-2] and macd_line[-1] < signal_line[-1]

        # Check if histogram is negative
        negative_histogram = histogram[-1] < 0

        return crossover and negative_histogram
    except Exception as e:
        logger.error(f"Error checking bearish MACD crossover: {e}")
        return False

def is_rsi_breaking_up(rsi_values, threshold_low=30, threshold_high=40):
    """
    Check if RSI is breaking upward from oversold territory

    Args:
        rsi_values (list): RSI values
        threshold_low (int): Lower threshold
        threshold_high (int): Upper threshold

    Returns:
        bool: True if RSI is breaking upward, False otherwise
    """
    try:
        if len(rsi_values) < 3:
            return False

        # Check if RSI was below lower threshold and is now between thresholds and rising
        below_threshold = rsi_values[-3] < threshold_low
        between_thresholds = threshold_low <= rsi_values[-1] <= threshold_high
        rising = rsi_values[-1] > rsi_values[-2]

        return below_threshold and between_thresholds and rising
    except Exception as e:
        logger.error(f"Error checking RSI breaking up: {e}")
        return False

def is_rsi_breaking_down(rsi_values, threshold_low=60, threshold_high=70):
    """
    Check if RSI is breaking downward from overbought territory

    Args:
        rsi_values (list): RSI values
        threshold_low (int): Lower threshold
        threshold_high (int): Upper threshold

    Returns:
        bool: True if RSI is breaking downward, False otherwise
    """
    try:
        if len(rsi_values) < 3:
            return False

        # Check if RSI was above upper threshold and is now between thresholds and falling
        above_threshold = rsi_values[-3] > threshold_high
        between_thresholds = threshold_low <= rsi_values[-1] <= threshold_high
        falling = rsi_values[-1] < rsi_values[-2]

        return above_threshold and between_thresholds and falling
    except Exception as e:
        logger.error(f"Error checking RSI breaking down: {e}")
        return False

def is_volume_above_average(volumes, lookback=20, threshold=1.1):
    """
    Check if current volume is above average

    Args:
        volumes (list): List of volumes
        lookback (int): Number of periods to look back
        threshold (float): Threshold multiplier

    Returns:
        bool: True if volume is above average, False otherwise
    """
    try:
        if len(volumes) < lookback + 1:
            return False

        # Calculate average volume
        avg_volume = sum(volumes[-lookback-1:-1]) / lookback

        # Check if current volume is above average
        return volumes[-1] > avg_volume * threshold
    except Exception as e:
        logger.error(f"Error checking volume above average: {e}")
        return False

def is_false_candle(open_prices, high_prices, low_prices, close_prices, threshold=5.0, reversal_threshold=2.5, timeframe_minutes=1):
    """
    Check if the current candle is a false candle (extreme movement with immediate reversal)

    Specifically detects extreme fast candles (>5% movement within a minute with immediate reversal)

    Args:
        open_prices (list): List of open prices
        high_prices (list): List of high prices
        low_prices (list): List of low prices
        close_prices (list): List of close prices
        threshold (float): Threshold for extreme movement in percentage (default: 3.0%)
        reversal_threshold (float): Threshold for reversal in percentage (default: 1.5%)
        timeframe_minutes (int): Timeframe in minutes (default: 1 minute)

    Returns:
        bool: True if false candle, False otherwise
    """
    try:
        if len(open_prices) < 2 or len(high_prices) < 2 or len(low_prices) < 2 or len(close_prices) < 2:
            return False

        # Calculate current candle range in percentage
        current_range_pct = (high_prices[-1] - low_prices[-1]) / low_prices[-1] * 100

        # Check if current candle has extreme movement (>3%)
        extreme_movement = current_range_pct > threshold

        # Calculate reversal
        prev_close = close_prices[-2]
        current_open = open_prices[-1]
        current_close = close_prices[-1]

        # Calculate price change from previous close to current close
        price_change_pct = (current_close - prev_close) / prev_close * 100

        # Check for immediate reversal
        # For bullish candles, we expect a negative reversal
        # For bearish candles, we expect a positive reversal
        if current_close > current_open:  # Bullish candle
            reversal = price_change_pct < -reversal_threshold
        else:  # Bearish candle
            reversal = price_change_pct > reversal_threshold

        # Only consider as false candle if timeframe is 1 minute (for fast movements)
        is_fast_movement = timeframe_minutes <= 1

        if extreme_movement and reversal and is_fast_movement:
            logger.warning(f"False candle detected: {current_range_pct:.2f}% movement with {price_change_pct:.2f}% reversal")
            return True

        return False
    except Exception as e:
        logger.error(f"Error checking false candle: {e}")
        return False

def calculate_trade_quality_score(conditions):
    """
    Calculate trade quality score based on conditions met

    Each trade is rated from 0 to 100 based on the number of conditions met.
    Entry only if the score is ≥ 60% (reduced from 70% to allow more trades in sideways markets).

    Args:
        conditions (dict): Dictionary of conditions and their status (True/False)

    Returns:
        float: Trade quality score (0-100)
    """
    try:
        # Define weights for each condition
        condition_weights = {
            # Trend conditions (35% total weight)
            'ema50_gt_ema200': 20,
            'ema50_lt_ema200': 20,
            'ema_sloping_up': 15,
            'ema_sloping_down': 15,

            # Oscillator conditions (25% total weight)
            'rsi_30_40_breaking_up': 15,
            'rsi_60_70_breaking_down': 15,
            'bullish_macd_crossover': 15,
            'bearish_macd_crossover': 15,

            # Volume and volatility conditions (15% total weight)
            'volume_above_average': 10,
            'atr_not_high': 5,
            'not_low_liquidity_time': 5,

            # New Bollinger Band conditions (15% total weight)
            'bollinger_buy_signal': 15,
            'bollinger_sell_signal': 15,

            # New Fibonacci conditions (10% total weight)
            'fibonacci_support': 10,
            'fibonacci_resistance': 10
        }

        # Calculate weighted score
        weighted_score = 0
        total_possible_weight = 0
        met_conditions = []

        for condition, is_met in conditions.items():
            # Get weight for this condition (default to 10 if not specified)
            weight = condition_weights.get(condition, 10)

            # Add to total possible weight
            total_possible_weight += weight

            # If condition is met, add its weight to the score
            if is_met:
                weighted_score += weight
                met_conditions.append(condition)

        # Calculate final score (0-100)
        if total_possible_weight > 0:
            final_score = (weighted_score / total_possible_weight) * 100
        else:
            final_score = 0

        # Log the score and conditions
        logger.info(f"Trade quality score: {final_score:.2f}/100 (weighted: {weighted_score}/{total_possible_weight})")

        # Log which conditions were met
        if met_conditions:
            logger.info(f"Conditions met: {', '.join(met_conditions)}")
        else:
            logger.info("No conditions met")

        return final_score
    except Exception as e:
        logger.error(f"Error calculating trade quality score: {e}")
        return 0

def is_low_liquidity_time(market_timezone=None):
    """
    Check if current time is in low liquidity period (2:00 AM - 5:00 AM market time)

    No trades should be opened during this time period.

    Args:
        market_timezone (str, optional): Timezone of the market. If None, uses local time.

    Returns:
        bool: True if low liquidity time, False otherwise
    """
    try:
        import datetime
        import pytz

        # Get current time
        current_time = datetime.datetime.now()

        # Convert to market timezone if provided
        if market_timezone:
            try:
                local_tz = pytz.timezone('UTC')  # Assume UTC as default
                market_tz = pytz.timezone(market_timezone)
                current_time = local_tz.localize(current_time).astimezone(market_tz)
            except Exception as e:
                logger.error(f"Error converting timezone: {e}")
                # Continue with local time if timezone conversion fails

        # Check if time is between 2:00 AM and 5:00 AM
        current_hour = current_time.hour
        is_low_liquidity = 2 <= current_hour < 5

        if is_low_liquidity:
            logger.info(f"Low liquidity time detected: {current_time.strftime('%H:%M:%S')} (market time)")

        return is_low_liquidity
    except Exception as e:
        logger.error(f"Error checking low liquidity time: {e}")
        return False  # Assume not low liquidity time if error

def is_atr_high(atr_values, lookback=20, threshold=1.5):
    """
    Check if ATR is high compared to recent values

    Args:
        atr_values (list): List of ATR values
        lookback (int): Number of periods to look back
        threshold (float): Threshold multiplier

    Returns:
        bool: True if ATR is high, False otherwise
    """
    try:
        if len(atr_values) < lookback + 1:
            return False

        # Calculate average ATR
        avg_atr = sum(atr_values[-lookback-1:-1]) / lookback

        # Check if current ATR is high
        return atr_values[-1] > avg_atr * threshold
    except Exception as e:
        logger.error(f"Error checking if ATR is high: {e}")
        return True  # Conservative approach: assume high volatility if error

def is_market_volatile(atr_values, lookback=5, threshold=1.5, currency=None):
    """
    Check if the market is volatile based on ATR, with currency-specific adjustments

    Args:
        atr_values (list): List of ATR values
        lookback (int): Number of periods to look back
        threshold (float): Threshold multiplier for volatility
        currency (str, optional): Currency symbol (e.g., "BTC", "ETH")

    Returns:
        bool: True if market is volatile, False otherwise
    """
    try:
        # Adjust threshold based on currency
        if currency:
            # Extract base currency from trading pair (e.g., "BTC/USDT" -> "BTC")
            base_currency = currency.split('/')[0] if '/' in currency else currency

            # Get currency-specific multiplier
            multiplier = CURRENCY_ATR_MULTIPLIERS.get(base_currency, CURRENCY_ATR_MULTIPLIERS["DEFAULT"])

            # Adjust threshold based on currency volatility
            adjusted_threshold = threshold / multiplier

            logger.info(f"Using currency-specific ATR threshold for {base_currency}: {adjusted_threshold:.2f} (base: {threshold}, multiplier: {multiplier})")

            # Use adjusted threshold
            return is_atr_high(atr_values, lookback, adjusted_threshold)
        else:
            # Use default threshold
            return is_atr_high(atr_values, lookback, threshold)
    except Exception as e:
        logger.error(f"Error checking market volatility: {e}")
        return True  # Conservative approach: assume high volatility if error

def is_atr_rising_rapidly(atr_values, lookback=5, threshold=1.5):
    """
    Check if ATR is rising rapidly

    Args:
        atr_values (list): List of ATR values
        lookback (int): Number of periods to look back
        threshold (float): Threshold multiplier

    Returns:
        bool: True if ATR is rising rapidly, False otherwise
    """
    try:
        if len(atr_values) < lookback + 1:
            return False

        # Calculate percentage change in ATR
        atr_change = (atr_values[-1] / atr_values[-lookback]) - 1

        # Check if ATR is rising rapidly
        return atr_change > threshold
    except Exception as e:
        logger.error(f"Error checking if ATR is rising rapidly: {e}")
        return True  # Conservative approach: assume high volatility if error

def check_trend_conditions(close_prices):
    """
    Check trend conditions based on EMA50 and EMA200

    Args:
        close_prices (list): List of close prices

    Returns:
        dict: Dictionary with trend conditions
    """
    try:
        if len(close_prices) < 200:
            logger.warning("Not enough data for trend analysis")
            return {
                "ema50": [],
                "ema200": [],
                "ema50_slope": 0,
                "is_uptrend": False,
                "is_downtrend": False,
                "is_flat": True,
                "trend_signal": "hold"
            }

        # Calculate EMA50 and EMA200
        ema50 = calculate_ema(close_prices, 50)
        ema200 = calculate_ema(close_prices, 200)

        # Calculate EMA50 slope
        ema50_slope = calculate_ema_slope(ema50)

        # Check if EMA50 is flat
        is_flat = is_ema_flat(ema50)

        # Check trend conditions
        is_uptrend = ema50[-1] > ema200[-1] and ema50_slope > 0
        is_downtrend = ema50[-1] < ema200[-1] and ema50_slope < 0

        # Determine trend signal
        if is_flat:
            trend_signal = "hold"
        elif is_uptrend:
            trend_signal = "buy"
        elif is_downtrend:
            trend_signal = "sell"
        else:
            trend_signal = "hold"

        return {
            "ema50": ema50,
            "ema200": ema200,
            "ema50_slope": ema50_slope,
            "is_uptrend": is_uptrend,
            "is_downtrend": is_downtrend,
            "is_flat": is_flat,
            "trend_signal": trend_signal
        }
    except Exception as e:
        logger.error(f"Error checking trend conditions: {e}")
        return {
            "ema50": [],
            "ema200": [],
            "ema50_slope": 0,
            "is_uptrend": False,
            "is_downtrend": False,
            "is_flat": True,
            "trend_signal": "hold"
        }

def find_recent_low(low_prices, lookback=20):
    """
    Find the recent low price

    Args:
        low_prices (list): List of low prices
        lookback (int): Number of periods to look back

    Returns:
        float: Recent low price
    """
    try:
        if len(low_prices) < lookback:
            return low_prices[-1]

        return min(low_prices[-lookback:])
    except Exception as e:
        logger.error(f"Error finding recent low: {e}")
        return low_prices[-1]  # Return current low as fallback

def find_recent_high(high_prices, lookback=20):
    """
    Find the recent high price

    Args:
        high_prices (list): List of high prices
        lookback (int): Number of periods to look back

    Returns:
        float: Recent high price
    """
    try:
        if len(high_prices) < lookback:
            return high_prices[-1]

        return max(high_prices[-lookback:])
    except Exception as e:
        logger.error(f"Error finding recent high: {e}")
        return high_prices[-1]  # Return current high as fallback

def calculate_fibonacci_retracement(high, low, trend="auto"):
    """
    Calculate Fibonacci retracement levels with enhanced trend detection

    Args:
        high (float): High price
        low (float): Low price
        trend (str): Trend direction ('up', 'down', or 'auto' for automatic detection)

    Returns:
        dict: Fibonacci retracement levels with additional metadata
    """
    try:
        if high <= low and trend != "down":
            logger.warning(f"Invalid high/low values for Fibonacci calculation: high={high}, low={low}")
            return {}

        if high >= low and trend != "up":
            logger.warning(f"Invalid high/low values for Fibonacci calculation: high={high}, low={low}")
            return {}

        # Determine trend direction if set to auto
        if trend == "auto":
            trend = "up" if high > low else "down"

        diff = abs(high - low)
        levels = {}

        # Add metadata
        levels["metadata"] = {
            "high": high,
            "low": low,
            "trend": trend,
            "range": diff,
            "range_percent": (diff / (low if trend == "up" else high)) * 100
        }

        # Calculate retracement levels based on trend
        if trend == "up":
            # Uptrend: retracement levels are below the high
            for level in FIBONACCI_LEVELS:
                if level == 0:
                    levels[level] = high
                elif level == 1:
                    levels[level] = low
                else:
                    levels[level] = high - (diff * level)
        else:
            # Downtrend: retracement levels are above the low
            for level in FIBONACCI_LEVELS:
                if level == 0:
                    levels[level] = low
                elif level == 1:
                    levels[level] = high
                else:
                    levels[level] = low + (diff * level)

        logger.debug(f"Calculated Fibonacci retracement levels for {trend}trend from high={high} to low={low}")
        return levels
    except Exception as e:
        logger.error(f"Error calculating Fibonacci retracement: {e}")
        return {}

def calculate_fibonacci_extension(high, low, trend_high=None):
    """
    Calculate Fibonacci extension levels

    Args:
        high (float): High price
        low (float): Low price
        trend_high (float, optional): Trend high price for extensions

    Returns:
        dict: Fibonacci extension levels
    """
    try:
        if high <= low:
            logger.warning(f"Invalid high/low values for Fibonacci calculation: high={high}, low={low}")
            return {}

        diff = high - low
        levels = {}

        # If trend_high is not provided, use the high as the base for extensions
        base = trend_high if trend_high is not None else high

        for level in FIBONACCI_LEVELS:
            if level <= 1:
                continue  # Skip retracement levels

            # Calculate extension level
            levels[level] = base + (diff * (level - 1))

        logger.debug(f"Calculated Fibonacci extension levels from high={high}, low={low}, base={base}")
        return levels
    except Exception as e:
        logger.error(f"Error calculating Fibonacci extension: {e}")
        return {}

def identify_fibonacci_support_resistance(price, fib_levels, tolerance=0.005):
    """
    Identify if current price is near a Fibonacci support/resistance level

    Args:
        price (float): Current price
        fib_levels (dict): Fibonacci levels
        tolerance (float): Tolerance percentage for level proximity

    Returns:
        tuple: (is_at_level, level, type)
    """
    try:
        if not fib_levels:
            return False, None, None

        # Check each Fibonacci level
        for level, level_price in fib_levels.items():
            # Calculate percentage difference
            diff_pct = abs(price - level_price) / level_price

            # Check if price is within tolerance of the level
            if diff_pct <= tolerance:
                # Determine if it's support or resistance
                level_type = "support" if price > level_price else "resistance"

                logger.info(f"Price {price:.2f} is at Fibonacci {level} {level_type} level ({level_price:.2f})")
                return True, level, level_type

        return False, None, None
    except Exception as e:
        logger.error(f"Error identifying Fibonacci support/resistance: {e}")
        return False, None, None

def calculate_total_risk(positions, account_balance):
    """
    Calculate the total risk across all open positions

    Args:
        positions (list): List of position dictionaries
        account_balance (float): Total account balance

    Returns:
        float: Total risk as a percentage of account balance
    """
    try:
        if not positions or account_balance <= 0:
            return 0.0

        total_risk_amount = 0.0

        for position in positions:
            # Get position details
            entry_price = position.get("entry_price", 0)
            quantity = position.get("quantity", 0)
            stop_loss_pct = position.get("stop_loss_pct", 0.02)  # Default to 2%

            # Calculate position size
            position_size = entry_price * quantity

            # Calculate risk amount
            risk_amount = position_size * stop_loss_pct

            # Add to total risk
            total_risk_amount += risk_amount

        # Calculate risk as percentage of account balance
        total_risk_pct = (total_risk_amount / account_balance) * 100

        return total_risk_pct
    except Exception as e:
        logger.error(f"Error calculating total risk: {e}")
        return 0.0

def calculate_position_size(account_balance, risk_pct, entry_price, stop_loss_price):
    """
    Calculate position size based on risk percentage

    Args:
        account_balance (float): Account balance
        risk_pct (float): Risk percentage (0-100)
        entry_price (float): Entry price
        stop_loss_price (float): Stop loss price

    Returns:
        float: Position size in base currency
    """
    try:
        if account_balance <= 0 or entry_price <= 0 or stop_loss_price <= 0:
            return 0.0

        # Convert risk percentage to decimal
        risk_decimal = risk_pct / 100

        # Calculate risk amount
        risk_amount = account_balance * risk_decimal

        # Calculate price difference
        price_diff = abs(entry_price - stop_loss_price)

        # Calculate position size
        if price_diff > 0:
            position_size = risk_amount / (price_diff / entry_price)
        else:
            position_size = 0

        return position_size
    except Exception as e:
        logger.error(f"Error calculating position size: {e}")
        return 0.0

def check_smart_entry_conditions(market_data):
    """
    Check smart entry conditions for buy and sell signals based on specific criteria:

    Buy (Long Entry):
    - EMA50 > EMA200
    - RSI between 30-40 and breaking upward
    - MACD: Bullish crossover + positive histogram
    - Volume is at least 10% above the average of the last 20 candles
    - ATR is low or moderate (not very high)
    - Bollinger Band breakout (price breaks above upper band)
    - Price at Fibonacci support level

    Sell (Short Entry):
    - EMA50 < EMA200
    - RSI between 60-70 and breaking down
    - MACD: Bearish crossover + negative histogram
    - Volume is 10% above the average
    - ATR is low or moderate
    - Bollinger Band breakout (price breaks below lower band)
    - Price at Fibonacci resistance level

    Args:
        market_data (dict): Market data including indicators and historical data

    Returns:
        dict: Dictionary with buy and sell conditions and scores
    """
    try:
        # Extract data from market_data
        historical = market_data.get("historical", [])
        symbol = market_data.get("symbol", "")

        if not historical or len(historical) < 20:
            logger.warning("Not enough historical data for smart entry conditions")
            return {
                "buy_conditions": {},
                "sell_conditions": {},
                "buy_score": 0,
                "sell_score": 0,
                "buy_signal": False,
                "sell_signal": False
            }

        # Extract price data
        closes = [candle.get("close", 0) for candle in historical]
        highs = [candle.get("high", 0) for candle in historical]
        lows = [candle.get("low", 0) for candle in historical]
        volumes = [candle.get("volume", 0) for candle in historical]
        opens = [candle.get("open", 0) for candle in historical]

        # Calculate basic indicators
        ema50 = calculate_ema(closes, 50)
        ema200 = calculate_ema(closes, 200)
        rsi_values = calculate_rsi(closes)
        macd_line, signal_line, histogram = calculate_macd(closes)
        atr_values = calculate_atr(highs, lows, closes)

        # Calculate Bollinger Bands
        upper_band, middle_band, lower_band = calculate_bollinger_bands(closes)

        # Check for Bollinger Band breakouts
        upper_breakout, lower_breakout = False, False
        if len(upper_band) > 0 and len(lower_band) > 0:
            upper_breakout, lower_breakout = is_bollinger_breakout(closes, upper_band, lower_band)

        # Check for Bollinger Band squeeze
        bollinger_squeeze = False
        if len(upper_band) > 0 and len(lower_band) > 0:
            bollinger_squeeze = is_bollinger_squeeze(upper_band, lower_band)

        # Calculate Fibonacci levels
        recent_high = find_recent_high(highs, 50)
        recent_low = find_recent_low(lows, 50)
        fib_levels = calculate_fibonacci_retracement(recent_high, recent_low)

        # Check if price is at Fibonacci support/resistance
        current_price = closes[-1] if closes else 0
        at_fib_level, fib_level, fib_level_type = identify_fibonacci_support_resistance(current_price, fib_levels)

        # 1. Check EMA conditions
        ema_condition_buy = len(ema50) > 0 and len(ema200) > 0 and ema50[-1] > ema200[-1]
        ema_condition_sell = len(ema50) > 0 and len(ema200) > 0 and ema50[-1] < ema200[-1]

        # Calculate EMA slope to determine if it's sloping upward/downward
        ema50_slope = 0
        if len(ema50) >= 5:
            ema50_slope = (ema50[-1] - ema50[-5]) / ema50[-5] * 100

        ema_sloping_up = ema50_slope > 0
        ema_sloping_down = ema50_slope < 0

        # 2. Check RSI conditions with specific thresholds
        # For buy: RSI between 30-40 and breaking upward
        rsi_condition_buy = False
        if len(rsi_values) >= 3:
            current_rsi = rsi_values[-1]
            prev_rsi = rsi_values[-2]
            rsi_in_buy_zone = 30 <= current_rsi <= 40
            rsi_breaking_up = current_rsi > prev_rsi
            rsi_condition_buy = rsi_in_buy_zone and rsi_breaking_up

        # For sell: RSI between 60-70 and breaking down
        rsi_condition_sell = False
        if len(rsi_values) >= 3:
            current_rsi = rsi_values[-1]
            prev_rsi = rsi_values[-2]
            rsi_in_sell_zone = 60 <= current_rsi <= 70
            rsi_breaking_down = current_rsi < prev_rsi
            rsi_condition_sell = rsi_in_sell_zone and rsi_breaking_down

        # 3. Check MACD conditions
        # For buy: Bullish crossover + positive histogram
        macd_condition_buy = False
        if len(macd_line) >= 2 and len(signal_line) >= 2 and len(histogram) >= 1:
            bullish_crossover = macd_line[-2] < signal_line[-2] and macd_line[-1] > signal_line[-1]
            positive_histogram = histogram[-1] > 0
            macd_condition_buy = bullish_crossover and positive_histogram

        # For sell: Bearish crossover + negative histogram
        macd_condition_sell = False
        if len(macd_line) >= 2 and len(signal_line) >= 2 and len(histogram) >= 1:
            bearish_crossover = macd_line[-2] > signal_line[-2] and macd_line[-1] < signal_line[-1]
            negative_histogram = histogram[-1] < 0
            macd_condition_sell = bearish_crossover and negative_histogram

        # 4. Check volume conditions (10% above average of last 20 candles)
        volume_condition = is_volume_above_average(volumes, 20, 1.1)

        # 5. Check ATR conditions (not very high)
        atr_condition = not is_atr_high(atr_values)

        # 6. Check if it's a low liquidity time (2-5 AM UTC)
        low_liquidity_time = is_low_liquidity_time()

        # 7. Check Fibonacci conditions
        fib_support_condition = at_fib_level and fib_level_type == "support"
        fib_resistance_condition = at_fib_level and fib_level_type == "resistance"

        # 8. Check Bollinger Band conditions
        bollinger_buy_condition = upper_breakout or bollinger_squeeze
        bollinger_sell_condition = lower_breakout or bollinger_squeeze

        # Compile conditions
        buy_conditions = {
            "ema50_gt_ema200": ema_condition_buy,
            "ema_sloping_up": ema_sloping_up,
            "rsi_30_40_breaking_up": rsi_condition_buy,
            "bullish_macd_crossover": macd_condition_buy,
            "volume_above_average": volume_condition,
            "atr_not_high": atr_condition,
            "not_low_liquidity_time": not low_liquidity_time,
            "bollinger_buy_signal": bollinger_buy_condition,
            "fibonacci_support": fib_support_condition
        }

        sell_conditions = {
            "ema50_lt_ema200": ema_condition_sell,
            "ema_sloping_down": ema_sloping_down,
            "rsi_60_70_breaking_down": rsi_condition_sell,
            "bearish_macd_crossover": macd_condition_sell,
            "volume_above_average": volume_condition,
            "atr_not_high": atr_condition,
            "not_low_liquidity_time": not low_liquidity_time,
            "bollinger_sell_signal": bollinger_sell_condition,
            "fibonacci_resistance": fib_resistance_condition
        }

        # Calculate scores
        buy_score = calculate_trade_quality_score(buy_conditions)
        sell_score = calculate_trade_quality_score(sell_conditions)

        # Determine signals (reduced from 70% to 60% as requested - 3 out of 5 conditions)
        # This allows for more trades in sideways markets
        required_score = 60  # Reduced from 70% to 60%

        # Adjust required score based on trend
        if ema_condition_buy and ema_sloping_up:
            # In strong uptrend, reduce required score further
            required_buy_score = max(55, required_score - 5)  # 55-60%
            required_sell_score = required_score + 5  # 65%
        elif ema_condition_sell and ema_sloping_down:
            # In strong downtrend, reduce required score for sells
            required_buy_score = required_score + 5  # 65%
            required_sell_score = max(55, required_score - 5)  # 55-60%
        else:
            # In sideways market, use standard score
            required_buy_score = required_score
            required_sell_score = required_score

        # Apply currency-specific adjustments if symbol is provided
        if symbol:
            # Extract base currency
            base_currency = symbol.split('/')[0] if '/' in symbol else symbol

            # For high-liquidity currencies (BTC, ETH, BNB), slightly reduce required score
            if base_currency in ["BTC", "ETH", "BNB"]:
                required_buy_score = max(55, required_buy_score - 5)
                required_sell_score = max(55, required_sell_score - 5)
                logger.info(f"Adjusted required score for high-liquidity currency {base_currency}")

        buy_signal = buy_score >= required_buy_score
        sell_signal = sell_score >= required_sell_score

        # Log the conditions for debugging
        logger.info(f"Smart entry conditions: Buy score: {buy_score:.1f}% (required: {required_buy_score}%), Sell score: {sell_score:.1f}% (required: {required_sell_score}%)")
        logger.info(f"Smart entry signals: Buy: {buy_signal}, Sell: {sell_signal}")

        # Log Fibonacci and Bollinger Band information
        if at_fib_level:
            logger.info(f"Price at Fibonacci {fib_level} {fib_level_type} level")

        if bollinger_squeeze:
            logger.info(f"Bollinger Band squeeze detected - potential breakout setup")
        elif upper_breakout:
            logger.info(f"Bullish Bollinger Band breakout detected")
        elif lower_breakout:
            logger.info(f"Bearish Bollinger Band breakout detected")

        return {
            "buy_conditions": buy_conditions,
            "sell_conditions": sell_conditions,
            "buy_score": buy_score,
            "sell_score": sell_score,
            "buy_signal": buy_signal,
            "sell_signal": sell_signal,
            "required_buy_score": required_buy_score,
            "required_sell_score": required_sell_score,
            "fibonacci_levels": fib_levels,
            "bollinger_bands": {
                "upper": upper_band[-1] if upper_band else None,
                "middle": middle_band[-1] if middle_band else None,
                "lower": lower_band[-1] if lower_band else None,
                "squeeze": bollinger_squeeze
            }
        }
    except Exception as e:
        logger.error(f"Error checking smart entry conditions: {e}")
        return {
            "buy_conditions": {},
            "sell_conditions": {},
            "buy_score": 0,
            "sell_score": 0,
            "buy_signal": False,
            "sell_signal": False
        }


# ============================================================================
# ADVANCED TECHNICAL INDICATORS - NEW ENHANCEMENTS
# ============================================================================

def calculate_ichimoku_cloud(high_prices, low_prices, close_prices,
                           tenkan_period=9, kijun_period=26, senkou_b_period=52):
    """
    Calculate Ichimoku Cloud components

    Args:
        high_prices (list): List of high prices
        low_prices (list): List of low prices
        close_prices (list): List of close prices
        tenkan_period (int): Tenkan-sen period (default: 9)
        kijun_period (int): Kijun-sen period (default: 26)
        senkou_b_period (int): Senkou Span B period (default: 52)

    Returns:
        dict: Ichimoku components
    """
    try:
        if len(high_prices) < senkou_b_period:
            logger.warning(f"Not enough data for Ichimoku calculation. Need at least {senkou_b_period} data points.")
            return {}

        df = pd.DataFrame({
            'high': high_prices,
            'low': low_prices,
            'close': close_prices
        })

        # Tenkan-sen (Conversion Line): (9-period high + 9-period low) / 2
        df['tenkan_high'] = df['high'].rolling(window=tenkan_period).max()
        df['tenkan_low'] = df['low'].rolling(window=tenkan_period).min()
        df['tenkan_sen'] = (df['tenkan_high'] + df['tenkan_low']) / 2

        # Kijun-sen (Base Line): (26-period high + 26-period low) / 2
        df['kijun_high'] = df['high'].rolling(window=kijun_period).max()
        df['kijun_low'] = df['low'].rolling(window=kijun_period).min()
        df['kijun_sen'] = (df['kijun_high'] + df['kijun_low']) / 2

        # Senkou Span A (Leading Span A): (Tenkan-sen + Kijun-sen) / 2, projected 26 periods ahead
        df['senkou_span_a'] = ((df['tenkan_sen'] + df['kijun_sen']) / 2).shift(kijun_period)

        # Senkou Span B (Leading Span B): (52-period high + 52-period low) / 2, projected 26 periods ahead
        df['senkou_b_high'] = df['high'].rolling(window=senkou_b_period).max()
        df['senkou_b_low'] = df['low'].rolling(window=senkou_b_period).min()
        df['senkou_span_b'] = ((df['senkou_b_high'] + df['senkou_b_low']) / 2).shift(kijun_period)

        # Chikou Span (Lagging Span): Close price projected 26 periods back
        df['chikou_span'] = df['close'].shift(-kijun_period)

        # Cloud thickness (measure of support/resistance strength)
        df['cloud_thickness'] = abs(df['senkou_span_a'] - df['senkou_span_b'])

        # Determine cloud color (green if Senkou A > Senkou B, red otherwise)
        df['cloud_color'] = np.where(df['senkou_span_a'] > df['senkou_span_b'], 'green', 'red')

        # Current values
        current_idx = -1
        result = {
            'tenkan_sen': df['tenkan_sen'].iloc[current_idx] if not pd.isna(df['tenkan_sen'].iloc[current_idx]) else None,
            'kijun_sen': df['kijun_sen'].iloc[current_idx] if not pd.isna(df['kijun_sen'].iloc[current_idx]) else None,
            'senkou_span_a': df['senkou_span_a'].iloc[current_idx] if not pd.isna(df['senkou_span_a'].iloc[current_idx]) else None,
            'senkou_span_b': df['senkou_span_b'].iloc[current_idx] if not pd.isna(df['senkou_span_b'].iloc[current_idx]) else None,
            'chikou_span': df['chikou_span'].iloc[current_idx] if not pd.isna(df['chikou_span'].iloc[current_idx]) else None,
            'cloud_thickness': df['cloud_thickness'].iloc[current_idx] if not pd.isna(df['cloud_thickness'].iloc[current_idx]) else None,
            'cloud_color': df['cloud_color'].iloc[current_idx],
            'current_price': close_prices[-1]
        }

        # Add trend signals
        result['signals'] = analyze_ichimoku_signals(result)

        logger.debug(f"Calculated Ichimoku Cloud: {result}")
        return result

    except Exception as e:
        logger.error(f"Error calculating Ichimoku Cloud: {e}")
        return {}


def analyze_ichimoku_signals(ichimoku_data):
    """
    Analyze Ichimoku signals for trend identification

    Args:
        ichimoku_data (dict): Ichimoku components

    Returns:
        dict: Signal analysis
    """
    try:
        signals = {
            'trend': 'neutral',
            'strength': 0,
            'buy_signal': False,
            'sell_signal': False,
            'cloud_support_resistance': 'neutral'
        }

        if not all(key in ichimoku_data for key in ['tenkan_sen', 'kijun_sen', 'current_price']):
            return signals

        current_price = ichimoku_data['current_price']
        tenkan = ichimoku_data['tenkan_sen']
        kijun = ichimoku_data['kijun_sen']
        senkou_a = ichimoku_data.get('senkou_span_a')
        senkou_b = ichimoku_data.get('senkou_span_b')

        # Tenkan-Kijun cross signals
        if tenkan and kijun:
            if tenkan > kijun:
                signals['buy_signal'] = True
                signals['trend'] = 'bullish'
                signals['strength'] += 1
            elif tenkan < kijun:
                signals['sell_signal'] = True
                signals['trend'] = 'bearish'
                signals['strength'] += 1

        # Price vs Cloud analysis
        if senkou_a and senkou_b:
            cloud_top = max(senkou_a, senkou_b)
            cloud_bottom = min(senkou_a, senkou_b)

            if current_price > cloud_top:
                signals['cloud_support_resistance'] = 'above_cloud'
                signals['strength'] += 2
                if signals['trend'] == 'neutral':
                    signals['trend'] = 'bullish'
            elif current_price < cloud_bottom:
                signals['cloud_support_resistance'] = 'below_cloud'
                signals['strength'] += 2
                if signals['trend'] == 'neutral':
                    signals['trend'] = 'bearish'
            else:
                signals['cloud_support_resistance'] = 'inside_cloud'
                signals['trend'] = 'neutral'

        # Cloud color confirmation
        if ichimoku_data.get('cloud_color') == 'green' and signals['trend'] == 'bullish':
            signals['strength'] += 1
        elif ichimoku_data.get('cloud_color') == 'red' and signals['trend'] == 'bearish':
            signals['strength'] += 1

        return signals

    except Exception as e:
        logger.error(f"Error analyzing Ichimoku signals: {e}")
        return {'trend': 'neutral', 'strength': 0, 'buy_signal': False, 'sell_signal': False}


def calculate_adx(high_prices, low_prices, close_prices, period=14):
    """
    Calculate Average Directional Index (ADX) and Directional Movement Indicators

    Args:
        high_prices (list): List of high prices
        low_prices (list): List of low prices
        close_prices (list): List of close prices
        period (int): ADX period (default: 14)

    Returns:
        dict: ADX, +DI, -DI values and trend strength analysis
    """
    try:
        if len(high_prices) < period + 1:
            logger.warning(f"Not enough data for ADX calculation. Need at least {period + 1} data points.")
            return {}

        df = pd.DataFrame({
            'high': high_prices,
            'low': low_prices,
            'close': close_prices
        })

        # Calculate True Range (TR)
        df['prev_close'] = df['close'].shift(1)
        df['tr1'] = df['high'] - df['low']
        df['tr2'] = abs(df['high'] - df['prev_close'])
        df['tr3'] = abs(df['low'] - df['prev_close'])
        df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)

        # Calculate Directional Movement
        df['dm_plus'] = np.where((df['high'] - df['high'].shift(1)) > (df['low'].shift(1) - df['low']),
                                np.maximum(df['high'] - df['high'].shift(1), 0), 0)
        df['dm_minus'] = np.where((df['low'].shift(1) - df['low']) > (df['high'] - df['high'].shift(1)),
                                 np.maximum(df['low'].shift(1) - df['low'], 0), 0)

        # Calculate smoothed True Range and Directional Movement
        df['atr'] = df['tr'].rolling(window=period).mean()
        df['dm_plus_smooth'] = df['dm_plus'].rolling(window=period).mean()
        df['dm_minus_smooth'] = df['dm_minus'].rolling(window=period).mean()

        # Calculate Directional Indicators
        df['di_plus'] = 100 * (df['dm_plus_smooth'] / df['atr'])
        df['di_minus'] = 100 * (df['dm_minus_smooth'] / df['atr'])

        # Calculate Directional Index (DX)
        df['dx'] = 100 * abs(df['di_plus'] - df['di_minus']) / (df['di_plus'] + df['di_minus'])

        # Calculate ADX (smoothed DX)
        df['adx'] = df['dx'].rolling(window=period).mean()

        # Get current values
        current_idx = -1
        result = {
            'adx': df['adx'].iloc[current_idx] if not pd.isna(df['adx'].iloc[current_idx]) else None,
            'di_plus': df['di_plus'].iloc[current_idx] if not pd.isna(df['di_plus'].iloc[current_idx]) else None,
            'di_minus': df['di_minus'].iloc[current_idx] if not pd.isna(df['di_minus'].iloc[current_idx]) else None,
            'dx': df['dx'].iloc[current_idx] if not pd.isna(df['dx'].iloc[current_idx]) else None
        }

        # Add trend strength analysis
        result['analysis'] = analyze_adx_signals(result)

        logger.debug(f"Calculated ADX: {result}")
        return result

    except Exception as e:
        logger.error(f"Error calculating ADX: {e}")
        return {}


def analyze_adx_signals(adx_data):
    """
    Analyze ADX signals for trend strength and direction

    Args:
        adx_data (dict): ADX components

    Returns:
        dict: Signal analysis
    """
    try:
        analysis = {
            'trend_strength': 'weak',
            'trend_direction': 'neutral',
            'buy_signal': False,
            'sell_signal': False,
            'strength_score': 0
        }

        if not all(key in adx_data for key in ['adx', 'di_plus', 'di_minus']):
            return analysis

        adx = adx_data['adx']
        di_plus = adx_data['di_plus']
        di_minus = adx_data['di_minus']

        # Determine trend strength based on ADX value
        if adx >= 50:
            analysis['trend_strength'] = 'very_strong'
            analysis['strength_score'] = 4
        elif adx >= 30:
            analysis['trend_strength'] = 'strong'
            analysis['strength_score'] = 3
        elif adx >= 20:
            analysis['trend_strength'] = 'moderate'
            analysis['strength_score'] = 2
        elif adx >= 15:
            analysis['trend_strength'] = 'weak'
            analysis['strength_score'] = 1
        else:
            analysis['trend_strength'] = 'very_weak'
            analysis['strength_score'] = 0

        # Determine trend direction based on DI+ vs DI-
        if di_plus > di_minus:
            analysis['trend_direction'] = 'bullish'
            if adx >= 20:  # Only signal if trend is strong enough
                analysis['buy_signal'] = True
        elif di_minus > di_plus:
            analysis['trend_direction'] = 'bearish'
            if adx >= 20:  # Only signal if trend is strong enough
                analysis['sell_signal'] = True
        else:
            analysis['trend_direction'] = 'neutral'

        # Additional signal confirmation
        di_spread = abs(di_plus - di_minus)
        if di_spread >= 10 and adx >= 25:
            # Strong directional movement with good trend strength
            analysis['strength_score'] += 1

        return analysis

    except Exception as e:
        logger.error(f"Error analyzing ADX signals: {e}")
        return {'trend_strength': 'weak', 'trend_direction': 'neutral', 'buy_signal': False, 'sell_signal': False}


def calculate_obv(close_prices, volumes):
    """
    Calculate On-Balance Volume (OBV) indicator

    Args:
        close_prices (list): List of close prices
        volumes (list): List of volume data

    Returns:
        dict: OBV values and trend analysis
    """
    try:
        if len(close_prices) != len(volumes) or len(close_prices) < 2:
            logger.warning("Invalid data for OBV calculation. Need matching close prices and volumes.")
            return {}

        df = pd.DataFrame({
            'close': close_prices,
            'volume': volumes
        })

        # Calculate price change
        df['price_change'] = df['close'].diff()

        # Calculate OBV
        df['obv'] = 0.0
        df.loc[0, 'obv'] = volumes[0]  # Initialize first OBV value

        for i in range(1, len(df)):
            if df.loc[i, 'price_change'] > 0:
                # Price increased: add volume
                df.loc[i, 'obv'] = df.loc[i-1, 'obv'] + df.loc[i, 'volume']
            elif df.loc[i, 'price_change'] < 0:
                # Price decreased: subtract volume
                df.loc[i, 'obv'] = df.loc[i-1, 'obv'] - df.loc[i, 'volume']
            else:
                # Price unchanged: keep same OBV
                df.loc[i, 'obv'] = df.loc[i-1, 'obv']

        # Calculate OBV moving averages for trend analysis
        df['obv_ma_10'] = df['obv'].rolling(window=10).mean()
        df['obv_ma_20'] = df['obv'].rolling(window=20).mean()

        # Get current values
        current_idx = -1
        result = {
            'obv': df['obv'].iloc[current_idx],
            'obv_ma_10': df['obv_ma_10'].iloc[current_idx] if not pd.isna(df['obv_ma_10'].iloc[current_idx]) else None,
            'obv_ma_20': df['obv_ma_20'].iloc[current_idx] if not pd.isna(df['obv_ma_20'].iloc[current_idx]) else None,
            'current_volume': volumes[-1],
            'current_price': close_prices[-1]
        }

        # Add trend analysis
        result['analysis'] = analyze_obv_signals(df, result)

        logger.debug(f"Calculated OBV: {result}")
        return result

    except Exception as e:
        logger.error(f"Error calculating OBV: {e}")
        return {}


def analyze_obv_signals(obv_df, obv_data):
    """
    Analyze OBV signals for volume trend confirmation

    Args:
        obv_df (DataFrame): OBV DataFrame with historical data
        obv_data (dict): Current OBV values

    Returns:
        dict: Signal analysis
    """
    try:
        analysis = {
            'volume_trend': 'neutral',
            'price_volume_divergence': False,
            'buy_signal': False,
            'sell_signal': False,
            'strength_score': 0
        }

        if len(obv_df) < 20:
            return analysis

        # Analyze OBV trend
        obv_current = obv_data['obv']
        obv_ma_10 = obv_data.get('obv_ma_10')
        obv_ma_20 = obv_data.get('obv_ma_20')

        if obv_ma_10 and obv_ma_20:
            if obv_current > obv_ma_10 > obv_ma_20:
                analysis['volume_trend'] = 'bullish'
                analysis['buy_signal'] = True
                analysis['strength_score'] += 2
            elif obv_current < obv_ma_10 < obv_ma_20:
                analysis['volume_trend'] = 'bearish'
                analysis['sell_signal'] = True
                analysis['strength_score'] += 2
            elif obv_current > obv_ma_20:
                analysis['volume_trend'] = 'bullish'
                analysis['strength_score'] += 1
            elif obv_current < obv_ma_20:
                analysis['volume_trend'] = 'bearish'
                analysis['strength_score'] += 1

        # Check for price-volume divergence
        if len(obv_df) >= 10:
            # Compare recent price trend vs OBV trend
            recent_prices = obv_df['close'].tail(10)
            recent_obv = obv_df['obv'].tail(10)

            price_trend = recent_prices.iloc[-1] - recent_prices.iloc[0]
            obv_trend = recent_obv.iloc[-1] - recent_obv.iloc[0]

            # Divergence: price and volume moving in opposite directions
            if (price_trend > 0 and obv_trend < 0) or (price_trend < 0 and obv_trend > 0):
                analysis['price_volume_divergence'] = True
                analysis['strength_score'] += 1
                logger.info(f"Detected price-volume divergence: price_trend={price_trend:.2f}, obv_trend={obv_trend:.2f}")

        # Volume confirmation
        if len(obv_df) >= 5:
            recent_volume_avg = obv_df['volume'].tail(5).mean()
            current_volume = obv_data['current_volume']

            if current_volume > recent_volume_avg * 1.5:
                analysis['strength_score'] += 1
                logger.info(f"High volume confirmation: current={current_volume:.2f}, avg={recent_volume_avg:.2f}")

        return analysis

    except Exception as e:
        logger.error(f"Error analyzing OBV signals: {e}")
        return {'volume_trend': 'neutral', 'price_volume_divergence': False, 'buy_signal': False, 'sell_signal': False}


# ============================================================================
# ML-BASED MARKET DETECTION SYSTEM
# ============================================================================

class MLMarketClassifier:
    """
    Machine Learning-based market regime classifier
    Classifies market conditions as Stable, Volatile, or Unclear
    """

    def __init__(self, model_path="data/ml_models/market_classifier.joblib"):
        """
        Initialize the ML market classifier

        Args:
            model_path (str): Path to save/load the trained model
        """
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False

        # Ensure model directory exists
        os.makedirs(os.path.dirname(model_path), exist_ok=True)

        # Try to load existing model
        self.load_model()

    def extract_features(self, market_data):
        """
        Extract features for ML classification

        Args:
            market_data (dict): Market data with OHLCV and indicators

        Returns:
            np.array: Feature vector
        """
        try:
            features = []

            # Price-based features
            if 'close_prices' in market_data and len(market_data['close_prices']) >= 20:
                closes = market_data['close_prices']

                # Volatility features
                returns = np.diff(closes) / closes[:-1]
                features.extend([
                    np.std(returns[-20:]) * 100,  # 20-period volatility
                    np.std(returns[-10:]) * 100,  # 10-period volatility
                    np.std(returns[-5:]) * 100,   # 5-period volatility
                ])

                # Price movement features
                features.extend([
                    abs(closes[-1] - closes[-2]) / closes[-2] * 100,  # Last candle movement
                    abs(closes[-1] - closes[-5]) / closes[-5] * 100,  # 5-period movement
                    abs(closes[-1] - closes[-10]) / closes[-10] * 100, # 10-period movement
                ])
            else:
                features.extend([0] * 6)  # Default values if not enough data

            # Volume features
            if 'volumes' in market_data and len(market_data['volumes']) >= 20:
                volumes = market_data['volumes']
                vol_avg_20 = np.mean(volumes[-20:])
                vol_avg_5 = np.mean(volumes[-5:])

                features.extend([
                    volumes[-1] / vol_avg_20 if vol_avg_20 > 0 else 1,  # Current vs 20-period avg
                    vol_avg_5 / vol_avg_20 if vol_avg_20 > 0 else 1,    # 5-period vs 20-period avg
                    np.std(volumes[-20:]) / vol_avg_20 if vol_avg_20 > 0 else 0,  # Volume volatility
                ])
            else:
                features.extend([1, 1, 0])  # Default values

            # Technical indicator features
            if 'atr' in market_data and market_data['atr']:
                atr_values = market_data['atr']
                if len(atr_values) >= 10:
                    features.extend([
                        atr_values[-1],  # Current ATR
                        atr_values[-1] / np.mean(atr_values[-10:]) if np.mean(atr_values[-10:]) > 0 else 1,  # ATR ratio
                    ])
                else:
                    features.extend([0, 1])
            else:
                features.extend([0, 1])

            # RSI features
            if 'rsi' in market_data and market_data['rsi']:
                rsi_values = market_data['rsi']
                if len(rsi_values) >= 5:
                    features.extend([
                        rsi_values[-1],  # Current RSI
                        abs(rsi_values[-1] - 50),  # Distance from neutral
                        np.std(rsi_values[-5:]),  # RSI volatility
                    ])
                else:
                    features.extend([50, 0, 0])
            else:
                features.extend([50, 0, 0])

            # Bollinger Band features
            if all(key in market_data for key in ['bb_upper', 'bb_lower', 'bb_middle']):
                bb_upper = market_data['bb_upper']
                bb_lower = market_data['bb_lower']
                bb_middle = market_data['bb_middle']
                current_price = market_data.get('current_price', 0)

                if bb_upper and bb_lower and bb_middle and len(bb_upper) > 0:
                    bb_width = (bb_upper[-1] - bb_lower[-1]) / bb_middle[-1] * 100 if bb_middle[-1] > 0 else 0
                    bb_position = (current_price - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1]) if (bb_upper[-1] - bb_lower[-1]) > 0 else 0.5

                    features.extend([
                        bb_width,  # Band width
                        bb_position,  # Position within bands
                    ])
                else:
                    features.extend([0, 0.5])
            else:
                features.extend([0, 0.5])

            return np.array(features).reshape(1, -1)

        except Exception as e:
            logger.error(f"Error extracting features: {e}")
            # Return default feature vector
            return np.zeros((1, 16))

    def train_model(self, training_data):
        """
        Train the market classification model

        Args:
            training_data (list): List of (features, label) tuples
        """
        try:
            if len(training_data) < 50:
                logger.warning("Not enough training data for ML model")
                return False

            # Prepare training data
            X = np.array([item[0] for item in training_data])
            y = np.array([item[1] for item in training_data])

            # Scale features
            X_scaled = self.scaler.fit_transform(X)

            # Train Random Forest classifier
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                class_weight='balanced'
            )

            self.model.fit(X_scaled, y)
            self.is_trained = True

            # Save model
            self.save_model()

            logger.info(f"ML model trained successfully with {len(training_data)} samples")
            return True

        except Exception as e:
            logger.error(f"Error training ML model: {e}")
            return False

    def predict_market_regime(self, market_data):
        """
        Predict market regime using the trained model

        Args:
            market_data (dict): Market data with indicators

        Returns:
            dict: Prediction results
        """
        try:
            if not self.is_trained:
                # Fallback to rule-based classification
                return self._rule_based_classification(market_data)

            # Extract features
            features = self.extract_features(market_data)

            # Scale features
            features_scaled = self.scaler.transform(features)

            # Make prediction
            prediction = self.model.predict(features_scaled)[0]
            probabilities = self.model.predict_proba(features_scaled)[0]

            # Get class names
            classes = self.model.classes_

            result = {
                'regime': prediction,
                'confidence': max(probabilities),
                'probabilities': {
                    classes[i]: probabilities[i] for i in range(len(classes))
                },
                'method': 'ml'
            }

            logger.debug(f"ML market regime prediction: {result}")
            return result

        except Exception as e:
            logger.error(f"Error predicting market regime: {e}")
            # Fallback to rule-based classification
            return self._rule_based_classification(market_data)

    def _rule_based_classification(self, market_data):
        """
        Fallback rule-based market classification

        Args:
            market_data (dict): Market data

        Returns:
            dict: Classification result
        """
        try:
            volatility_score = 0

            # Check ATR
            if 'atr' in market_data and market_data['atr']:
                atr_values = market_data['atr']
                if len(atr_values) >= 10:
                    current_atr = atr_values[-1]
                    avg_atr = np.mean(atr_values[-10:])
                    if current_atr > avg_atr * 1.5:
                        volatility_score += 2

            # Check price movements
            if 'close_prices' in market_data and len(market_data['close_prices']) >= 5:
                closes = market_data['close_prices']
                recent_change = abs(closes[-1] - closes[-5]) / closes[-5] * 100
                if recent_change > 3:
                    volatility_score += 2
                elif recent_change > 1.5:
                    volatility_score += 1

            # Check volume
            if 'volumes' in market_data and len(market_data['volumes']) >= 10:
                volumes = market_data['volumes']
                current_vol = volumes[-1]
                avg_vol = np.mean(volumes[-10:])
                if current_vol > avg_vol * 2:
                    volatility_score += 1

            # Classify based on score
            if volatility_score >= 4:
                regime = 'volatile'
                confidence = 0.8
            elif volatility_score <= 1:
                regime = 'stable'
                confidence = 0.7
            else:
                regime = 'unclear'
                confidence = 0.6

            return {
                'regime': regime,
                'confidence': confidence,
                'probabilities': {regime: confidence},
                'method': 'rule_based'
            }

        except Exception as e:
            logger.error(f"Error in rule-based classification: {e}")
            return {
                'regime': 'unclear',
                'confidence': 0.5,
                'probabilities': {'unclear': 0.5},
                'method': 'fallback'
            }

    def save_model(self):
        """Save the trained model and scaler"""
        try:
            if self.model and self.is_trained:
                model_data = {
                    'model': self.model,
                    'scaler': self.scaler,
                    'timestamp': datetime.datetime.now().isoformat()
                }
                joblib.dump(model_data, self.model_path)
                logger.info(f"ML model saved to {self.model_path}")
        except Exception as e:
            logger.error(f"Error saving ML model: {e}")

    def load_model(self):
        """Load a previously trained model"""
        try:
            if os.path.exists(self.model_path):
                model_data = joblib.load(self.model_path)
                self.model = model_data['model']
                self.scaler = model_data['scaler']
                self.is_trained = True
                logger.info(f"ML model loaded from {self.model_path}")
                return True
        except Exception as e:
            logger.error(f"Error loading ML model: {e}")

        return False
