"""
Historical Data Loader Module
Loads and validates historical data for technical analysis
"""

import os
import json
import logging
import datetime
import pandas as pd
import numpy as np
from pathlib import Path

# Set up logging
logger = logging.getLogger("historical_data_loader")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/historical_data_loader.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class HistoricalDataLoader:
    """
    Loads and validates historical data for technical analysis
    """

    def __init__(self, exchange_api=None):
        """
        Initialize the historical data loader

        Args:
            exchange_api: Exchange API instance
        """
        self.exchange_api = exchange_api
        self.data_dir = Path("data/historical")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        logger.info("HistoricalDataLoader initialized")

    def ensure_minimum_candles(self, symbol, timeframe='1h', min_candles=200):
        """
        Ensure we have the minimum number of candles required for technical analysis

        Args:
            symbol (str): Trading pair symbol
            timeframe (str): Timeframe (e.g., '1h', '4h', '1d')
            min_candles (int): Minimum number of candles required

        Returns:
            list: Historical candles
        """
        logger.info(f"Ensuring minimum {min_candles} candles for {symbol} ({timeframe})")

        # Try to load from cache first
        cached_data = self.load_from_cache(symbol, timeframe)

        if cached_data and len(cached_data) >= min_candles:
            logger.info(f"Using cached data with {len(cached_data)} candles")
            return cached_data

        # If not enough cached data, fetch from exchange
        if self.exchange_api:
            try:
                # Fetch with a buffer to ensure we have enough
                fetch_limit = min(1000, min_candles + 50)  # Most exchanges limit to 1000
                ohlcv = self.exchange_api.get_ohlcv(symbol, timeframe=timeframe, limit=fetch_limit)

                if ohlcv and len(ohlcv) >= min_candles:
                    logger.info(f"Fetched {len(ohlcv)} candles from exchange")

                    # Save to cache
                    self.save_to_cache(symbol, timeframe, ohlcv)

                    return ohlcv
                else:
                    logger.warning(f"Could not fetch enough candles from exchange. Got {len(ohlcv) if ohlcv else 0}, need {min_candles}")
            except Exception as e:
                logger.error(f"Error fetching historical data: {e}")

        # If we still don't have enough data, try to load from file
        file_data = self.load_from_file(symbol, timeframe)

        if file_data and len(file_data) >= min_candles:
            logger.info(f"Using file data with {len(file_data)} candles")
            return file_data

        logger.error(f"Could not obtain minimum {min_candles} candles for {symbol} ({timeframe})")
        return []

    def load_from_cache(self, symbol, timeframe):
        """
        Load historical data from cache

        Args:
            symbol (str): Trading pair symbol
            timeframe (str): Timeframe

        Returns:
            list: Historical candles
        """
        cache_key = f"{symbol.replace('/', '_')}_{timeframe}"
        cache_file = self.data_dir / f"{cache_key}_cache.json"

        if cache_file.exists():
            try:
                # Check if cache is fresh (less than 1 hour old)
                if datetime.datetime.now().timestamp() - cache_file.stat().st_mtime < 3600:
                    with open(cache_file, 'r') as f:
                        data = json.load(f)
                    logger.info(f"Loaded {len(data)} candles from cache")
                    return data
            except Exception as e:
                logger.error(f"Error loading from cache: {e}")

        return []

    def save_to_cache(self, symbol, timeframe, data):
        """
        Save historical data to cache

        Args:
            symbol (str): Trading pair symbol
            timeframe (str): Timeframe
            data (list): Historical candles
        """
        cache_key = f"{symbol.replace('/', '_')}_{timeframe}"
        cache_file = self.data_dir / f"{cache_key}_cache.json"

        try:
            with open(cache_file, 'w') as f:
                json.dump(data, f)
            logger.info(f"Saved {len(data)} candles to cache")
        except Exception as e:
            logger.error(f"Error saving to cache: {e}")

    def load_from_file(self, symbol, timeframe):
        """
        Load historical data from file

        Args:
            symbol (str): Trading pair symbol
            timeframe (str): Timeframe

        Returns:
            list: Historical candles
        """
        file_key = f"{symbol.replace('/', '_')}_{timeframe}"
        data_file = self.data_dir / f"{file_key}.json"

        if data_file.exists():
            try:
                with open(data_file, 'r') as f:
                    data = json.load(f)
                logger.info(f"Loaded {len(data)} candles from file")
                return data
            except Exception as e:
                logger.error(f"Error loading from file: {e}")

        return []

    def save_to_file(self, symbol, timeframe, data):
        """
        Save historical data to file

        Args:
            symbol (str): Trading pair symbol
            timeframe (str): Timeframe
            data (list): Historical candles
        """
        file_key = f"{symbol.replace('/', '_')}_{timeframe}"
        data_file = self.data_dir / f"{file_key}.json"

        try:
            with open(data_file, 'w') as f:
                json.dump(data, f)
            logger.info(f"Saved {len(data)} candles to file")
        except Exception as e:
            logger.error(f"Error saving to file: {e}")

    def validate_data_quality(self, data, min_candles=200):
        """
        Validate the quality of historical data

        Args:
            data (list): Historical candles
            min_candles (int): Minimum number of candles required

        Returns:
            bool: True if data is valid, False otherwise
        """
        if not data or len(data) < min_candles:
            logger.warning(f"Insufficient data: {len(data) if data else 0} candles (minimum {min_candles})")
            return False

        # Check for missing values
        missing_values = sum(1 for candle in data if any(v is None or v == 0 for v in [
            candle.get('open', 0),
            candle.get('high', 0),
            candle.get('low', 0),
            candle.get('close', 0)
        ]))

        if missing_values > 0:
            missing_pct = (missing_values / len(data)) * 100
            logger.warning(f"Data contains {missing_values} candles with missing values ({missing_pct:.2f}%)")

            # Only fail validation if more than 5% of data is missing
            if missing_pct > 5:
                return False

        # Check for timestamp continuity
        timestamps = [candle.get('timestamp') for candle in data if 'timestamp' in candle]
        if timestamps:
            # Convert string timestamps to datetime if needed
            if isinstance(timestamps[0], str):
                timestamps = [datetime.datetime.fromisoformat(ts.replace('Z', '+00:00')) for ts in timestamps]

            # Check for gaps
            gaps = 0
            for i in range(1, len(timestamps)):
                if isinstance(timestamps[i], datetime.datetime) and isinstance(timestamps[i-1], datetime.datetime):
                    diff = (timestamps[i] - timestamps[i-1]).total_seconds()
                    # For 1h timeframe, gap would be > 3600 seconds
                    if diff > 3600 * 1.5:  # Allow 50% tolerance
                        gaps += 1

            if gaps > 0:
                gap_pct = (gaps / len(data)) * 100
                logger.warning(f"Data contains {gaps} timestamp gaps ({gap_pct:.2f}%)")

                # Only fail validation if more than 5% of data has gaps
                if gap_pct > 5:
                    return False

        logger.info(f"Data validation passed: {len(data)} candles")
        return True

    def clear_cache(self, symbol=None, timeframe=None):
        """
        Clear the data cache

        Args:
            symbol (str, optional): Trading pair symbol. If None, clear all symbols.
            timeframe (str, optional): Timeframe. If None, clear all timeframes.

        Returns:
            int: Number of cache files cleared
        """
        cleared_count = 0

        try:
            # If specific symbol and timeframe are provided
            if symbol and timeframe:
                cache_key = f"{symbol.replace('/', '_')}_{timeframe}"
                cache_file = self.data_dir / f"{cache_key}_cache.json"

                if cache_file.exists():
                    cache_file.unlink()
                    cleared_count += 1
                    logger.info(f"Cleared cache for {symbol} ({timeframe})")

            # If only symbol is provided, clear all timeframes for that symbol
            elif symbol:
                prefix = f"{symbol.replace('/', '_')}_"
                for cache_file in self.data_dir.glob(f"{prefix}*_cache.json"):
                    cache_file.unlink()
                    cleared_count += 1
                    logger.info(f"Cleared cache file: {cache_file.name}")

            # If only timeframe is provided, clear all symbols for that timeframe
            elif timeframe:
                suffix = f"_{timeframe}_cache.json"
                for cache_file in self.data_dir.glob(f"*{suffix}"):
                    cache_file.unlink()
                    cleared_count += 1
                    logger.info(f"Cleared cache file: {cache_file.name}")

            # If neither is provided, clear all cache files
            else:
                for cache_file in self.data_dir.glob("*_cache.json"):
                    cache_file.unlink()
                    cleared_count += 1
                    logger.info(f"Cleared cache file: {cache_file.name}")

            logger.info(f"Cleared {cleared_count} cache files")
            return cleared_count

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return cleared_count

    def merge_data_sources(self, symbol, timeframe, min_candles=200):
        """
        Merge data from multiple sources to ensure we have enough high-quality data

        Args:
            symbol (str): Trading pair symbol
            timeframe (str): Timeframe
            min_candles (int): Minimum number of candles required

        Returns:
            list: Merged historical candles
        """
        # Try to get data from exchange first
        exchange_data = []
        if self.exchange_api:
            try:
                exchange_data = self.exchange_api.get_ohlcv(symbol, timeframe=timeframe, limit=min_candles)
            except Exception as e:
                logger.error(f"Error fetching data from exchange: {e}")

        # Load data from file
        file_data = self.load_from_file(symbol, timeframe)

        # If we have enough exchange data, use it
        if exchange_data and len(exchange_data) >= min_candles:
            # Update our saved file with the latest data
            self.save_to_file(symbol, timeframe, exchange_data)
            return exchange_data

        # If we have enough file data, use it
        if file_data and len(file_data) >= min_candles:
            return file_data

        # If we have both but neither is enough, merge them
        if exchange_data and file_data:
            # Convert to pandas for easier merging
            try:
                # Create DataFrames
                exchange_df = pd.DataFrame(exchange_data)
                file_df = pd.DataFrame(file_data)

                # Ensure we have timestamp column
                if 'timestamp' in exchange_df.columns and 'timestamp' in file_df.columns:
                    # Merge and drop duplicates
                    merged_df = pd.concat([exchange_df, file_df]).drop_duplicates(subset=['timestamp'])

                    # Sort by timestamp
                    merged_df = merged_df.sort_values('timestamp')

                    # Convert back to list of dicts
                    merged_data = merged_df.to_dict('records')

                    if len(merged_data) >= min_candles:
                        logger.info(f"Successfully merged data: {len(merged_data)} candles")
                        # Save the merged data
                        self.save_to_file(symbol, timeframe, merged_data)
                        return merged_data
            except Exception as e:
                logger.error(f"Error merging data: {e}")

        logger.error(f"Could not obtain minimum {min_candles} candles after merging sources")
        return exchange_data if len(exchange_data) > len(file_data) else file_data
