#!/usr/bin/env python
"""
AI Model Evaluator

This module evaluates the performance of AI models and adjusts their weights
based on historical accuracy.
"""

import os
import json
import logging
import datetime
import yaml
from pathlib import Path

# Import the debate enhancer
from ai_services.debate_enhancer import DebateEnhancer

logger = logging.getLogger(__name__)

class AIModelEvaluator:
    """
    Evaluates AI model performance and adjusts weights based on accuracy
    """

    def __init__(self, base_weights=None, config_path=None):
        """
        Initialize the AI model evaluator

        Args:
            base_weights (dict, optional): Base weights for AI models
            config_path (str, optional): Path to configuration file
        """
        # Load configuration
        self.config = self._load_config(config_path)

        # Default base weights - adjusted as requested
        self.base_weights = base_weights or {
            "openai": 0.30,
            "deepseek": 0.35,  # Increased from 0.20 to 0.35
            "qwen": 0.35       # Reduced from 0.50 to 0.35
        }

        # Current weights (will be adjusted based on performance)
        self.current_weights = self.base_weights.copy()

        # Performance tracking
        self.performance_history = {}
        self.predictions = {}

        # Evaluation settings
        self.evaluation_window = 24  # hours
        self.min_predictions = 5  # minimum predictions needed for evaluation
        self.weight_adjustment_factor = 0.05  # maximum weight adjustment per evaluation

        # Weight bounds for each model
        self.weight_bounds = {
            "openai": (0.25, 0.35),
            "deepseek": (0.30, 0.40),
            "qwen": (0.30, 0.40)
        }

        # Initialize debate enhancer
        self.debate_enhancer = DebateEnhancer()

        # Load historical performance data
        self._load_performance_history()

        logger.info(f"AIModelEvaluator initialized with base weights: {self.base_weights}")

    def _load_config(self, config_path=None):
        """
        Load configuration from file

        Args:
            config_path (str, optional): Path to configuration file

        Returns:
            dict: Configuration parameters
        """
        if config_path is None:
            config_path = "config/ai_config.yaml"

        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                        import yaml
                        return yaml.safe_load(f)
                    else:
                        return json.load(f)
            else:
                logger.warning(f"Configuration file {config_path} not found, using defaults")
                return {}
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return {}

    def _load_performance_history(self):
        """
        Load historical performance data from file
        """
        try:
            history_file = Path("data/ai_model_performance.json")

            if history_file.exists():
                with open(history_file, 'r') as f:
                    data = json.load(f)
                    self.performance_history = data.get("performance_history", {})
                    self.current_weights = data.get("current_weights", self.base_weights.copy())

                logger.info(f"Loaded performance history for {len(self.performance_history)} days")
                logger.info(f"Current weights: {self.current_weights}")
            else:
                logger.info("No performance history found, using base weights")

        except Exception as e:
            logger.error(f"Error loading performance history: {e}")

    def _save_performance_history(self):
        """
        Save performance history to file
        """
        try:
            # Create data directory if it doesn't exist
            os.makedirs("data", exist_ok=True)

            history_file = Path("data/ai_model_performance.json")

            data = {
                "performance_history": self.performance_history,
                "current_weights": self.current_weights,
                "last_updated": datetime.datetime.now().isoformat()
            }

            with open(history_file, 'w') as f:
                json.dump(data, f, indent=4)

            logger.info(f"Saved performance history to {history_file}")

        except Exception as e:
            logger.error(f"Error saving performance history: {e}")

    def record_prediction(self, model, prediction, confidence, timestamp=None):
        """
        Record a prediction from an AI model

        Args:
            model (str): Model name (openai, deepseek, qwen)
            prediction (str): Prediction (buy, sell, hold)
            confidence (float): Confidence level (0-1)
            timestamp (str, optional): Timestamp of prediction
        """
        if model not in self.predictions:
            self.predictions[model] = []

        timestamp = timestamp or datetime.datetime.now().isoformat()

        self.predictions[model].append({
            "prediction": prediction,
            "confidence": confidence,
            "timestamp": timestamp,
            "verified": False,
            "correct": None
        })

        logger.debug(f"Recorded prediction from {model}: {prediction} with {confidence:.2f} confidence")

    def verify_prediction(self, model, index, correct):
        """
        Verify if a prediction was correct

        Args:
            model (str): Model name (openai, deepseek, qwen)
            index (int): Index of prediction
            correct (bool): Whether the prediction was correct
        """
        if model in self.predictions and index < len(self.predictions[model]):
            self.predictions[model][index]["verified"] = True
            self.predictions[model][index]["correct"] = correct

            logger.debug(f"Verified prediction from {model} at index {index}: {'correct' if correct else 'incorrect'}")

    def verify_latest_predictions(self, actual_outcome):
        """
        Verify the latest predictions from all models based on actual outcome

        Args:
            actual_outcome (str): Actual outcome (buy, sell, hold)
        """
        for model in self.predictions:
            # Get unverified predictions
            unverified = [p for p in self.predictions[model] if not p["verified"]]

            for i, prediction in enumerate(unverified):
                # Check if prediction matches actual outcome
                correct = prediction["prediction"] == actual_outcome

                # Update prediction
                idx = self.predictions[model].index(prediction)
                self.verify_prediction(model, idx, correct)

        logger.info(f"Verified latest predictions against actual outcome: {actual_outcome}")

    def evaluate_performance(self):
        """
        Evaluate model performance and adjust weights

        Returns:
            dict: Updated weights
        """
        # Get current date
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        # Calculate accuracy for each model
        accuracy = {}
        total_predictions = 0
        predictions_count = {}

        for model in self.predictions:
            # Get verified predictions
            verified = [p for p in self.predictions[model] if p["verified"]]
            predictions_count[model] = len(verified)

            if len(verified) >= self.min_predictions:
                # Calculate accuracy
                correct = sum(1 for p in verified if p["correct"])
                accuracy[model] = correct / len(verified)
                total_predictions += len(verified)

                logger.info(f"{model} accuracy: {accuracy[model]:.2f} ({correct}/{len(verified)})")
            else:
                logger.info(f"{model} has insufficient verified predictions: {len(verified)}/{self.min_predictions}")

        # Only adjust weights if we have enough data
        if len(accuracy) >= 2 and total_predictions >= self.min_predictions * 2:
            # Calculate new weights
            new_weights = self._calculate_new_weights(accuracy)

            # Update current weights
            self.current_weights = new_weights

            # Save performance history
            self.performance_history[today] = {
                "accuracy": accuracy,
                "weights": new_weights,
                "predictions_count": predictions_count,
                "total_predictions": total_predictions
            }

            # Calculate performance trend
            performance_trend = self._calculate_performance_trend(model_accuracies=accuracy)

            # Log performance trends
            for model, trend in performance_trend.items():
                trend_direction = "improving" if trend > 0 else "declining" if trend < 0 else "stable"
                logger.info(f"{model} performance trend: {trend_direction} ({trend:.2f})")

            # Identify underperforming models
            underperforming = [model for model, acc in accuracy.items() if acc < 0.5]
            if underperforming:
                logger.warning(f"Underperforming models detected: {', '.join(underperforming)}")

                # Temporarily reduce weight of severely underperforming models
                for model in underperforming:
                    if accuracy[model] < 0.3:  # Less than 30% accuracy
                        logger.warning(f"Severely underperforming model {model} detected. Temporarily reducing weight.")
                        # Reduce weight by half but ensure it's not zero
                        new_weights[model] = max(0.05, new_weights[model] / 2)

                # Renormalize weights
                total_weight = sum(new_weights.values())
                for model in new_weights:
                    new_weights[model] /= total_weight

            self._save_performance_history()

            logger.info(f"Updated weights based on performance: {new_weights}")
        else:
            logger.info("Insufficient data to adjust weights")

        return self.current_weights

    def _calculate_performance_trend(self, model_accuracies, days=7):
        """
        Calculate performance trend for each model over the specified number of days

        Args:
            model_accuracies (dict): Current model accuracies
            days (int): Number of days to analyze

        Returns:
            dict: Performance trend for each model (-1 to 1 scale)
        """
        trends = {}

        # Get recent days
        recent_days = sorted(self.performance_history.keys())[-days:] if len(self.performance_history) >= days else []

        if not recent_days:
            # Not enough historical data
            return {model: 0 for model in model_accuracies}

        # Calculate trend for each model
        for model in model_accuracies:
            # Get historical accuracies
            historical_acc = []

            for day in recent_days:
                if day in self.performance_history and "accuracy" in self.performance_history[day]:
                    if model in self.performance_history[day]["accuracy"]:
                        historical_acc.append(self.performance_history[day]["accuracy"][model])

            # Calculate trend if we have enough data
            if len(historical_acc) >= 3:
                # Simple trend calculation: compare average of recent days to average of earlier days
                mid_point = len(historical_acc) // 2
                recent_avg = sum(historical_acc[mid_point:]) / len(historical_acc[mid_point:])
                earlier_avg = sum(historical_acc[:mid_point]) / len(historical_acc[:mid_point])

                # Calculate trend (-1 to 1 scale)
                trend = min(1.0, max(-1.0, (recent_avg - earlier_avg) * 2))
                trends[model] = trend
            else:
                # Not enough data for trend
                trends[model] = 0

        return trends

    def _calculate_new_weights(self, accuracy):
        """
        Calculate new weights based on accuracy

        Args:
            accuracy (dict): Accuracy for each model

        Returns:
            dict: New weights
        """
        # Start with base weights
        new_weights = self.current_weights.copy()

        # Calculate average accuracy
        avg_accuracy = sum(accuracy.values()) / len(accuracy)

        # Adjust weights based on relative performance
        for model in accuracy:
            # Calculate relative performance
            relative_performance = accuracy[model] - avg_accuracy

            # Calculate weight adjustment (limited by adjustment factor)
            adjustment = min(
                self.weight_adjustment_factor,
                max(-self.weight_adjustment_factor, relative_performance / 2)
            )

            # Apply adjustment
            new_weights[model] += adjustment

        # Normalize weights to sum to 1
        total_weight = sum(new_weights.values())
        for model in new_weights:
            new_weights[model] /= total_weight

        return new_weights

    def get_current_weights(self):
        """
        Get current model weights

        Returns:
            dict: Current weights
        """
        return self.current_weights

    def get_performance_history(self):
        """
        Get performance history

        Returns:
            dict: Performance history
        """
        return self.performance_history

    def get_debate_weights(self, signals):
        """
        Get adjusted weights for debate mode when models disagree

        Args:
            signals (dict): Signals from AI models

        Returns:
            dict: Adjusted weights for debate
        """
        # Check if models disagree
        recommendations = set()
        for source in signals:
            if "analysis" in signals[source]:
                recommendation = signals[source]["analysis"].get("recommendation", "hold").lower()
                recommendations.add(recommendation)

        # If all models agree or only one model provided a signal, use current weights
        if len(recommendations) <= 1 or len(signals) <= 1:
            return self.current_weights.copy()

        # Enhanced AI Debate Mode
        # Models disagree, adjust weights based on historical accuracy
        logger.info("AI Debate Mode activated: Models disagree on recommendation")
        logger.info(f"Recommendations: {recommendations}")

        # Extract model recommendations and confidences
        model_recommendations = {}
        model_confidences = {}
        detailed_analyses = {}

        for source in signals:
            if "analysis" in signals[source]:
                model_recommendations[source] = signals[source]["analysis"].get("recommendation", "hold").lower()
                model_confidences[source] = signals[source]["analysis"].get("confidence", 50) / 100.0

                # Extract detailed analysis if available
                if "detailed_analysis" in signals[source]:
                    detailed_analyses[source] = signals[source]["detailed_analysis"]
                elif "explanation" in signals[source]["analysis"]:
                    detailed_analyses[source] = signals[source]["analysis"]["explanation"]
                else:
                    detailed_analyses[source] = "No detailed analysis provided."

        # Get debate weights from debate enhancer
        debate_weights = self.debate_enhancer.get_debate_weights(self.current_weights)

        # Log the debate session with detailed analyses
        models_involved = list(model_recommendations.keys())
        final_decision = self._calculate_weighted_decision(model_recommendations, model_confidences, debate_weights)

        self.debate_enhancer.log_debate_session(
            models_involved=models_involved,
            recommendations=model_recommendations,
            confidences=model_confidences,
            weights=debate_weights,
            final_decision=final_decision,
            detailed_analyses=detailed_analyses
        )

        # Generate and log a human-readable debate summary
        debate_data = {
            "session_id": len(self.predictions.get('openai', [])),
            "timestamp": datetime.datetime.now().isoformat(),
            "models_involved": models_involved,
            "recommendations": model_recommendations,
            "confidences": model_confidences,
            "weights": debate_weights,
            "final_decision": final_decision,
            "detailed_analyses": detailed_analyses
        }

        debate_summary = self.debate_enhancer.format_debate_summary(debate_data)
        logger.info(f"Debate Summary:\n{debate_summary}")

        # Store the debate results for future reference
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        debate_key = f"{today}_debate_{len(self.predictions.get('openai', []))}"

        if today not in self.performance_history:
            self.performance_history[today] = {}

        self.performance_history[today][debate_key] = {
            "recommendations": model_recommendations,
            "confidences": model_confidences,
            "adjusted_weights": debate_weights,
            "final_decision": final_decision
        }

        # Save the updated performance history
        self._save_performance_history()

        return debate_weights

    def _calculate_weighted_decision(self, recommendations, confidences, weights):
        """
        Calculate weighted decision based on model recommendations, confidences, and weights

        Args:
            recommendations (dict): Recommendations from each model
            confidences (dict): Confidence levels from each model
            weights (dict): Weights for each model

        Returns:
            str: Weighted decision
        """
        # Calculate weighted scores for each recommendation
        weighted_scores = {"buy": 0, "sell": 0, "hold": 0}

        for model, recommendation in recommendations.items():
            if model in weights and model in confidences:
                weighted_scores[recommendation] += weights[model] * confidences[model]

        # Find recommendation with highest weighted score
        return max(weighted_scores.items(), key=lambda x: x[1])[0]

    def generate_daily_report(self):
        """
        Generate a comprehensive daily performance report

        Returns:
            dict: Performance report
        """
        # Get current date
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        # Calculate accuracy for each model
        accuracy = {}
        predictions_count = {}
        correct_predictions = {}
        confidence_avg = {}
        prediction_details = {}

        for model in self.predictions:
            # Get verified predictions
            verified = [p for p in self.predictions[model] if p["verified"]]
            predictions_count[model] = len(verified)

            if verified:
                # Calculate accuracy
                correct = sum(1 for p in verified if p["correct"])
                correct_predictions[model] = correct
                accuracy[model] = correct / len(verified)

                # Calculate average confidence
                confidence_avg[model] = sum(p["confidence"] for p in verified) / len(verified)

                # Collect prediction details
                prediction_details[model] = {
                    "buy": {
                        "count": sum(1 for p in verified if p["prediction"] == "buy"),
                        "correct": sum(1 for p in verified if p["prediction"] == "buy" and p["correct"])
                    },
                    "sell": {
                        "count": sum(1 for p in verified if p["prediction"] == "sell"),
                        "correct": sum(1 for p in verified if p["prediction"] == "sell" and p["correct"])
                    },
                    "hold": {
                        "count": sum(1 for p in verified if p["prediction"] == "hold"),
                        "correct": sum(1 for p in verified if p["prediction"] == "hold" and p["correct"])
                    }
                }

                # Calculate accuracy by prediction type
                for pred_type in ["buy", "sell", "hold"]:
                    count = prediction_details[model][pred_type]["count"]
                    if count > 0:
                        prediction_details[model][pred_type]["accuracy"] = prediction_details[model][pred_type]["correct"] / count
                    else:
                        prediction_details[model][pred_type]["accuracy"] = 0

        # Calculate performance trend
        performance_trend = self._calculate_performance_trend(model_accuracies=accuracy)

        # Generate report
        report = {
            "date": today,
            "accuracy": accuracy,
            "predictions_count": predictions_count,
            "correct_predictions": correct_predictions,
            "confidence_avg": confidence_avg,
            "current_weights": self.current_weights,
            "total_verified_predictions": sum(predictions_count.values()),
            "performance_trend": performance_trend,
            "prediction_details": prediction_details
        }

        # Add debate statistics if available
        debate_count = 0
        for day in self.performance_history:
            for key in self.performance_history[day]:
                if key.startswith(f"{day}_debate_"):
                    debate_count += 1

        report["debate_statistics"] = {
            "total_debates": debate_count
        }

        # Add model recommendations
        report["model_recommendations"] = self._generate_model_recommendations(accuracy, performance_trend)

        # Save report to file
        try:
            # Create reports directory if it doesn't exist
            os.makedirs("reports", exist_ok=True)

            report_file = Path(f"reports/ai_performance_{today}.json")

            with open(report_file, 'w') as f:
                json.dump(report, f, indent=4)

            # Generate human-readable summary
            summary_file = Path(f"reports/ai_performance_summary_{today}.txt")

            with open(summary_file, 'w') as f:
                f.write(f"AI Model Performance Summary - {today}\n")
                f.write("=" * 50 + "\n\n")

                f.write("Overall Accuracy:\n")
                for model, acc in sorted(accuracy.items(), key=lambda x: x[1], reverse=True):
                    trend_symbol = "↑" if performance_trend.get(model, 0) > 0.1 else "↓" if performance_trend.get(model, 0) < -0.1 else "→"
                    f.write(f"  - {model}: {acc:.2%} ({correct_predictions.get(model, 0)}/{predictions_count.get(model, 0)}) {trend_symbol}\n")

                f.write("\nCurrent Model Weights:\n")
                for model, weight in sorted(self.current_weights.items(), key=lambda x: x[1], reverse=True):
                    f.write(f"  - {model}: {weight:.2%}\n")

                f.write("\nPrediction Type Accuracy:\n")
                for model in prediction_details:
                    f.write(f"  {model}:\n")
                    for pred_type in ["buy", "sell", "hold"]:
                        details = prediction_details[model][pred_type]
                        if details["count"] > 0:
                            f.write(f"    - {pred_type}: {details['accuracy']:.2%} ({details['correct']}/{details['count']})\n")

                f.write("\nRecommendations:\n")
                for rec in report["model_recommendations"]:
                    f.write(f"  - {rec}\n")

            logger.info(f"Generated daily performance report: {report_file}")
            logger.info(f"Generated human-readable summary: {summary_file}")

        except Exception as e:
            logger.error(f"Error saving daily report: {e}")

        return report

    def evaluate_long_term_performance(self, days=7):
        """
        Evaluate AI model performance over multiple days

        Args:
            days (int): Number of days for performance analysis

        Returns:
            dict: Model performance over the requested period
        """
        try:
            # Get recent days
            recent_days = sorted(self.performance_history.keys())[-days:] if len(self.performance_history) >= days else []

            if not recent_days:
                logger.warning(f"Insufficient historical data for long-term evaluation (requested {days} days)")
                return {
                    "status": "insufficient_data",
                    "message": f"Insufficient historical data for long-term evaluation (requested {days} days)"
                }

            # Initialize performance data
            performance_data = {
                "period": {
                    "start_date": recent_days[0],
                    "end_date": recent_days[-1],
                    "days_analyzed": len(recent_days)
                },
                "models": {}
            }

            # Analyze each model
            for model in self.base_weights.keys():
                model_data = {
                    "accuracy": {
                        "daily": {},
                        "average": 0,
                        "trend": 0
                    },
                    "predictions": {
                        "total": 0,
                        "correct": 0,
                        "by_type": {
                            "buy": {"total": 0, "correct": 0, "accuracy": 0},
                            "sell": {"total": 0, "correct": 0, "accuracy": 0},
                            "hold": {"total": 0, "correct": 0, "accuracy": 0}
                        }
                    },
                    "confidence": {
                        "average": 0,
                        "correlation_with_accuracy": 0
                    },
                    "weight": {
                        "initial": self.base_weights.get(model, 0),
                        "current": self.current_weights.get(model, 0),
                        "change": 0
                    }
                }

                # Collect daily accuracy data
                daily_accuracies = []
                daily_confidences = []
                total_predictions = 0
                total_correct = 0

                for day in recent_days:
                    if day in self.performance_history and "accuracy" in self.performance_history[day]:
                        if model in self.performance_history[day]["accuracy"]:
                            accuracy = self.performance_history[day]["accuracy"][model]
                            model_data["accuracy"]["daily"][day] = accuracy
                            daily_accuracies.append(accuracy)

                            # Collect prediction counts
                            if "predictions_count" in self.performance_history[day] and model in self.performance_history[day]["predictions_count"]:
                                pred_count = self.performance_history[day]["predictions_count"][model]
                                total_predictions += pred_count

                            if "correct_predictions" in self.performance_history[day] and model in self.performance_history[day]["correct_predictions"]:
                                correct_count = self.performance_history[day]["correct_predictions"][model]
                                total_correct += correct_count

                            # Collect prediction details by type
                            if "prediction_details" in self.performance_history[day] and model in self.performance_history[day]["prediction_details"]:
                                for pred_type in ["buy", "sell", "hold"]:
                                    if pred_type in self.performance_history[day]["prediction_details"][model]:
                                        details = self.performance_history[day]["prediction_details"][model][pred_type]
                                        model_data["predictions"]["by_type"][pred_type]["total"] += details.get("count", 0)
                                        model_data["predictions"]["by_type"][pred_type]["correct"] += details.get("correct", 0)

                            # Collect confidence data
                            if "confidence_avg" in self.performance_history[day] and model in self.performance_history[day]["confidence_avg"]:
                                confidence = self.performance_history[day]["confidence_avg"][model]
                                daily_confidences.append(confidence)

                # Calculate average accuracy
                if daily_accuracies:
                    model_data["accuracy"]["average"] = sum(daily_accuracies) / len(daily_accuracies)

                # Calculate accuracy trend
                if len(daily_accuracies) >= 3:
                    # Simple trend calculation
                    first_half = daily_accuracies[:len(daily_accuracies)//2]
                    second_half = daily_accuracies[len(daily_accuracies)//2:]

                    first_half_avg = sum(first_half) / len(first_half) if first_half else 0
                    second_half_avg = sum(second_half) / len(second_half) if second_half else 0

                    model_data["accuracy"]["trend"] = second_half_avg - first_half_avg

                # Update prediction counts
                model_data["predictions"]["total"] = total_predictions
                model_data["predictions"]["correct"] = total_correct

                # Calculate accuracy by prediction type
                for pred_type in ["buy", "sell", "hold"]:
                    total = model_data["predictions"]["by_type"][pred_type]["total"]
                    correct = model_data["predictions"]["by_type"][pred_type]["correct"]

                    if total > 0:
                        model_data["predictions"]["by_type"][pred_type]["accuracy"] = correct / total

                # Calculate average confidence
                if daily_confidences:
                    model_data["confidence"]["average"] = sum(daily_confidences) / len(daily_confidences)

                # Calculate correlation between confidence and accuracy
                if len(daily_confidences) == len(daily_accuracies) and len(daily_confidences) > 3:
                    # Simple correlation calculation
                    mean_conf = sum(daily_confidences) / len(daily_confidences)
                    mean_acc = sum(daily_accuracies) / len(daily_accuracies)

                    numerator = sum((conf - mean_conf) * (acc - mean_acc) for conf, acc in zip(daily_confidences, daily_accuracies))
                    denominator_conf = sum((conf - mean_conf) ** 2 for conf in daily_confidences)
                    denominator_acc = sum((acc - mean_acc) ** 2 for acc in daily_accuracies)

                    if denominator_conf > 0 and denominator_acc > 0:
                        correlation = numerator / ((denominator_conf ** 0.5) * (denominator_acc ** 0.5))
                        model_data["confidence"]["correlation_with_accuracy"] = correlation

                # Calculate weight change
                model_data["weight"]["change"] = model_data["weight"]["current"] - model_data["weight"]["initial"]

                # Add model data to performance data
                performance_data["models"][model] = model_data

            # Calculate overall statistics
            performance_data["overall"] = {
                "best_model": max(
                    [(model, data["accuracy"]["average"]) for model, data in performance_data["models"].items()],
                    key=lambda x: x[1]
                )[0] if performance_data["models"] else None,
                "most_improved": max(
                    [(model, data["accuracy"]["trend"]) for model, data in performance_data["models"].items()],
                    key=lambda x: x[1]
                )[0] if performance_data["models"] else None,
                "recommendations": self._generate_long_term_recommendations(performance_data)
            }

            # Save long-term evaluation to file
            try:
                # Create reports directory if it doesn't exist
                os.makedirs("reports/long_term", exist_ok=True)

                report_file = Path(f"reports/long_term/evaluation_{datetime.datetime.now().strftime('%Y%m%d')}.json")

                with open(report_file, 'w') as f:
                    json.dump(performance_data, f, indent=4)

                logger.info(f"Saved long-term performance evaluation to {report_file}")
            except Exception as e:
                logger.error(f"Error saving long-term evaluation: {e}")

            return performance_data

        except Exception as e:
            logger.error(f"Error evaluating long-term performance: {e}")
            return {
                "status": "error",
                "message": f"Error evaluating long-term performance: {e}"
            }

    def _generate_long_term_recommendations(self, performance_data):
        """
        Generate recommendations based on long-term performance analysis

        Args:
            performance_data (dict): Long-term performance data

        Returns:
            list: Recommendations
        """
        recommendations = []

        # Check if we have enough data
        if not performance_data.get("models"):
            return ["Insufficient data for long-term recommendations"]

        # Identify best and worst performing models
        models_by_accuracy = sorted(
            [(model, data["accuracy"]["average"]) for model, data in performance_data["models"].items()],
            key=lambda x: x[1],
            reverse=True
        )

        best_model, best_accuracy = models_by_accuracy[0]
        worst_model, worst_accuracy = models_by_accuracy[-1]

        # Check for significant accuracy gap
        if best_accuracy - worst_accuracy > 0.2:  # 20% gap
            recommendations.append(
                f"Consider increasing {best_model} weight and decreasing {worst_model} weight due to significant performance gap "
                f"({best_accuracy:.2%} vs {worst_accuracy:.2%})"
            )

        # Check for improving models
        improving_models = [
            (model, data["accuracy"]["trend"])
            for model, data in performance_data["models"].items()
            if data["accuracy"]["trend"] > 0.1  # 10% improvement
        ]

        for model, trend in improving_models:
            recommendations.append(
                f"Consider increasing {model} weight due to improving trend (+{trend:.2%} over analysis period)"
            )

        # Check for declining models
        declining_models = [
            (model, data["accuracy"]["trend"])
            for model, data in performance_data["models"].items()
            if data["accuracy"]["trend"] < -0.1  # 10% decline
        ]

        for model, trend in declining_models:
            recommendations.append(
                f"Consider decreasing {model} weight due to declining trend ({trend:.2%} over analysis period)"
            )

        # Check for models with poor confidence correlation
        for model, data in performance_data["models"].items():
            correlation = data["confidence"]["correlation_with_accuracy"]
            if correlation < 0.3 and data["predictions"]["total"] > 10:
                recommendations.append(
                    f"{model} shows poor correlation between confidence and accuracy ({correlation:.2f}). "
                    f"Consider adjusting how confidence is factored into decisions."
                )

        # Check for prediction type imbalance
        for model, data in performance_data["models"].items():
            buy_acc = data["predictions"]["by_type"]["buy"]["accuracy"]
            sell_acc = data["predictions"]["by_type"]["sell"]["accuracy"]

            buy_count = data["predictions"]["by_type"]["buy"]["total"]
            sell_count = data["predictions"]["by_type"]["sell"]["total"]

            if buy_count > 5 and sell_count > 5 and abs(buy_acc - sell_acc) > 0.3:
                better_type = "buy" if buy_acc > sell_acc else "sell"
                recommendations.append(
                    f"{model} performs significantly better on {better_type} signals. "
                    f"Consider specializing this model for {better_type} recommendations."
                )

        # If no specific recommendations, add a general one
        if not recommendations:
            recommendations.append("Current model configuration appears optimal based on long-term performance data")

        return recommendations

    def _generate_model_recommendations(self, accuracy, performance_trend):
        """
        Generate recommendations based on model performance

        Args:
            accuracy (dict): Model accuracy
            performance_trend (dict): Model performance trend

        Returns:
            list: Recommendations
        """
        recommendations = []

        # Check for underperforming models
        for model, acc in accuracy.items():
            if acc < 0.4:
                trend = performance_trend.get(model, 0)
                if trend < -0.1:
                    recommendations.append(f"Consider reducing {model} weight further due to declining performance ({acc:.2%} accuracy)")
                elif trend > 0.1:
                    recommendations.append(f"Monitor {model} closely - low accuracy ({acc:.2%}) but improving trend")
                else:
                    recommendations.append(f"Consider reducing {model} weight due to poor performance ({acc:.2%} accuracy)")

        # Check for high-performing models
        best_model = max(accuracy.items(), key=lambda x: x[1])[0] if accuracy else None
        if best_model and accuracy[best_model] > 0.7:
            recommendations.append(f"Consider increasing {best_model} weight due to strong performance ({accuracy[best_model]:.2%} accuracy)")

        # Check for weight imbalance
        weights = self.current_weights
        if max(weights.values()) - min(weights.values()) > 0.3:
            recommendations.append("Consider rebalancing model weights to avoid over-reliance on a single model")

        # If no specific recommendations, add a general one
        if not recommendations:
            recommendations.append("Current model weights appear optimal based on performance data")

        return recommendations
