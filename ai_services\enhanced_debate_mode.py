"""
Enhanced AI Debate Mode with Dynamic Weight Adjustment
Implements advanced debate resolution with model accuracy tracking
"""

import os
import json
import logging
import datetime
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# Set up logging
logger = logging.getLogger("enhanced_debate_mode")
logger.setLevel(logging.INFO)

# Ensure logs directory exists
os.makedirs("logs", exist_ok=True)

# Add file handler if not already added
if not any(isinstance(h, logging.FileHandler) for h in logger.handlers):
    handler = logging.FileHandler("logs/enhanced_debate_mode.log")
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)


@dataclass
class ModelPrediction:
    """Data class for model predictions"""
    model_name: str
    recommendation: str
    confidence: float
    reasoning: str
    timestamp: datetime.datetime
    market_data_hash: str


@dataclass
class ModelPerformance:
    """Data class for model performance tracking"""
    model_name: str
    total_predictions: int
    correct_predictions: int
    accuracy: float
    recent_accuracy: float  # Last 20 predictions
    weight_adjustment: float
    last_updated: datetime.datetime


class EnhancedDebateMode:
    """
    Enhanced AI Debate Mode with dynamic weight adjustment and performance tracking
    """
    
    def __init__(self, base_weights=None, performance_file="data/ai_performance/model_performance.json"):
        """
        Initialize Enhanced Debate Mode
        
        Args:
            base_weights (dict): Base weights for AI models
            performance_file (str): Path to performance tracking file
        """
        self.base_weights = base_weights or {
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        }
        
        self.performance_file = performance_file
        self.model_performances = {}
        self.prediction_history = []
        self.debate_sessions = []
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(performance_file), exist_ok=True)
        
        # Load existing performance data
        self.load_performance_data()
        
        # Initialize model performances if not loaded
        for model_name in self.base_weights.keys():
            if model_name not in self.model_performances:
                self.model_performances[model_name] = ModelPerformance(
                    model_name=model_name,
                    total_predictions=0,
                    correct_predictions=0,
                    accuracy=0.5,  # Start with neutral accuracy
                    recent_accuracy=0.5,
                    weight_adjustment=0.0,
                    last_updated=datetime.datetime.now()
                )
    
    def conduct_debate(self, ai_signals, market_data=None):
        """
        Conduct enhanced debate between AI models with dynamic weight adjustment
        
        Args:
            ai_signals (dict): Signals from different AI models
            market_data (dict): Current market data for context
        
        Returns:
            dict: Debate results with final decision
        """
        try:
            logger.info("Starting enhanced AI debate session")
            
            # Extract model predictions
            predictions = self._extract_predictions(ai_signals)
            
            if len(predictions) < 2:
                logger.warning("Not enough models for debate. Need at least 2 models.")
                return self._single_model_decision(predictions[0] if predictions else None)
            
            # Calculate dynamic weights
            dynamic_weights = self._calculate_dynamic_weights()
            
            # Check for consensus
            consensus_result = self._check_consensus(predictions, dynamic_weights)
            if consensus_result['has_consensus']:
                logger.info(f"AI models reached consensus: {consensus_result['decision']}")
                return consensus_result
            
            # Conduct detailed debate
            debate_result = self._conduct_detailed_debate(predictions, dynamic_weights, market_data)
            
            # Log debate session
            self._log_debate_session(predictions, dynamic_weights, debate_result, market_data)
            
            return debate_result
            
        except Exception as e:
            logger.error(f"Error in enhanced debate: {e}")
            return self._fallback_decision(ai_signals)
    
    def _extract_predictions(self, ai_signals):
        """Extract structured predictions from AI signals"""
        predictions = []
        
        for model_name, signal_data in ai_signals.items():
            if model_name in self.base_weights and 'analysis' in signal_data:
                analysis = signal_data['analysis']
                
                prediction = ModelPrediction(
                    model_name=model_name,
                    recommendation=analysis.get('recommendation', 'hold').lower(),
                    confidence=analysis.get('confidence', 50) / 100.0,
                    reasoning=analysis.get('reasoning', 'No reasoning provided'),
                    timestamp=datetime.datetime.now(),
                    market_data_hash=self._hash_market_data(signal_data.get('market_data', {}))
                )
                
                predictions.append(prediction)
        
        return predictions
    
    def _calculate_dynamic_weights(self):
        """Calculate dynamic weights based on model performance"""
        dynamic_weights = {}
        
        # Get performance-based adjustments
        total_adjustment = 0
        for model_name, base_weight in self.base_weights.items():
            performance = self.model_performances.get(model_name)
            
            if performance:
                # Calculate weight adjustment based on recent accuracy
                accuracy_bonus = (performance.recent_accuracy - 0.5) * 0.2  # Max ±10% adjustment
                weight_adjustment = min(max(accuracy_bonus, -0.1), 0.1)  # Cap at ±10%
                
                dynamic_weights[model_name] = base_weight + weight_adjustment
                total_adjustment += weight_adjustment
                
                # Update performance tracking
                performance.weight_adjustment = weight_adjustment
            else:
                dynamic_weights[model_name] = base_weight
        
        # Normalize weights to sum to 1.0
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            dynamic_weights = {k: v / total_weight for k, v in dynamic_weights.items()}
        
        logger.info(f"Dynamic weights calculated: {dynamic_weights}")
        return dynamic_weights
    
    def _check_consensus(self, predictions, weights):
        """Check if models have reached consensus"""
        if len(predictions) < 2:
            return {'has_consensus': False}
        
        # Group predictions by recommendation
        recommendation_weights = {}
        for pred in predictions:
            rec = pred.recommendation
            weight = weights.get(pred.model_name, 0)
            confidence_weight = weight * pred.confidence
            
            if rec not in recommendation_weights:
                recommendation_weights[rec] = 0
            recommendation_weights[rec] += confidence_weight
        
        # Check if any recommendation has >70% weighted support
        total_weight = sum(recommendation_weights.values())
        if total_weight > 0:
            for rec, weight in recommendation_weights.items():
                if weight / total_weight > 0.7:
                    return {
                        'has_consensus': True,
                        'decision': rec,
                        'confidence': weight / total_weight,
                        'method': 'consensus',
                        'supporting_models': [p.model_name for p in predictions if p.recommendation == rec]
                    }
        
        return {'has_consensus': False}
    
    def _conduct_detailed_debate(self, predictions, weights, market_data):
        """Conduct detailed debate with conflict resolution"""
        logger.info("Conducting detailed debate - no consensus reached")
        
        # Calculate weighted scores for each recommendation
        recommendation_scores = {}
        model_contributions = {}
        
        for pred in predictions:
            rec = pred.recommendation
            model_weight = weights.get(pred.model_name, 0)
            
            # Calculate contribution score (weight * confidence * performance_factor)
            performance = self.model_performances.get(pred.model_name)
            performance_factor = performance.recent_accuracy if performance else 0.5
            
            contribution_score = model_weight * pred.confidence * performance_factor
            
            if rec not in recommendation_scores:
                recommendation_scores[rec] = 0
                model_contributions[rec] = []
            
            recommendation_scores[rec] += contribution_score
            model_contributions[rec].append({
                'model': pred.model_name,
                'weight': model_weight,
                'confidence': pred.confidence,
                'performance': performance_factor,
                'contribution': contribution_score,
                'reasoning': pred.reasoning
            })
        
        # Find winning recommendation
        if recommendation_scores:
            winning_rec = max(recommendation_scores.keys(), key=lambda x: recommendation_scores[x])
            winning_score = recommendation_scores[winning_rec]
            total_score = sum(recommendation_scores.values())
            
            final_confidence = winning_score / total_score if total_score > 0 else 0.5
            
            # Apply confidence threshold
            if final_confidence < 0.55:
                winning_rec = 'hold'
                final_confidence = 0.6
                logger.info("Low confidence in debate result, defaulting to 'hold'")
            
            return {
                'decision': winning_rec,
                'confidence': final_confidence,
                'method': 'detailed_debate',
                'recommendation_scores': recommendation_scores,
                'model_contributions': model_contributions,
                'debate_summary': self._generate_debate_summary(predictions, model_contributions)
            }
        
        # Fallback
        return {
            'decision': 'hold',
            'confidence': 0.5,
            'method': 'fallback',
            'reason': 'Unable to resolve debate'
        }
    
    def _generate_debate_summary(self, predictions, model_contributions):
        """Generate a summary of the debate"""
        summary = {
            'total_models': len(predictions),
            'recommendations': {},
            'key_arguments': []
        }
        
        # Count recommendations
        for pred in predictions:
            rec = pred.recommendation
            if rec not in summary['recommendations']:
                summary['recommendations'][rec] = 0
            summary['recommendations'][rec] += 1
        
        # Extract key arguments
        for rec, contributions in model_contributions.items():
            for contrib in contributions:
                if contrib['contribution'] > 0.1:  # Only include significant contributions
                    summary['key_arguments'].append({
                        'model': contrib['model'],
                        'recommendation': rec,
                        'reasoning': contrib['reasoning'][:200],  # Truncate long reasoning
                        'weight': contrib['contribution']
                    })
        
        return summary
    
    def update_model_performance(self, model_name, prediction, actual_outcome):
        """
        Update model performance based on prediction accuracy
        
        Args:
            model_name (str): Name of the model
            prediction (str): Model's prediction
            actual_outcome (str): Actual market outcome
        """
        try:
            if model_name not in self.model_performances:
                return
            
            performance = self.model_performances[model_name]
            
            # Determine if prediction was correct
            is_correct = self._evaluate_prediction_accuracy(prediction, actual_outcome)
            
            # Update total statistics
            performance.total_predictions += 1
            if is_correct:
                performance.correct_predictions += 1
            
            # Calculate overall accuracy
            performance.accuracy = performance.correct_predictions / performance.total_predictions
            
            # Calculate recent accuracy (last 20 predictions)
            recent_predictions = [p for p in self.prediction_history 
                                if p.model_name == model_name][-20:]
            
            if len(recent_predictions) >= 5:  # Need at least 5 recent predictions
                recent_correct = sum(1 for p in recent_predictions 
                                   if hasattr(p, 'was_correct') and p.was_correct)
                performance.recent_accuracy = recent_correct / len(recent_predictions)
            else:
                performance.recent_accuracy = performance.accuracy
            
            performance.last_updated = datetime.datetime.now()
            
            # Save updated performance
            self.save_performance_data()
            
            logger.info(f"Updated {model_name} performance: "
                       f"accuracy={performance.accuracy:.3f}, "
                       f"recent_accuracy={performance.recent_accuracy:.3f}")
            
        except Exception as e:
            logger.error(f"Error updating model performance: {e}")
    
    def _evaluate_prediction_accuracy(self, prediction, actual_outcome):
        """Evaluate if a prediction was accurate"""
        # Simple evaluation - can be enhanced with more sophisticated logic
        if prediction == actual_outcome:
            return True
        
        # Consider partial accuracy for hold predictions
        if prediction == 'hold' and actual_outcome in ['small_gain', 'small_loss']:
            return True
        
        return False
    
    def _hash_market_data(self, market_data):
        """Create a hash of market data for tracking"""
        try:
            # Simple hash based on key market indicators
            hash_string = f"{market_data.get('price', 0):.2f}_{market_data.get('volume', 0):.0f}"
            return str(hash(hash_string))
        except:
            return "unknown"
    
    def _log_debate_session(self, predictions, weights, result, market_data):
        """Log detailed debate session"""
        session = {
            'timestamp': datetime.datetime.now().isoformat(),
            'predictions': [
                {
                    'model': p.model_name,
                    'recommendation': p.recommendation,
                    'confidence': p.confidence,
                    'reasoning': p.reasoning[:100]  # Truncate for storage
                }
                for p in predictions
            ],
            'weights': weights,
            'result': result,
            'market_context': {
                'price': market_data.get('current_price') if market_data else None,
                'volume': market_data.get('current_volume') if market_data else None
            }
        }
        
        self.debate_sessions.append(session)
        
        # Keep only last 100 sessions
        if len(self.debate_sessions) > 100:
            self.debate_sessions = self.debate_sessions[-100:]
    
    def save_performance_data(self):
        """Save model performance data to file"""
        try:
            data = {
                'model_performances': {
                    name: {
                        'model_name': perf.model_name,
                        'total_predictions': perf.total_predictions,
                        'correct_predictions': perf.correct_predictions,
                        'accuracy': perf.accuracy,
                        'recent_accuracy': perf.recent_accuracy,
                        'weight_adjustment': perf.weight_adjustment,
                        'last_updated': perf.last_updated.isoformat()
                    }
                    for name, perf in self.model_performances.items()
                },
                'last_updated': datetime.datetime.now().isoformat()
            }
            
            with open(self.performance_file, 'w') as f:
                json.dump(data, f, indent=2)
                
            logger.debug(f"Performance data saved to {self.performance_file}")
            
        except Exception as e:
            logger.error(f"Error saving performance data: {e}")
    
    def load_performance_data(self):
        """Load model performance data from file"""
        try:
            if os.path.exists(self.performance_file):
                with open(self.performance_file, 'r') as f:
                    data = json.load(f)
                
                for name, perf_data in data.get('model_performances', {}).items():
                    self.model_performances[name] = ModelPerformance(
                        model_name=perf_data['model_name'],
                        total_predictions=perf_data['total_predictions'],
                        correct_predictions=perf_data['correct_predictions'],
                        accuracy=perf_data['accuracy'],
                        recent_accuracy=perf_data['recent_accuracy'],
                        weight_adjustment=perf_data['weight_adjustment'],
                        last_updated=datetime.datetime.fromisoformat(perf_data['last_updated'])
                    )
                
                logger.info(f"Performance data loaded from {self.performance_file}")
                
        except Exception as e:
            logger.error(f"Error loading performance data: {e}")
    
    def _single_model_decision(self, prediction):
        """Handle case with only one model"""
        if not prediction:
            return {'decision': 'hold', 'confidence': 0.5, 'method': 'no_models'}
        
        return {
            'decision': prediction.recommendation,
            'confidence': prediction.confidence,
            'method': 'single_model',
            'model': prediction.model_name
        }
    
    def _fallback_decision(self, ai_signals):
        """Fallback decision when debate fails"""
        # Simple majority vote as fallback
        recommendations = []
        for signal in ai_signals.values():
            if 'analysis' in signal:
                recommendations.append(signal['analysis'].get('recommendation', 'hold'))
        
        if recommendations:
            # Most common recommendation
            decision = max(set(recommendations), key=recommendations.count)
            confidence = recommendations.count(decision) / len(recommendations)
        else:
            decision = 'hold'
            confidence = 0.5
        
        return {
            'decision': decision,
            'confidence': confidence,
            'method': 'fallback'
        }
    
    def get_model_performance_summary(self):
        """Get summary of model performances"""
        summary = {}
        for name, perf in self.model_performances.items():
            summary[name] = {
                'accuracy': perf.accuracy,
                'recent_accuracy': perf.recent_accuracy,
                'total_predictions': perf.total_predictions,
                'weight_adjustment': perf.weight_adjustment,
                'current_weight': self.base_weights.get(name, 0) + perf.weight_adjustment
            }
        
        return summary
