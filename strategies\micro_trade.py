"""
Micro-Trade Mode Module

This module implements a micro-trade mode for small accounts.
"""

import os
import yaml
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class MicroTradeManager:
    """
    Micro-Trade Manager for small accounts
    
    Adjusts position sizing, leverage, and stacking entries based on account size.
    """
    
    def __init__(self, config_path="config/micro_trade_config.yaml"):
        """
        Initialize the micro-trade manager
        
        Args:
            config_path (str): Path to micro-trade configuration file
        """
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Extract settings
        self.micro_trade_settings = self.config.get("micro_trade_mode", {
            "enabled": True,
            "small_account_threshold": 25.0,
            "max_position_size_percent": 0.5
        })
        
        self.position_sizing = self.config.get("position_sizing", {
            "very_small_account": {"risk_percent": 0.5, "max_leverage": 1.5},
            "small_account": {"risk_percent": 1.0, "max_leverage": 2.0},
            "normal_account": {"risk_percent": 2.0, "max_leverage": 3.0}
        })
        
        self.stacking_entries = self.config.get("stacking_entries", {
            "very_small_account": {"first_entry_percent": 30, "second_entry_percent": 70},
            "small_account": {"first_entry_percent": 40, "second_entry_percent": 60},
            "normal_account": {"first_entry_percent": 50, "second_entry_percent": 50}
        })
        
        logger.info("MicroTradeManager initialized")
    
    def _load_config(self, config_path):
        """
        Load configuration from YAML file
        
        Args:
            config_path (str): Path to configuration file
            
        Returns:
            dict: Configuration parameters
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"Error loading micro-trade config: {e}")
            return {
                "micro_trade_mode": {
                    "enabled": True,
                    "small_account_threshold": 25.0,
                    "max_position_size_percent": 0.5
                },
                "position_sizing": {
                    "very_small_account": {"risk_percent": 0.5, "max_leverage": 1.5},
                    "small_account": {"risk_percent": 1.0, "max_leverage": 2.0},
                    "normal_account": {"risk_percent": 2.0, "max_leverage": 3.0}
                },
                "stacking_entries": {
                    "very_small_account": {"first_entry_percent": 30, "second_entry_percent": 70},
                    "small_account": {"first_entry_percent": 40, "second_entry_percent": 60},
                    "normal_account": {"first_entry_percent": 50, "second_entry_percent": 50}
                }
            }
    
    def is_micro_trade_mode_enabled(self):
        """
        Check if micro-trade mode is enabled
        
        Returns:
            bool: True if micro-trade mode is enabled, False otherwise
        """
        return self.micro_trade_settings.get("enabled", True)
    
    def determine_account_type(self, account_balance):
        """
        Determine account type based on balance
        
        Args:
            account_balance (float): Account balance in USDT
            
        Returns:
            str: Account type ('very_small_account', 'small_account', or 'normal_account')
        """
        small_account_threshold = self.micro_trade_settings.get("small_account_threshold", 25.0)
        
        if account_balance < small_account_threshold:
            logger.info(f"Very small account detected: ${account_balance:.2f} < ${small_account_threshold:.2f}")
            return "very_small_account"
        elif account_balance < 50.0:
            logger.info(f"Small account detected: ${account_balance:.2f} < $50.00")
            return "small_account"
        else:
            logger.info(f"Normal account detected: ${account_balance:.2f} >= $50.00")
            return "normal_account"
    
    def calculate_position_size(self, account_balance, entry_price, stop_loss_price):
        """
        Calculate position size based on account balance
        
        Args:
            account_balance (float): Account balance in USDT
            entry_price (float): Entry price
            stop_loss_price (float): Stop loss price
            
        Returns:
            float: Position size in base currency
        """
        # Determine account type
        account_type = self.determine_account_type(account_balance)
        
        # Get risk percentage based on account type
        risk_percent = self.position_sizing.get(account_type, {}).get("risk_percent", 1.0)
        
        # Calculate risk amount
        risk_amount = account_balance * (risk_percent / 100)
        
        # Calculate position size
        if entry_price <= 0 or stop_loss_price <= 0 or entry_price == stop_loss_price:
            logger.warning(f"Invalid prices: entry={entry_price}, stop_loss={stop_loss_price}")
            return 0
        
        # Calculate stop loss percentage
        if entry_price > stop_loss_price:  # Long position
            stop_loss_pct = (entry_price - stop_loss_price) / entry_price
        else:  # Short position
            stop_loss_pct = (stop_loss_price - entry_price) / entry_price
        
        # Calculate position size
        position_size = risk_amount / (entry_price * stop_loss_pct)
        
        # Apply maximum position size limit for very small accounts
        if account_type == "very_small_account":
            max_position_size_percent = self.micro_trade_settings.get("max_position_size_percent", 0.5)
            max_position_size = account_balance * (max_position_size_percent / 100)
            position_size = min(position_size, max_position_size / entry_price)
        
        logger.info(f"Calculated position size for {account_type}: {position_size:.6f} units")
        logger.info(f"  Risk: {risk_percent:.1f}% of ${account_balance:.2f} = ${risk_amount:.2f}")
        logger.info(f"  Stop loss: {stop_loss_pct * 100:.2f}%")
        
        return position_size
    
    def get_leverage(self, account_balance):
        """
        Get leverage based on account balance
        
        Args:
            account_balance (float): Account balance in USDT
            
        Returns:
            float: Maximum leverage
        """
        # Determine account type
        account_type = self.determine_account_type(account_balance)
        
        # Get leverage based on account type
        leverage = self.position_sizing.get(account_type, {}).get("max_leverage", 2.0)
        
        logger.info(f"Using leverage for {account_type}: {leverage}x")
        
        return leverage
    
    def get_stacking_entry_percentages(self, account_balance):
        """
        Get stacking entry percentages based on account balance
        
        Args:
            account_balance (float): Account balance in USDT
            
        Returns:
            tuple: (first_entry_percent, second_entry_percent)
        """
        # Determine account type
        account_type = self.determine_account_type(account_balance)
        
        # Get stacking entry percentages based on account type
        first_entry_percent = self.stacking_entries.get(account_type, {}).get("first_entry_percent", 50)
        second_entry_percent = self.stacking_entries.get(account_type, {}).get("second_entry_percent", 50)
        
        logger.info(f"Stacking entries for {account_type}: {first_entry_percent}% + {second_entry_percent}%")
        
        return first_entry_percent, second_entry_percent
