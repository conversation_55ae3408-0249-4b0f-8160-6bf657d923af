"""
Memory Guard Module

This module provides utilities for monitoring and managing memory usage
to prevent memory leaks and optimize performance on low-end systems.
"""

import os
import gc
import time
import psutil
import logging
import threading
from typing import Dict, Any, Optional, Tuple, List, Callable

# Configure logging
logger = logging.getLogger(__name__)

class MemoryGuard:
    """
    Memory and CPU usage monitor with automatic optimization
    """
    
    def __init__(self, 
                 memory_threshold: float = 75.0, 
                 cpu_threshold: float = 85.0,
                 check_interval: int = 60,
                 gc_threshold: float = 70.0,
                 emergency_threshold: float = 90.0,
                 low_memory_callback: Optional[Callable] = None):
        """
        Initialize the memory guard
        
        Args:
            memory_threshold (float): Memory usage threshold percentage
            cpu_threshold (float): CPU usage threshold percentage
            check_interval (int): Check interval in seconds
            gc_threshold (float): Threshold to trigger garbage collection
            emergency_threshold (float): Threshold for emergency measures
            low_memory_callback (Callable, optional): Callback for low memory condition
        """
        self.memory_threshold = memory_threshold
        self.cpu_threshold = cpu_threshold
        self.check_interval = check_interval
        self.gc_threshold = gc_threshold
        self.emergency_threshold = emergency_threshold
        self.low_memory_callback = low_memory_callback
        
        # Initialize monitoring
        self.process = psutil.Process(os.getpid())
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # Initialize stats
        self.stats = {
            "memory_usage": 0.0,
            "cpu_usage": 0.0,
            "gc_collections": 0,
            "emergency_actions": 0,
            "last_check": time.time()
        }
        
        # Initialize object tracking for memory leak detection
        self.object_counts = {}
        self.previous_counts = {}
        
        logger.info(f"MemoryGuard initialized with thresholds: Memory {memory_threshold}%, CPU {cpu_threshold}%")
    
    def start_monitoring(self) -> None:
        """
        Start the monitoring thread
        """
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            logger.warning("Monitoring thread is already running")
            return
        
        self.stop_event.clear()
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info(f"Memory monitoring started with interval {self.check_interval}s")
    
    def stop_monitoring(self) -> None:
        """
        Stop the monitoring thread
        """
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.stop_event.set()
            self.monitoring_thread.join(timeout=5)
            logger.info("Memory monitoring stopped")
    
    def _monitoring_loop(self) -> None:
        """
        Main monitoring loop
        """
        while not self.stop_event.is_set():
            try:
                # Check memory and CPU usage
                self._check_resources()
                
                # Sleep until next check
                for _ in range(self.check_interval):
                    if self.stop_event.is_set():
                        break
                    time.sleep(1)
            
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Sleep for a bit before retrying
    
    def _check_resources(self) -> None:
        """
        Check memory and CPU usage and take action if needed
        """
        try:
            # Get memory usage
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            # Get CPU usage
            cpu_percent = self.process.cpu_percent(interval=1.0)
            
            # Update stats
            self.stats["memory_usage"] = memory_percent
            self.stats["cpu_usage"] = cpu_percent
            self.stats["last_check"] = time.time()
            
            # Log current usage
            logger.debug(f"Memory: {memory_percent:.1f}% ({memory_info.rss / (1024 * 1024):.1f} MB), CPU: {cpu_percent:.1f}%")
            
            # Check for memory leaks
            if memory_percent > self.gc_threshold:
                self._check_for_leaks()
            
            # Take action based on thresholds
            if memory_percent > self.emergency_threshold or cpu_percent > self.cpu_threshold:
                # Emergency measures
                self._emergency_optimization()
                self.stats["emergency_actions"] += 1
                
                # Call low memory callback if provided
                if self.low_memory_callback and callable(self.low_memory_callback):
                    self.low_memory_callback()
            
            elif memory_percent > self.memory_threshold:
                # Regular optimization
                self._optimize_memory()
                self.stats["gc_collections"] += 1
        
        except Exception as e:
            logger.error(f"Error checking resources: {e}")
    
    def _optimize_memory(self) -> None:
        """
        Perform memory optimization
        """
        # Run garbage collection
        gc.collect()
        
        logger.info("Memory optimization performed (garbage collection)")
    
    def _emergency_optimization(self) -> None:
        """
        Perform emergency memory optimization
        """
        # Run full garbage collection
        gc.collect(0)
        gc.collect(1)
        gc.collect(2)
        
        # Clear caches
        self._clear_caches()
        
        logger.warning("Emergency memory optimization performed")
    
    def _clear_caches(self) -> None:
        """
        Clear various caches to free memory
        """
        # Clear function cache if available
        if hasattr(gc, 'get_objects'):
            for obj in gc.get_objects():
                if hasattr(obj, 'cache_clear') and callable(obj.cache_clear):
                    try:
                        obj.cache_clear()
                    except:
                        pass
        
        # Clear any module-specific caches here
        # Example: self._clear_pandas_cache()
    
    def _check_for_leaks(self) -> None:
        """
        Check for potential memory leaks
        """
        # Get current object counts
        self.previous_counts = self.object_counts.copy()
        self.object_counts = {}
        
        # Count objects by type
        for obj in gc.get_objects():
            obj_type = type(obj).__name__
            self.object_counts[obj_type] = self.object_counts.get(obj_type, 0) + 1
        
        # Check for significant increases
        for obj_type, count in self.object_counts.items():
            prev_count = self.previous_counts.get(obj_type, 0)
            if prev_count > 0 and count > 100 and count > prev_count * 2:
                logger.warning(f"Potential memory leak detected: {obj_type} count increased from {prev_count} to {count}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get current memory and CPU statistics
        
        Returns:
            Dict[str, Any]: Statistics
        """
        return self.stats
    
    def get_detailed_memory_info(self) -> Dict[str, Any]:
        """
        Get detailed memory information
        
        Returns:
            Dict[str, Any]: Detailed memory information
        """
        memory_info = self.process.memory_info()
        
        return {
            "rss": memory_info.rss / (1024 * 1024),  # MB
            "vms": memory_info.vms / (1024 * 1024),  # MB
            "percent": self.process.memory_percent(),
            "gc_stats": {
                "collections": [gc.get_count()[i] for i in range(3)],
                "thresholds": gc.get_threshold()
            }
        }
    
    def optimize_for_low_end_cpu(self) -> None:
        """
        Apply optimizations for low-end CPUs
        """
        # Set garbage collection thresholds for low-end systems
        gc.set_threshold(700, 10, 5)  # More aggressive GC
        
        # Set process priority
        try:
            self.process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS)
            logger.info("Process priority set to below normal for low-end CPU optimization")
        except:
            logger.warning("Failed to set process priority")
        
        # Disable background threads if possible
        # This is application-specific and would need to be implemented
        # based on the specific requirements
        
        logger.info("Low-end CPU optimizations applied")

# Global instance
memory_guard = MemoryGuard()

def start_memory_monitoring(memory_threshold: float = 75.0, 
                           cpu_threshold: float = 85.0,
                           check_interval: int = 60,
                           low_memory_callback: Optional[Callable] = None) -> None:
    """
    Start memory monitoring with the specified thresholds
    
    Args:
        memory_threshold (float): Memory usage threshold percentage
        cpu_threshold (float): CPU usage threshold percentage
        check_interval (int): Check interval in seconds
        low_memory_callback (Callable, optional): Callback for low memory condition
    """
    global memory_guard
    memory_guard = MemoryGuard(
        memory_threshold=memory_threshold,
        cpu_threshold=cpu_threshold,
        check_interval=check_interval,
        low_memory_callback=low_memory_callback
    )
    memory_guard.start_monitoring()

def stop_memory_monitoring() -> None:
    """
    Stop memory monitoring
    """
    global memory_guard
    memory_guard.stop_monitoring()

def get_memory_stats() -> Dict[str, Any]:
    """
    Get current memory statistics
    
    Returns:
        Dict[str, Any]: Memory statistics
    """
    global memory_guard
    return memory_guard.get_stats()

def optimize_for_low_end_system() -> None:
    """
    Apply optimizations for low-end systems
    """
    global memory_guard
    memory_guard.optimize_for_low_end_cpu()
