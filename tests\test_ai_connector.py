"""
Test for AI Connector
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_services.ai_connector import MultiAIConnector, AIServiceAccount

class TestAIConnector(unittest.TestCase):
    """
    Test AI Connector
    """

    def setUp(self):
        """
        Set up test environment
        """
        # Mock model evaluator
        self.model_evaluator = MagicMock()
        self.model_evaluator.get_current_weights.return_value = {
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        }
        
        # Create AI connector
        with patch('ai_services.ai_connector.AIServiceManager'):
            self.connector = MultiAIConnector(model_evaluator=self.model_evaluator)
        
        # Mock service manager
        self.connector.service_manager = MagicMock()
        
        # Create mock accounts
        self.openai_account = AIServiceAccount("openai", "account1", "fake_key")
        self.deepseek_account = AIServiceAccount("deepseek", "account1", "fake_key")
        self.qwen_account = AIServiceAccount("qwen", "account1", "fake_key")
        
        # Set up service manager to return mock accounts
        self.connector.service_manager.get_next_openai_account.return_value = self.openai_account
        self.connector.service_manager.get_next_deepseek_account.return_value = self.deepseek_account
        self.connector.service_manager.get_next_qwen_account.return_value = self.qwen_account

    def test_openai_api_with_system_prompt(self):
        """
        Test OpenAI API with system prompt
        """
        # Test data
        formatted_data = "Test market data"
        custom_system_prompt = "Custom system prompt"
        
        # Call API with system prompt
        with patch('time.sleep'):  # Skip sleep
            result = self.connector._call_openai_api(
                self.openai_account, 
                formatted_data, 
                system_prompt=custom_system_prompt
            )
        
        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result["service"], "openai")
        self.assertEqual(result["account_id"], "account1")
        self.assertIn("analysis", result)
        self.assertIn("timestamp", result)

    def test_openai_api_with_user_prompt(self):
        """
        Test OpenAI API with user prompt
        """
        # Test data
        formatted_data = "Test market data"
        user_prompt = "Additional user prompt"
        
        # Call API with user prompt
        with patch('time.sleep'):  # Skip sleep
            result = self.connector._call_openai_api(
                self.openai_account, 
                formatted_data, 
                user_prompt=user_prompt
            )
        
        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result["service"], "openai")
        self.assertEqual(result["account_id"], "account1")
        self.assertIn("analysis", result)
        self.assertIn("timestamp", result)

    def test_deepseek_api_with_system_prompt(self):
        """
        Test DeepSeek API with system prompt
        """
        # Test data
        formatted_data = "Test market data"
        custom_system_prompt = "Custom system prompt"
        
        # Call API with system prompt
        with patch('time.sleep'):  # Skip sleep
            result = self.connector._call_deepseek_api(
                self.deepseek_account, 
                formatted_data, 
                system_prompt=custom_system_prompt
            )
        
        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result["service"], "deepseek")
        self.assertEqual(result["account_id"], "account1")
        self.assertIn("analysis", result)
        self.assertIn("timestamp", result)

    def test_qwen_api_with_system_prompt(self):
        """
        Test Qwen API with system prompt
        """
        # Test data
        formatted_data = "Test market data"
        custom_system_prompt = "Custom system prompt"
        
        # Call API with system prompt
        with patch('time.sleep'):  # Skip sleep
            result = self.connector._call_qwen_api(
                self.qwen_account, 
                formatted_data, 
                system_prompt=custom_system_prompt
            )
        
        # Verify result
        self.assertIsNotNone(result)
        self.assertEqual(result["service"], "qwen")
        self.assertEqual(result["account_id"], "account1")
        self.assertIn("analysis", result)
        self.assertIn("timestamp", result)

if __name__ == "__main__":
    unittest.main()
