"""
Enhanced AI Debate Mode

This module enhances the AI debate mode with detailed logging,
historical performance tracking, and automatic weight adjustment.
"""

import os
import json
import logging
import datetime
from pathlib import Path
import numpy as np

logger = logging.getLogger(__name__)

class DebateEnhancer:
    """
    Enhances the AI debate mode with advanced features
    """

    def __init__(self, base_path="data/ai_performance"):
        """
        Initialize the debate enhancer

        Args:
            base_path (str): Base path for storing performance data
        """
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)

        # Load historical performance data
        self.performance_history = self._load_performance_history()

        # Initialize debate session counter
        self.debate_sessions = 0

        # Define weight bounds
        self.weight_bounds = {
            "openai": (0.25, 0.35),
            "deepseek": (0.30, 0.40),
            "qwen": (0.30, 0.40)
        }

        # Initialize current weights
        self.current_weights = {
            "openai": 0.30,
            "deepseek": 0.35,
            "qwen": 0.35
        }

        logger.info("DebateEnhancer initialized")

    def _load_performance_history(self):
        """
        Load historical performance data

        Returns:
            dict: Performance history
        """
        history_file = self.base_path / "history.json"

        if history_file.exists():
            try:
                with open(history_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading performance history: {e}")

        return {}

    def _save_performance_history(self):
        """
        Save performance history to file
        """
        history_file = self.base_path / "history.json"

        try:
            with open(history_file, 'w') as f:
                json.dump(self.performance_history, f, indent=4)
        except Exception as e:
            logger.error(f"Error saving performance history: {e}")

    def record_model_accuracy(self, model_accuracies):
        """
        Record daily model accuracy

        Args:
            model_accuracies (dict): Accuracy for each model

        Returns:
            str: Path to the saved file
        """
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        # Create data structure
        data = {
            "date": today,
            "openai_accuracy": model_accuracies.get("openai", 0),
            "deepseek_accuracy": model_accuracies.get("deepseek", 0),
            "qwen_accuracy": model_accuracies.get("qwen", 0)
        }

        # Save to file
        file_path = self.base_path / f"accuracy_{today}.json"
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=4)

        # Update performance history
        self.performance_history[today] = data
        self._save_performance_history()

        logger.info(f"Recorded model accuracy for {today}: {data}")

        return str(file_path)

    def update_ai_weights(self):
        """
        Update AI weights based on weekly historical performance

        Returns:
            dict: Updated weights
        """
        # Get last 7 days of performance data
        today = datetime.datetime.now().date()
        last_7_days = []

        for i in range(7):
            date_str = (today - datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            if date_str in self.performance_history:
                last_7_days.append(self.performance_history[date_str])

        if not last_7_days:
            logger.warning("No performance data available for the last 7 days")
            return self.current_weights

        # Calculate average accuracy for each model
        avg_accuracy = {
            "openai": 0,
            "deepseek": 0,
            "qwen": 0
        }

        for day in last_7_days:
            avg_accuracy["openai"] += day.get("openai_accuracy", 0)
            avg_accuracy["deepseek"] += day.get("deepseek_accuracy", 0)
            avg_accuracy["qwen"] += day.get("qwen_accuracy", 0)

        for model in avg_accuracy:
            avg_accuracy[model] /= len(last_7_days)

        # Calculate new weights based on relative performance
        total_accuracy = sum(avg_accuracy.values())
        if total_accuracy == 0:
            logger.warning("Total accuracy is zero, keeping current weights")
            return self.current_weights

        # Calculate raw weights based on accuracy
        raw_weights = {
            model: acc / total_accuracy
            for model, acc in avg_accuracy.items()
        }

        # Apply bounds
        bounded_weights = {}
        for model, weight in raw_weights.items():
            min_weight, max_weight = self.weight_bounds.get(model, (0, 1))
            bounded_weights[model] = max(min_weight, min(max_weight, weight))

        # Normalize weights to sum to 1
        total_bounded_weight = sum(bounded_weights.values())
        normalized_weights = {
            model: weight / total_bounded_weight
            for model, weight in bounded_weights.items()
        }

        # Update current weights
        self.current_weights = normalized_weights

        logger.info(f"Updated AI weights based on weekly performance: {self.current_weights}")

        return self.current_weights

    def get_top_performing_model(self, days=7):
        """
        Get the top performing model over the specified number of days

        Args:
            days (int): Number of days to consider

        Returns:
            tuple: (model_name, accuracy)
        """
        # Get performance data for the specified days
        today = datetime.datetime.now().date()
        performance_data = []

        for i in range(days):
            date_str = (today - datetime.timedelta(days=i)).strftime("%Y-%m-%d")
            if date_str in self.performance_history:
                performance_data.append(self.performance_history[date_str])

        if not performance_data:
            logger.warning(f"No performance data available for the last {days} days")
            return None, 0

        # Calculate average accuracy for each model
        avg_accuracy = {
            "openai": 0,
            "deepseek": 0,
            "qwen": 0
        }

        for day in performance_data:
            avg_accuracy["openai"] += day.get("openai_accuracy", 0)
            avg_accuracy["deepseek"] += day.get("deepseek_accuracy", 0)
            avg_accuracy["qwen"] += day.get("qwen_accuracy", 0)

        for model in avg_accuracy:
            avg_accuracy[model] /= len(performance_data)

        # Find top performing model
        top_model = max(avg_accuracy.items(), key=lambda x: x[1])

        logger.info(f"Top performing model over last {days} days: {top_model[0]} with accuracy {top_model[1]:.2f}")

        return top_model

    def get_debate_weights(self, base_weights=None):
        """
        Get weights for debate mode, giving extra weight to the top performing model

        Args:
            base_weights (dict, optional): Base weights to adjust

        Returns:
            dict: Adjusted weights for debate mode
        """
        # Use current weights if base weights not provided
        if base_weights is None:
            base_weights = self.current_weights.copy()

        # Get top performing model
        top_model, top_accuracy = self.get_top_performing_model()

        if top_model is None:
            logger.warning("No top performing model found, using base weights")
            return base_weights

        # Add 10% extra weight to top performing model
        debate_weights = base_weights.copy()
        extra_weight = 0.10  # 10% extra weight

        # Add extra weight to top model
        debate_weights[top_model] += extra_weight

        # Normalize weights to sum to 1
        total_weight = sum(debate_weights.values())
        normalized_weights = {
            model: weight / total_weight
            for model, weight in debate_weights.items()
        }

        logger.info(f"Debate weights: {normalized_weights} (added {extra_weight:.0%} to {top_model})")

        return normalized_weights

    def log_debate_session(self, models_involved, recommendations, confidences, weights, final_decision, detailed_analyses=None, market_data=None, technical_indicators=None):
        """
        Log a debate session with detailed analyses

        Args:
            models_involved (list): List of models involved
            recommendations (dict): Recommendations from each model
            confidences (dict): Confidence levels from each model
            weights (dict): Weights used for each model
            final_decision (str): Final decision after debate
            detailed_analyses (dict, optional): Detailed analyses from each model
            market_data (dict, optional): Market data used for the debate
            technical_indicators (dict, optional): Technical indicators used for the debate

        Returns:
            str: Path to the saved log file
        """
        self.debate_sessions += 1

        # Create debate session data
        debate_data = {
            "session_id": self.debate_sessions,
            "timestamp": datetime.datetime.now().isoformat(),
            "models_involved": models_involved,
            "recommendations": recommendations,
            "confidences": confidences,
            "weights": weights,
            "final_decision": final_decision,
            "detailed_analyses": detailed_analyses or {},
            "disagreement_analysis": self.analyze_disagreement_reasons(
                models_involved,
                recommendations,
                confidences,
                detailed_analyses or {}
            ),
            "market_data_summary": self._summarize_market_data(market_data) if market_data else {},
            "technical_indicators_summary": self._summarize_technical_indicators(technical_indicators) if technical_indicators else {}
        }

        # Save to file
        debate_dir = self.base_path / "debates"
        debate_dir.mkdir(exist_ok=True)

        file_path = debate_dir / f"debate_session_{self.debate_sessions}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(file_path, 'w') as f:
            json.dump(debate_data, f, indent=4)

        # Log formatted summary
        summary = self.format_debate_summary(debate_data)
        logger.info(f"Debate Session #{self.debate_sessions} Summary:\n{summary}")

        # Save summary to separate file for easier reading
        summary_file = debate_dir / f"debate_summary_{self.debate_sessions}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(summary_file, 'w') as f:
            f.write(summary)

        logger.info(f"Logged debate session {self.debate_sessions} to {file_path}")

        return str(file_path)

    def analyze_disagreement_reasons(self, models_involved, recommendations, confidences, detailed_analyses):
        """
        Analyze reasons for model disagreements

        Args:
            models_involved (list): List of models involved
            recommendations (dict): Recommendations from each model
            confidences (dict): Confidence levels from each model
            detailed_analyses (dict): Detailed analyses from each model

        Returns:
            dict: Analysis of disagreement reasons
        """
        if len(models_involved) < 2:
            return {"status": "no_disagreement", "reason": "Only one model involved"}

        # Check if there's disagreement
        unique_recommendations = set(recommendations.values())
        if len(unique_recommendations) == 1:
            return {"status": "agreement", "recommendation": list(unique_recommendations)[0]}

        # Analyze disagreement
        disagreement_analysis = {
            "status": "disagreement",
            "unique_recommendations": list(unique_recommendations),
            "confidence_discrepancy": {},
            "reasons": []
        }

        # Check confidence discrepancy
        for i, model1 in enumerate(models_involved):
            for model2 in models_involved[i+1:]:
                if model1 in confidences and model2 in confidences:
                    conf_diff = abs(confidences[model1] - confidences[model2])
                    disagreement_analysis["confidence_discrepancy"][f"{model1}_vs_{model2}"] = conf_diff

                    if conf_diff > 20:
                        disagreement_analysis["reasons"].append(
                            f"Large confidence discrepancy between {model1} ({confidences[model1]:.0f}%) and {model2} ({confidences[model2]:.0f}%)"
                        )

        # Analyze detailed analyses for keyword differences
        if detailed_analyses:
            market_trend_keywords = {
                "bullish": 0,
                "bearish": 0,
                "uptrend": 0,
                "downtrend": 0,
                "rising": 0,
                "falling": 0,
                "positive": 0,
                "negative": 0
            }

            technical_indicator_mentions = {
                "ema": 0,
                "rsi": 0,
                "macd": 0,
                "volume": 0,
                "bollinger": 0,
                "fibonacci": 0,
                "support": 0,
                "resistance": 0
            }

            for model, analysis in detailed_analyses.items():
                analysis_lower = analysis.lower()

                # Count market trend keywords
                for keyword in market_trend_keywords:
                    if keyword in analysis_lower:
                        market_trend_keywords[keyword] += 1

                # Count technical indicator mentions
                for indicator in technical_indicator_mentions:
                    if indicator in analysis_lower:
                        technical_indicator_mentions[indicator] += 1

            # Check for disagreement in market trend analysis
            bullish_count = sum([
                market_trend_keywords["bullish"],
                market_trend_keywords["uptrend"],
                market_trend_keywords["rising"],
                market_trend_keywords["positive"]
            ])

            bearish_count = sum([
                market_trend_keywords["bearish"],
                market_trend_keywords["downtrend"],
                market_trend_keywords["falling"],
                market_trend_keywords["negative"]
            ])

            if bullish_count > 0 and bearish_count > 0:
                disagreement_analysis["reasons"].append(
                    f"Disagreement in market trend analysis (bullish mentions: {bullish_count}, bearish mentions: {bearish_count})"
                )

            # Check for different technical indicator focus
            indicator_focus = {}
            for indicator, count in technical_indicator_mentions.items():
                if count > 0:
                    indicator_focus[indicator] = count

            if len(indicator_focus) > 1:
                disagreement_analysis["reasons"].append(
                    f"Different focus on technical indicators: {indicator_focus}"
                )

            disagreement_analysis["keyword_analysis"] = {
                "market_trend_keywords": market_trend_keywords,
                "technical_indicator_mentions": technical_indicator_mentions
            }

        # If no specific reasons found, add a generic reason
        if not disagreement_analysis["reasons"]:
            disagreement_analysis["reasons"].append(
                "General disagreement in market analysis without specific identifiable cause"
            )

        return disagreement_analysis

    def _summarize_market_data(self, market_data):
        """
        Summarize market data for logging

        Args:
            market_data (dict): Market data

        Returns:
            dict: Summarized market data
        """
        if not market_data:
            return {}

        summary = {}

        # Extract key market data points
        if "close_prices" in market_data and len(market_data["close_prices"]) > 0:
            summary["current_price"] = market_data["close_prices"][-1]

            if len(market_data["close_prices"]) > 1:
                price_change = market_data["close_prices"][-1] - market_data["close_prices"][-2]
                price_change_pct = (price_change / market_data["close_prices"][-2]) * 100
                summary["price_change"] = price_change
                summary["price_change_pct"] = price_change_pct

        # Summarize volume
        if "volume" in market_data and len(market_data["volume"]) > 0:
            summary["current_volume"] = market_data["volume"][-1]

            if len(market_data["volume"]) > 20:
                avg_volume = sum(market_data["volume"][-21:-1]) / 20
                volume_change_pct = ((market_data["volume"][-1] - avg_volume) / avg_volume) * 100
                summary["avg_volume"] = avg_volume
                summary["volume_change_pct"] = volume_change_pct

        return summary

    def _summarize_technical_indicators(self, indicators):
        """
        Summarize technical indicators for logging

        Args:
            indicators (dict): Technical indicators

        Returns:
            dict: Summarized technical indicators
        """
        if not indicators:
            return {}

        summary = {}

        # Extract key indicator values
        for indicator in ["ema50", "ema200", "rsi", "macd", "signal", "histogram", "atr"]:
            if indicator in indicators and len(indicators[indicator]) > 0:
                summary[indicator] = indicators[indicator][-1]

        # Add indicator interpretations
        if "ema50" in summary and "ema200" in summary:
            summary["ema_trend"] = "bullish" if summary["ema50"] > summary["ema200"] else "bearish"

        if "rsi" in summary:
            if summary["rsi"] > 70:
                summary["rsi_condition"] = "overbought"
            elif summary["rsi"] < 30:
                summary["rsi_condition"] = "oversold"
            else:
                summary["rsi_condition"] = "neutral"

        if "macd" in summary and "signal" in summary:
            summary["macd_trend"] = "bullish" if summary["macd"] > summary["signal"] else "bearish"

        # Add Bollinger Bands information
        if "upper_band" in indicators and "middle_band" in indicators and "lower_band" in indicators:
            if len(indicators["upper_band"]) > 0 and len(indicators["middle_band"]) > 0 and len(indicators["lower_band"]) > 0:
                summary["upper_band"] = indicators["upper_band"][-1]
                summary["middle_band"] = indicators["middle_band"][-1]
                summary["lower_band"] = indicators["lower_band"][-1]

                if "close_prices" in indicators and len(indicators["close_prices"]) > 0:
                    current_price = indicators["close_prices"][-1]

                    if current_price > summary["upper_band"]:
                        summary["bollinger_position"] = "above_upper"
                    elif current_price < summary["lower_band"]:
                        summary["bollinger_position"] = "below_lower"
                    else:
                        summary["bollinger_position"] = "within_bands"

        return summary

    def format_debate_summary(self, debate_data):
        """
        Format a debate session summary for logging

        Args:
            debate_data (dict): Debate session data

        Returns:
            str: Formatted debate summary
        """
        summary = []
        summary.append(f"Debate Session #{debate_data['session_id']} - {debate_data['timestamp']}")
        summary.append("=" * 80)

        # Add model recommendations
        summary.append("Model Recommendations:")
        for model in debate_data['models_involved']:
            recommendation = debate_data['recommendations'].get(model, "unknown")
            confidence = debate_data['confidences'].get(model, 0)
            weight = debate_data['weights'].get(model, 0)

            summary.append(f"  - {model.upper()} (weight: {weight:.2f}): {recommendation.upper()} with {confidence:.2f} confidence")

        summary.append("")

        # Add disagreement analysis if available
        if 'disagreement_analysis' in debate_data and debate_data['disagreement_analysis'].get('status') == 'disagreement':
            summary.append("Disagreement Analysis:")
            for reason in debate_data['disagreement_analysis'].get('reasons', []):
                summary.append(f"  - {reason}")
            summary.append("")

        # Add market data summary if available
        if debate_data.get('market_data_summary'):
            market_data = debate_data['market_data_summary']
            summary.append("Market Data Summary:")
            if 'current_price' in market_data:
                summary.append(f"  - Current Price: {market_data['current_price']}")
            if 'price_change_pct' in market_data:
                summary.append(f"  - Price Change: {market_data['price_change_pct']:.2f}%")
            if 'volume_change_pct' in market_data:
                summary.append(f"  - Volume Change: {market_data['volume_change_pct']:.2f}%")
            summary.append("")

        # Add technical indicators summary if available
        if debate_data.get('technical_indicators_summary'):
            tech_indicators = debate_data['technical_indicators_summary']
            summary.append("Technical Indicators:")
            if 'ema_trend' in tech_indicators:
                summary.append(f"  - EMA Trend: {tech_indicators['ema_trend'].upper()}")
            if 'rsi' in tech_indicators:
                summary.append(f"  - RSI: {tech_indicators['rsi']:.2f} ({tech_indicators.get('rsi_condition', 'unknown')})")
            if 'macd_trend' in tech_indicators:
                summary.append(f"  - MACD Trend: {tech_indicators['macd_trend'].upper()}")
            if 'bollinger_position' in tech_indicators:
                summary.append(f"  - Bollinger Position: {tech_indicators['bollinger_position'].replace('_', ' ').upper()}")
            summary.append("")

        # Add detailed analyses if available
        if debate_data.get('detailed_analyses'):
            summary.append("Detailed Analyses:")
            for model, analysis in debate_data['detailed_analyses'].items():
                summary.append(f"  {model.upper()}:")
                summary.append(f"    {analysis}")
                summary.append("")

        # Add final decision
        summary.append("=" * 40)
        summary.append(f"Final Decision: {debate_data['final_decision'].upper()}")
        summary.append("=" * 40)

        return "\n".join(summary)
